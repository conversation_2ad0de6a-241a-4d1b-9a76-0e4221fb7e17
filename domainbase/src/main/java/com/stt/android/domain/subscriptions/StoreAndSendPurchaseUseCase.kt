package com.stt.android.domain.subscriptions

import javax.inject.Inject

class StoreAndSendPurchaseUseCase
@Inject constructor(
    private val pendingPurchaseRepository: PendingPurchaseRepository,
    private val playBillingHandler: PlayBillingHandler,
) {
    suspend fun storeAndSend(purchase: DomainPurchase) {
        pendingPurchaseRepository.storeAndSendPurchase(purchase)
        playBillingHandler.acknowledgePurchase(purchase.token!!)
    }
}
