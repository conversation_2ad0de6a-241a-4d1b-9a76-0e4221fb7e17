package com.stt.android.domain.notifications

import kotlinx.coroutines.flow.Flow

interface NotificationsDataSource {
    suspend fun fetchNotifications(): List<DomainNotification>
    suspend fun replaceNotifications(notifications: List<DomainNotification>)
    fun readNotifications(): Flow<List<DomainNotification>>
    fun notificationsCount(): Flow<Int>
    fun unreadNotificationsCount(): Flow<Int>
    suspend fun markNotificationsAsRead(notifications: List<DomainNotification>)
}
