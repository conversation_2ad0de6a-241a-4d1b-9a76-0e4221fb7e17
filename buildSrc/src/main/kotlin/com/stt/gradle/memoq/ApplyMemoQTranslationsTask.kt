package com.stt.gradle.memoq

import org.gradle.api.DefaultTask
import org.gradle.api.file.FileTree
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.TaskAction
import org.gradle.process.ExecOperations
import java.io.ByteArrayOutputStream
import java.io.File
import javax.inject.Inject
import javax.xml.stream.XMLInputFactory
import javax.xml.stream.XMLOutputFactory
import javax.xml.stream.XMLStreamConstants
import java.io.File.separatorChar as SEPARATOR

abstract class ApplyMemoQTranslationsTask
@Inject constructor() : DefaultTask() {
    @get:Input
    abstract val translatedFiles: Property<FileTree>

    @get:Input
    abstract val reference: Property<FileTree>

    @get:Input
    abstract val performGitAdd: Property<Boolean>

    private val xmlInputFactory: XMLInputFactory by lazy {
        XMLInputFactory.newInstance().apply {
            setProperty(XMLInputFactory.IS_COALESCING, false)
            setProperty("http://java.sun.com/xml/stream/properties/report-cdata-event", true)
        }
    }

    private val xmlOutputFactory: XMLOutputFactory by lazy { XMLOutputFactory.newInstance() }

    private val unescapedSingleQuoteRegex = """([^\\])'""".toRegex()

    init {
        description =
            "Integrate new translations from strings.xml exported from memoQ's content connector into individual strings XML files"
        group = "memoQ"
    }

    @TaskAction
    fun applyTranslations() {
        for (ref in reference.get().files) {
            for (translated in translatedFiles.get().files) {
                // Reference file 'ref' is an English strings.xml in the project, such as
                // - workouts/domain/src/main/res/values/strings.xml
                //
                // Translated file is the merged file with all the translations for one language
                // downloaded from memoQ, such as
                // - build/memoQ/translated/dut-NL-strings.xml
                //
                // Output file 'outputFile' is the file where the translations will be written in
                // a language specific strings.xml file, such as
                // - workouts/domain/src/main/res/values-nl/strings.xml
                //
                // The generated output file will follow the structure of the reference file
                // exactly, but the actual translations will be replaced with content from the
                // 'translated' file.

                // memoQ uses 3-letter language codes often with a region suffix. Use the
                // ISO3_LANGUAGE_MAP mapping table to convert to Android convention.
                val languageCode =
                    ISO3_LANGUAGE_MAP[translated.nameWithoutExtension.removeSuffix("-strings")]
                        ?: throw IllegalArgumentException("Unable to deduce language code for $translated")

                val outputFile = File(ref.path.replace("${SEPARATOR}values${SEPARATOR}", "${SEPARATOR}values-$languageCode${SEPARATOR}"))

                val translatedXmlSnippets = loadTranslations(translated)

                // TODO: Remove this temporary hack when we get proper plurals resources from memoQ
                val plurals = if (outputFile.exists()) {
                    loadPluralsSnippets(outputFile)
                } else {
                    emptyMap()
                }
                val translatedXmlSnippetsWithOriginalPlurals = translatedXmlSnippets + plurals

                applyTranslations(
                    reference = ref,
                    translatedXmlSnippets = translatedXmlSnippetsWithOriginalPlurals,
                    output = outputFile
                )

                if (performGitAdd.get()) {
                    services.get(ExecOperations::class.java).exec {
                        it.commandLine("git", "add", "--verbose", outputFile.path)
                    }
                }
            }
        }
    }

    private fun loadTranslations(
        translated: File
    ): Map<StringKey, String> = translated.inputStream().use { stream ->
        buildMap {
            val reader = xmlInputFactory.createXMLStreamReader(stream)
            while (reader.hasNext()) {
                reader.next()
                if (reader.isStringResourceElement()) {
                    val type = reader.localName
                    val attributes = reader.attributes()
                    val name: String? = attributes["name"]

                    val outStream = ByteArrayOutputStream()
                    val writer = xmlOutputFactory.createXMLStreamWriter(outStream, "UTF-8")
                    writer.writeStartElement(reader.localName)
                    writer.writeAttributes(attributes)

                    var nestedTagCount = 0
                    while (reader.hasNext()) {
                        reader.next()
                        when (reader.eventType) {
                            XMLStreamConstants.START_ELEMENT -> {
                                if (reader.prefix.isNullOrBlank()) {
                                    writer.writeStartElement(reader.localName)
                                } else {
                                    writer.writeStartElement(reader.prefix, reader.localName, "")
                                }

                                writer.writeAttributes(reader.attributes())
                                nestedTagCount++
                            }

                            XMLStreamConstants.CHARACTERS -> writer.writeCharacters(reader.text)
                            // TODO: It would be better to fix the memoQ XML escaping script, but for now use a regex here to escape single quotes in CDATA
                            XMLStreamConstants.CDATA -> writer.writeCData(reader.text.replace(unescapedSingleQuoteRegex, "$1\\\\'"))
                            XMLStreamConstants.END_ELEMENT -> {
                                writer.writeEndElement()
                                nestedTagCount--
                                if (nestedTagCount < 0) {
                                    reader.next()
                                    break
                                }
                            }

                            XMLStreamConstants.COMMENT -> writer.writeComment(reader.text)

                            else -> throw IllegalArgumentException("Unhandled event type ${reader.eventType}")
                        }
                    }

                    if (name != null && attributes["translatable"] != "false") {
                        // The XML writer the writes to the byte buffer has a different indent
                        // than the final file due to the missing <resources> tag. Adjust indent
                        // here manually just to make it match what we expect for <plurals>, for
                        // example.
                        val text = outStream.toString("UTF-8")
                            .replace("\n    <", "\n        <")
                            .replace("\n  <", "\n    <")
                        put(StringKey(id = name, type = type), text)
                    }
                }
            }
        }
    }

    // TODO: Remove this temporary hack when we get proper plurals resources from memoQ
    private fun loadPluralsSnippets(
        file: File
    ): Map<StringKey, String> = file.inputStream().use { stream ->
        buildMap {
            val reader = xmlInputFactory.createXMLStreamReader(stream)
            while (reader.hasNext()) {
                reader.next()
                if (reader.isStringResourceElement() && reader.localName == "plurals") {
                    val type = reader.localName
                    val attributes = reader.attributes()
                    val name: String? = attributes["name"]

                    val outStream = ByteArrayOutputStream()
                    val writer = xmlOutputFactory.createXMLStreamWriter(outStream, "UTF-8")
                    writer.writeStartElement(reader.localName)
                    writer.writeAttributes(attributes)

                    var nestedTagCount = 0
                    while (reader.hasNext()) {
                        reader.next()
                        when (reader.eventType) {
                            XMLStreamConstants.START_ELEMENT -> {
                                if (reader.prefix.isNullOrBlank()) {
                                    writer.writeStartElement(reader.localName)
                                } else {
                                    writer.writeStartElement(reader.prefix, reader.localName, "")
                                }

                                writer.writeAttributes(reader.attributes())
                                nestedTagCount++
                            }

                            XMLStreamConstants.CHARACTERS -> writer.writeCharacters(reader.text)
                            // TODO: It would be better to fix the memoQ XML escaping script, but for now use a regex here to escape single quotes in CDATA
                            XMLStreamConstants.CDATA -> writer.writeCData(reader.text.replace(unescapedSingleQuoteRegex, "$1\\\\'"))
                            XMLStreamConstants.END_ELEMENT -> {
                                writer.writeEndElement()
                                nestedTagCount--
                                if (nestedTagCount < 0) {
                                    reader.next()
                                    break
                                }
                            }

                            XMLStreamConstants.COMMENT -> writer.writeComment(reader.text)

                            else -> throw IllegalArgumentException("Unhandled event type ${reader.eventType}")
                        }
                    }

                    if (name != null && attributes["translatable"] != "false") {
                        // The XML writer the writes to the byte buffer has a different indent
                        // than the final file due to the missing <resources> tag. Adjust indent
                        // here manually just to make it match what we expect for <plurals>, for
                        // example.
                        val text = outStream.toString("UTF-8")
                            .replace("\n  <", "\n    <")
                        put(StringKey(id = name, type = type), text)
                    }
                }
            }
        }
    }

    private fun applyTranslations(
        reference: File,
        translatedXmlSnippets: Map<StringKey, String>,
        output: File
    ) {
        fun findSnippet(key: StringKey?): String? {
            if (key == null) {
                return null
            }

            val name = key.id

            val stringIDsToTry = mutableListOf(key.id)
            if (reference.path.contains("${SEPARATOR}china") || reference.path.contains("${SEPARATOR}suuntoChina")) {
                stringIDsToTry.add(0, "${name}_china")
            }

            if (reference.path.contains("${SEPARATOR}sportstracker${SEPARATOR}")) {
                stringIDsToTry.add(0, "${name}_sportstracker")
            }

            if (reference.path.contains("${SEPARATOR}suunto${SEPARATOR}") || reference.path.contains("${SEPARATOR}suuntoPlaystore")) {
                stringIDsToTry.add(0, "${name}_suunto")
            }

            for (stringID in stringIDsToTry) {
                val keyToTry = StringKey(id = stringID, type = key.type)
                if (translatedXmlSnippets.contains(keyToTry)) {
                    // The merged, translated strings.xml will have the _suunto, _china or _sportstracker
                    // suffix for strings, but those should be dropped when applying the translations to
                    // the actual strings.xml files in modules.
                    return translatedXmlSnippets[keyToTry]!!.replace("\"$stringID\"", "\"$name\"")
                }
            }

            println("Missing translation for $key (${output.path}")
            return null
        }

        reference.inputStream().use { inputStream ->
            val reader = xmlInputFactory.createXMLStreamReader(inputStream)

            // Build the language specific strings XML by following the structure of the English
            // strings.xml. The output is constructed directly using a buffered writer instead of
            // an XML writer since it consists of XML snippets which may contain whitespace for
            // indentation, nested tags, CDATA or other content.
            val outputData = ByteArrayOutputStream()
            outputData.bufferedWriter(Charsets.UTF_8).use { outputStream ->
                outputStream.write("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")

                while (reader.hasNext()) {
                    reader.next()
                    // <resources xmlns:tools="...">
                    if (reader.isResourcesElement()) {
                        val namespaceDefinition = reader.flattenedNamespaceDefinition()
                        if (namespaceDefinition.isNotBlank()) {
                            outputStream.write("<resources $namespaceDefinition>")
                        } else {
                            outputStream.write("<resources>")
                        }
                    }

                    // <string> or <plurals>
                    if (reader.isStringResourceElement()) {
                        val type = reader.localName
                        val attributes = reader.attributes()
                        val name: String? = attributes["name"]

                        val key = name?.let { StringKey(id = name, type = type) }

                        if (attributes["translatable"] != "false") {
                            // Try to find matching translation. Try with _china, _suunto, or
                            // _sportstracker postfix depending on the context.
                            findSnippet(key)?.let { translatedSnippet ->
                                outputStream.write(translatedSnippet)
                            }
                        }

                        reader.skipUntilEndOfCurrentTag()
                    }

                    if (reader.eventType == XMLStreamConstants.COMMENT) {
                        outputStream.write("<!--${reader.text}-->")
                    }

                    if (reader.eventType == XMLStreamConstants.CHARACTERS) {
                        outputStream.write(reader.text)
                    }
                }

                outputStream.write("</resources>\n")
            }

            if (!output.parentFile.exists()) {
                output.parentFile.mkdirs()
            }

            output.outputStream().use { outputStream ->
                val data = outputData
                    .toString("UTF-8")
                    .replace(" +\n".toRegex(), "\n") // Trim trailing whitespace
                    .replace("\n{2,}".toRegex(), "\n\n") // Limit number of consecutive empty lines to 1
                    .toByteArray()

                outputStream.write(data)
                println("Wrote changes to ${output.path}")
            }
        }
    }

    companion object {
        // Key: How the language is represented in memoQ
        // Value: How the language is represented in Android
        val ISO3_LANGUAGE_MAP = mapOf(
            "swe-SE" to "sv",
            "pol" to "pl",
            "fin" to "fi",
            "dan" to "da",
            "tur" to "tr",
            "nnb" to "nb",
            "zho-CN" to "zh-rCN",
            "jpn" to "ja",
            "cze" to "cs",
            "ita-IT" to "it",
            "ger-DE" to "de",
            "rus" to "ru",
            "dut-NL" to "nl",
            "fre-FR" to "fr",
            "tha" to "th",
            "kor" to "ko",
            "spa-ES" to "es",
            "por-PT" to "pt",
            "vie" to "vi",
            "zho-TW" to "zh-rTW",
        )
    }
}
