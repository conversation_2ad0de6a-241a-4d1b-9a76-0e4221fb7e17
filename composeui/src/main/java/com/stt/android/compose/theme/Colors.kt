package com.stt.android.compose.theme

import androidx.compose.material.Colors
import androidx.compose.material.SwitchColors
import androidx.compose.material.SwitchDefaults
import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.material3.SwitchColors as M3SwitchColors
import androidx.compose.material3.SwitchDefaults as M3SwitchDefaults

private data class ExtraPalette(
    val lightGrey: Color = Color(0xFFE9ECEE),
    val mediumGrey: Color = Color(0xFFACAFB6),
    val darkGreySt: Color = Color(0xFF7D7D7D),
    val activitySleep: Color = Color(0xFF9E6CEC),
    val activitySteps: Color = Color(0xFF66B6FF),
    val activityExercise: Color = Color(0xFF6CD8AB),
    val activityRecovery: Color = Color(0xFF9CE157),
    val activityCalories: Color = Color(0xFFEFB749),
    val activityCycling: Color = Color(0xFFFF7C3B),
    val activityProgress: Color = Color(0xFFDB315A),
    val nearWhite: Color = Color(0xFFF1F1F1),
    val veryLightGray: Color = Color(0xFFF0F0F0),
    val cloudyGrey: Color = Color(0xFFD8DBDD),
    val nearBlack: Color = Color(0xFF303030),
    val darkestGrey: Color = Color(0xFF3D3D3D),
    val darkGrey: Color = Color(0xFF7E8084),
    val confirmation: Color = Color(0xFF21CE56),
    val attention: Color = Color(0xFFFFBD00),
    val heartRateZone5: Color = Color(0xFFDE0101),
    val heartRateZone4: Color = Color(0xFFFF7C02),
    val heartRateZone3: Color = Color(0xFFFFC221),
    val heartRateZone2: Color = Color(0xFF01AD3A),
    val heartRateZone1: Color = Color(0xFF4DBEE5),
    val heartRateChart:Color =  Color(0xFFFF3333),
    val vo2Max: Color = Color(0xFFDE0101),
    val anaerobic: Color = Color(0xFFFFC221),
    val aerobic: Color = Color(0xFF01AD3A),
    val suuntoCoach: Color = Color(0xFFFF7C3B),
    val lightGreen: Color = Color(0xFFD3F5DD),
    val vo2MaxZone1: Color = Color(0xFFDF2323),
    val vo2MaxZone2: Color = Color(0xFFFFBD00),
    val vo2MaxZone3: Color = Color(0xFF1AA243),
)

private val LocalExtraPalette = staticCompositionLocalOf { ExtraPalette() }

val Colors.dividerColor: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGrey


val ColorScheme.dividerColor: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGrey

val Colors.disabledColor: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.mediumGrey

val ColorScheme.disabledColor: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.mediumGrey

val Colors.activitySleep: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activitySleep

val ColorScheme.activitySleep: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activitySleep

val Colors.activitySteps: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activitySteps

val ColorScheme.activitySteps: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activitySteps

val Colors.activityExercise: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityExercise

val ColorScheme.activityExercise: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityExercise

val Colors.activityRecovery: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityRecovery

val ColorScheme.activityRecovery: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityRecovery

val Colors.activityCalories: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityCalories

val ColorScheme.activityCalories: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityCalories

val Colors.activityCycling: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityCycling

val ColorScheme.activityCycling: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityCycling

val Colors.activityProgress: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityProgress

val ColorScheme.activityProgress: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.activityProgress

val Colors.darkGreyText: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkGreySt

val ColorScheme.darkGreyText: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkGreySt

val Colors.switchColors: SwitchColors
    @Composable
    get() = SwitchDefaults.colors(
        checkedThumbColor = primary,
        checkedTrackColor = primary,
        uncheckedThumbColor = background
    )

val ColorScheme.switchColors: M3SwitchColors
    @Composable
    get() = M3SwitchDefaults.colors(
        checkedThumbColor = primary,
        checkedTrackColor = primary,
        uncheckedThumbColor = background
    )

val Colors.lightGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGrey

val ColorScheme.lightGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGrey

val Colors.nearWhite: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.nearWhite

val ColorScheme.nearWhite: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.nearWhite

val Colors.veryLightGray: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.veryLightGray

val ColorScheme.veryLightGray: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.veryLightGray

val Colors.mediumGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.mediumGrey

val ColorScheme.mediumGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.mediumGrey

val Colors.cloudyGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.cloudyGrey

val ColorScheme.cloudyGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.cloudyGrey

val Colors.nearBlack: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.nearBlack

val ColorScheme.nearBlack: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.nearBlack

val Colors.darkGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkGrey

val ColorScheme.darkGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkGrey

val Colors.darkestGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkestGrey

val ColorScheme.darkestGrey: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.darkestGrey

val Colors.confirmation: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.confirmation

val ColorScheme.confirmation: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.confirmation

val Colors.attention: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.attention

val ColorScheme.attention: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.attention

val Colors.heartRateZone5: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone5

val ColorScheme.heartRateZone5: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone5

val Colors.heartRateZone4: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone4

val ColorScheme.heartRateZone4: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone4

val Colors.heartRateZone3: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone3

val ColorScheme.heartRateZone3: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone3

val Colors.heartRateZone2: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone2

val ColorScheme.heartRateZone2: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone2

val Colors.heartRateZone1: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone1

val ColorScheme.heartRateZone1: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateZone1

val ColorScheme.heartRateChart:Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.heartRateChart

val Colors.vo2Max: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2Max

val ColorScheme.vo2Max: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2Max

val Colors.anaerobic: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.anaerobic

val ColorScheme.anaerobic: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.anaerobic

val Colors.aerobic: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.aerobic

val ColorScheme.aerobic: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.aerobic

val Colors.suuntoCoach: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.suuntoCoach

val ColorScheme.suuntoCoach: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.suuntoCoach

val Colors.lightGreen: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGreen

val ColorScheme.lightGreen: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.lightGreen

val Colors.vo2MaxZone1: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone1

val ColorScheme.vo2MaxZone1: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone1

val Colors.vo2MaxZone2: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone2

val ColorScheme.vo2MaxZone2: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone2

val Colors.vo2MaxZone3: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone3

val ColorScheme.vo2MaxZone3: Color
    @Composable
    @ReadOnlyComposable
    get() = LocalExtraPalette.current.vo2MaxZone3
