package com.stt.android.compose.widgets

import androidx.annotation.DrawableRes
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FloatingActionButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R
import kotlinx.coroutines.delay

// See Round Buttons/FAB_Button in Components specification in Figma
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ExpandingFloatingActionButton(
    @DrawableRes drawableResource: Int,
    text: String,
    expanded: Boolean,
    enabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    contentColor: Color = MaterialTheme.colors.onSurface,
    backgroundColor: Color = MaterialTheme.colors.surface,
    iconSize: Dp = MaterialTheme.iconSizes.small,
) {
    val layoutDirection = LocalLayoutDirection.current
    val iconSizeDiff = iconSize - MaterialTheme.iconSizes.small
    val textPadding = PaddingValues(
        start = MaterialTheme.spacing.xxsmall - iconSizeDiff / 2,
        end = MaterialTheme.spacing.xxsmall
    )

    MeasureSingleLineTextWidth(
        text = text,
        style = MaterialTheme.typography.bodyLarge,
        modifier = modifier,
    ) { textWidth ->
        // Follow a similar approach to androidx.compose.material.FloatingActionButton but
        // implement support for disabled state.
        val interactionSource = remember { MutableInteractionSource() }

        val colorState = animateColorAsState(
            targetValue = if (enabled) {
                backgroundColor
            } else {
                MaterialTheme.colors.cloudyGrey
            }
        )

        val contentColorState = animateColorAsState(
            targetValue = if (enabled) {
                contentColor
            } else {
                backgroundColor
            }
        )

        Surface(
            color = colorState.value,
            enabled = enabled,
            shape = MaterialTheme.shapes.small.copy(CornerSize(percent = 50)),
            contentColor = contentColorState.value,
            interactionSource = interactionSource,
            elevation = if (enabled) {
                FloatingActionButtonDefaults.elevation().elevation(interactionSource).value
            } else {
                0.dp
            },
            onClick = {
                if (enabled) {
                    onClick()
                }
            },
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .defaultMinSize(minWidth = FabSize, minHeight = FabSize)
                    .padding(horizontal = MaterialTheme.spacing.small - iconSizeDiff / 2)
            ) {
                Icon(
                    painter = painterResource(id = drawableResource),
                    contentDescription = null,
                    modifier = Modifier.size(iconSize)
                )

                // Animate text width and alpha based on expanded state
                val alpha by animateFloatAsState(
                    targetValue = if (expanded) 1f else 0f,
                    animationSpec = spring(stiffness = Spring.StiffnessMediumLow)
                )

                val width by animateDpAsState(
                    targetValue = if (expanded) {
                        textWidth + textPadding.calculateStartPadding(layoutDirection) + textPadding.calculateEndPadding(
                            layoutDirection
                        )
                    } else {
                        0.dp
                    },
                    animationSpec = spring(stiffness = Spring.StiffnessMediumLow)
                )

                Text(
                    text = text,
                    overflow = TextOverflow.Clip,
                    style = MaterialTheme.typography.body,
                    softWrap = false,
                    maxLines = 1,
                    modifier = Modifier
                        .width(width)
                        .padding(textPadding)
                        .graphicsLayer { this.alpha = alpha }
                )
            }
        }
    }
}

private val FabSize = 40.dp // private in androidx.compose.material.FloatingActionButton

@Composable
private fun MeasureSingleLineTextWidth(
    text: String,
    style: TextStyle,
    modifier: Modifier = Modifier,
    content: @Composable (textWidth: Dp) -> Unit
) {
    SubcomposeLayout(modifier = modifier) { constraints ->
        val measuredTextWidth = subcompose(
            slotId = "textForMeasurement",
            content = { Text(text = text, style = style) }
        )
            .first()
            .measure(Constraints()) // No limit on size
            .width
            .toDp()

        val contentPlaceable = subcompose(
            slotId = "content",
            content = { content(measuredTextWidth) }
        )
            .first()
            .measure(constraints) // Respect given constraints

        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.place(0, 0)
        }
    }
}

@Preview
@Composable
private fun ExpandingFloatingActionButtonPreview() {
    AppTheme {
        Surface {
            var expanded by remember { mutableStateOf(false) }

            Box(
                contentAlignment = Alignment.CenterEnd,
                modifier = Modifier
                    .widthIn(min = 320.dp)
                    .padding(MaterialTheme.spacing.medium)
            ) {
                // Use interactive preview to test animation
                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.ic_activity_bowling,
                    text = "Go bowling",
                    expanded = expanded,
                    enabled = true,
                    onClick = { expanded = !expanded }
                )
            }
        }
    }
}

@Preview
@Composable
private fun DisabledExpandingFloatingActionButtonPreview() {
    AppTheme {
        Surface {
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                modifier = Modifier
                    .widthIn(min = 320.dp)
                    .padding(MaterialTheme.spacing.medium)
            ) {
                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.ic_activity_bowling,
                    text = "Disabled",
                    expanded = false,
                    enabled = false,
                    onClick = { /* Not going to be called */ }
                )

                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.ic_activity_bowling,
                    text = "Disabled",
                    expanded = true,
                    enabled = false,
                    onClick = { /* Not going to be called */ }
                )
            }
        }
    }
}

@Preview
@Composable
private fun DisablingTransitionPreview() {
    AppTheme {
        Surface {
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                modifier = Modifier
                    .widthIn(min = 320.dp)
                    .padding(MaterialTheme.spacing.medium)
            ) {
                var enabled by remember { mutableStateOf(true) }

                LaunchedEffect(Unit) {
                    while (true) {
                        delay(1500L)
                        enabled = !enabled
                    }
                }

                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.ic_search_fill,
                    text = "Disabled",
                    expanded = false,
                    enabled = enabled,
                    onClick = { /* Not going to be called */ },
                    backgroundColor = MaterialTheme.colors.secondary,
                    contentColor = MaterialTheme.colors.surface,
                    iconSize = MaterialTheme.iconSizes.small,
                )

                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.ic_search_fill,
                    text = "Disabled",
                    expanded = true,
                    enabled = enabled,
                    onClick = { /* Not going to be called */ },
                    backgroundColor = MaterialTheme.colors.secondary,
                    contentColor = MaterialTheme.colors.surface,
                    iconSize = MaterialTheme.iconSizes.small,
                )
            }
        }
    }
}
