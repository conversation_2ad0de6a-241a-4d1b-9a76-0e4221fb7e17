package com.stt.android.compose.markdown

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import org.commonmark.node.Node
import org.commonmark.parser.Parser
import java.util.Locale

private val EXPANDABLE_MARKDOWN_COLLAPSED_HEIGHT = 80.dp

@Composable
fun ExpandableMarkdownDocument(
    rootNode: Node,
    initiallyExpanded: Boolean,
    showMoreText: String,
    showLessText: String,
    modifier: Modifier = Modifier,
    defaultTextStyle: TextStyle = MaterialTheme.typography.bodyLarge,
    hyperlinkHandler: ((String) -> Unit)? = null
) {
    var expanded by rememberSaveable { mutableStateOf(initiallyExpanded) }

    val backgroundColor = MaterialTheme.colors.surface

    Column(
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier.then(
                if (expanded) {
                    Modifier
                } else {
                    Modifier
                        .heightIn(max = EXPANDABLE_MARKDOWN_COLLAPSED_HEIGHT)
                        .drawWithContent {
                            drawContent()

                            drawRect(
                                brush = Brush.verticalGradient(
                                    colors = listOf(
                                        backgroundColor.copy(alpha = 0f),
                                        backgroundColor.copy(alpha = 1.0f),
                                    ),
                                    startY = size.height * 0.6f
                                )
                            )
                        }
                }
                    .animateContentSize()
            )
        ) {
            MarkdownDocument(
                rootNode = rootNode,
                defaultTextStyle = defaultTextStyle,
                hyperlinkHandler = hyperlinkHandler
            )
        }

        TextButton(
            onClick = { expanded = !expanded },
            modifier = Modifier
                .offset(x = -MaterialTheme.spacing.small) // Compensate for internal button padding
        ) {
            val text = if (expanded) showLessText else showMoreText
            Text(text.uppercase(Locale.getDefault()))
        }
    }
}

@Preview
@Composable
private fun ExpandableMarkdownDocumentPreview() {
    val doc = remember {
        Parser.Builder().build().parse(RunningGuideMarkdown)
    }

    AppTheme {
        Surface {
            ExpandableMarkdownDocument(
                rootNode = doc,
                initiallyExpanded = false,
                showMoreText = "SHOW MORE",
                showLessText = "SHOW LESS",
            )
        }
    }
}
