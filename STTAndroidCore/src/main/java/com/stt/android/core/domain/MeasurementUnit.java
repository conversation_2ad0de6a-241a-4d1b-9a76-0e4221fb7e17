package com.stt.android.core.domain;

import androidx.annotation.StringRes;
import com.stt.android.core.R;

public enum MeasurementUnit {
    METRIC(R.string.km, R.string.km_h, R.string.per_km),
    IMPERIAL(R.string.mile, R.string.mph, R.string.per_mi);

    @StringRes
    public final int distanceUnitResId;
    @StringRes
    public final int speedUnitResId;
    @StringRes
    public final int paceUnitResId;

    MeasurementUnit(@StringRes int distanceUnitId, @StringRes int speedUnitId, @StringRes int
        paceUnitId) {
        this.distanceUnitResId = distanceUnitId;
        this.speedUnitResId = speedUnitId;
        this.paceUnitResId = paceUnitId;
    }
}
