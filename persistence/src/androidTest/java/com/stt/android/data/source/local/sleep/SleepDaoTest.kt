package com.stt.android.data.source.local.sleep

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.MediumTest
import androidx.test.platform.app.InstrumentationRegistry
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.TimeUtils
import com.stt.android.data.source.local.RoomAppDatabaseTest
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import timber.log.Timber

@MediumTest
@RunWith(AndroidJUnit4::class)
internal class SleepDaoTest : RoomAppDatabaseTest() {

    private val context = InstrumentationRegistry.getInstrumentation().context

    @Test
    fun insertAndGetSleepByRange() = runTest {
        val fromDate = 1516924800L
        val toDate = 1517356800L
        val serial1 = "2342342"
        val serial2 = "2342343"

        val sleepList = listOf(
            LocalSleepSegment(
                serial1,
                1517356800L,
                432f
            ), // 31/01/2018 00:00:00
            LocalSleepSegment(
                serial2,
                1517270400L,
                200f
            ), // 30/01/2018 00:00:00
            LocalSleepSegment(
                serial1,
                1517184000L,
                550f
            ), // 29/01/2018 00:00:00
            LocalSleepSegment(
                serial2,
                1516924800L,
                134f
            ) // 26/01/2018 00:00:00
        )

        db.sleepSegmentsDao().insertSleepSegments(sleepList)

        Timber.d("all sleep segments: ${db.sleepSegmentsDao().queryAll()}")

        val actual = db.sleepSegmentsDao().fetchSleepSegments(fromDate, toDate)
            .first()
        assertThat(actual).isEqualTo(sleepList.reversed())
    }

    @Test
    fun insertAndGetSleepUntilTimestamp() = runTest {
        val dayTimeSeconds = 86_400L
        val days = 10
        val firstDayTimestampSeconds = 1517356800L
        val list = mutableListOf<LocalSleepSegment>()
        for (i in 0..days) {
            val timestampSeconds = firstDayTimestampSeconds + dayTimeSeconds * i
            list.add(
                LocalSleepSegment(
                    serial = "2342342",
                    timestampSeconds = timestampSeconds,
                    durationSeconds = 100f * i,
                    syncedStatus = LocalSleepSegment.STATUS_NOT_SYNCED,
                    timeISO8601 = TimeUtils.epochToLocalZonedDateTime(timestampSeconds * 1000).withFixedOffsetZone()
                )
            )
        }

        db.sleepSegmentsDao().insertSleepSegments(list)

        val actual = db.sleepSegmentsDao().fetchSleepSegmentsFrom(firstDayTimestampSeconds) // the last entry in the table
            .first()

        assertThat(actual.sortedBy { it.timestampSeconds }).isEqualTo(list.toList())
    }

    @Test
    fun insertAndGetSleepUntilTimestampIncludesNewEntries() = runTest {
        val dayTimeSeconds = 86_400L
        val days = 10
        val startDayTimestampSeconds = 1517356800L
        val serial = "2342342"
        val list = mutableListOf<LocalSleepSegment>()
        for (i in 0 until days) {
            val timestampSeconds = startDayTimestampSeconds + dayTimeSeconds * i
            list.add(
                LocalSleepSegment(
                    serial = serial,
                    timestampSeconds = timestampSeconds,
                    durationSeconds = 100f * i,
                    syncedStatus = LocalSleepSegment.STATUS_NOT_SYNCED,
                    timeISO8601 = TimeUtils.epochToLocalZonedDateTime(timestampSeconds * 1000)
                )
            )
        }

        db.sleepSegmentsDao().insertSleepSegments(list)

        assertEquals(
            listOf(days),
            db.sleepSegmentsDao().fetchSleepSegmentsFrom(startDayTimestampSeconds).take(1).map { it.size }.toList()
        )

        // Add entry to the top of the table
        val timestampSeconds = startDayTimestampSeconds + dayTimeSeconds * days + 1
        db.sleepSegmentsDao().insertSleepSegments(
            listOf(
                LocalSleepSegment(
                    serial = serial,
                    timestampSeconds = timestampSeconds,
                    durationSeconds = 234f,
                    syncedStatus = LocalSleepSegment.STATUS_NOT_SYNCED,
                    timeISO8601 = TimeUtils.epochToLocalZonedDateTime(timestampSeconds * 1000)
                )
            )
        )

        assertEquals(
            listOf(days + 1),
            db.sleepSegmentsDao().fetchSleepSegmentsFrom(startDayTimestampSeconds).take(1).map { it.size }.toList()
        )
    }

    @Test
    fun testIsSleepDataAvailable() = runTest {
        val sleepDataNotAvailable = db.sleepSegmentsDao().sleepDataCount(0) > 0
        assertThat(sleepDataNotAvailable).isEqualTo(false)
        db.sleepSegmentsDao().insertSleepSegments(listOf(LocalSleepSegment("abc", 123L, 12f)))
        val sleepDataAvailable = db.sleepSegmentsDao().sleepDataCount(100) > 0
        assertThat(sleepDataAvailable).isEqualTo(true)
    }
}
