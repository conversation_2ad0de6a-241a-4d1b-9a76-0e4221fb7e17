package com.stt.android.data.source.local.workout

import androidx.room.ColumnInfo
import androidx.room.TypeConverters
import com.stt.android.data.source.local.workout.tss.LocalTSS
import com.stt.android.data.source.local.workout.tss.LocalTSSConverter

@TypeConverters(LocalTSSConverter::class)
data class LocalWorkoutTSSSummary(
    @ColumnInfo(name = "id") val id: Int,
    @ColumnInfo(name = "key") val key: String?,
    @ColumnInfo(name = "username") val username: String,
    @ColumnInfo(name = "startTime") val startTime: Long,
    @ColumnInfo(name = "stopTime") val endTime: Long,
    @ColumnInfo(name = "tss") val tss: LocalTSS,
    @ColumnInfo(name = "activityId") val activityId: Int
)
