package com.stt.android.data.source.local

import androidx.annotation.VisibleForTesting
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.stt.android.data.source.local.ranking.LocalRanking
import kotlinx.coroutines.flow.Flow

@Dao
abstract class RankingDao {
    @VisibleForTesting
    @Query("SELECT * FROM $TABLE_RANKINGS")
    internal abstract fun selectAll(): List<LocalRanking>

    @Query("SELECT * FROM $TABLE_RANKINGS WHERE :workoutKey = ${LocalRanking.WORKOUT_KEY}")
    abstract fun findAllByWorkoutKey(workoutKey: String): Flow<List<LocalRanking>>

    @Query("DELETE FROM $TABLE_RANKINGS WHERE :workoutKey = ${LocalRanking.WORKOUT_KEY}")
    abstract suspend fun deleteByWorkoutKey(workoutKey: String)

    @Insert
    abstract suspend fun insert(rankings: List<LocalRanking>)

    @Query(
        """
        DELETE
        FROM $TABLE_RANKINGS
    """
    )
    abstract fun deleteAll()
}
