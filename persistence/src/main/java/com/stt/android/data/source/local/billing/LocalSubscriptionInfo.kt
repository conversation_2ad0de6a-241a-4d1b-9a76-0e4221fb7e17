package com.stt.android.data.source.local.billing

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.stt.android.data.source.local.TABLE_SUBSCRIPTION_INFO

@Entity(tableName = TABLE_SUBSCRIPTION_INFO)
data class LocalSubscriptionInfo(
    @ColumnInfo(name = ID) @PrimaryKey val id: String,
    @ColumnInfo(name = TYPE) val type: SubscriptionType?,
    @ColumnInfo(name = LENGTH) val length: SubscriptionLength?,
    @ColumnInfo(name = AUTO_RENEW) val autoRenew: Boolean,
    @ColumnInfo(name = LOCALIZED_PRICE) val localizedPrice: String?,
    @ColumnInfo(name = FETCHED_TIMESTAMP) val fetchedTimestamp: Long,
    @ColumnInfo(name = FREE_TRIAL_PERIOD_SECONDS) val freeTrialPeriodSeconds: Long?
) {

    enum class SubscriptionType {
        ACTIVE,
        IN_GRACE_PERIOD,
        ON_HOLD,
        UNKNOWN
    }

    enum class SubscriptionLength {
        MONTHLY,
        YEARLY,
        UNKNOWN
    }

    companion object {
        const val ID = "id"
        const val TYPE = "type"
        const val LENGTH = "length"
        const val AUTO_RENEW = "autoRenew"
        const val LOCALIZED_PRICE = "localizedPrice"
        const val FETCHED_TIMESTAMP = "fetchedTimestamp"
        const val FREE_TRIAL_PERIOD_SECONDS = "freeTrialPeriodSeconds"
    }
}
