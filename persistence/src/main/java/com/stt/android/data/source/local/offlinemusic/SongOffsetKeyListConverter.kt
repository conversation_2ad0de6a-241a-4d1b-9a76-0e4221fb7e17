package com.stt.android.data.source.local.offlinemusic

import androidx.room.TypeConverter
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.moshi.buildBasicMoshi
import timber.log.Timber

class SongOffsetKeyListConverter {
    private val moshi: Moshi = buildBasicMoshi()

    private val adapter: JsonAdapter<List<LocalSongOffsetKey>> =
        moshi.adapter(Types.newParameterizedType(List::class.java, LocalSongOffsetKey::class.java))

    @TypeConverter
    fun fromStringToLocalSongOffsetKeys(data: String): List<LocalSongOffsetKey>? {
        return try {
            adapter.fromJson(data)
        } catch (e: Exception) {
            Timber.w(e)
            null
        }
    }

    @TypeConverter
    fun toLocalSongOffsetKeysString(keys: List<LocalSongOffsetKey>): String {
        return adapter.toJson(keys)
    }
}
