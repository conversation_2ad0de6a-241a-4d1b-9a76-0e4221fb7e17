package com.stt.android.data.source.local.goaldefinition

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_GOAL_DEFINITION
import kotlinx.coroutines.flow.Flow

@Dao
abstract class GoalDefinitionDao {
    @Query("SELECT * FROM goal_definitions WHERE userName = :username ORDER BY created DESC LIMIT 1")
    abstract fun findByUsername(username: String): Flow<List<LocalGoalDefinition>>

    @Query("SELECT * FROM goal_definitions")
    abstract fun fetchAll(): Flow<List<LocalGoalDefinition>>

    @Insert
    abstract suspend fun insert(goalDefinition: LocalGoalDefinition): Long

    @Insert
    abstract fun insert(goalDefinition: List<LocalGoalDefinition>)

    @Update
    abstract suspend fun update(goalDefinition: LocalGoalDefinition)

    @Transaction
    open suspend fun upsert(goalDefinition: LocalGoalDefinition): Long {
        return if (goalDefinition.id == 0L) {
            insert(goalDefinition)
        } else {
            update(goalDefinition)
            goalDefinition.id
        }
    }

    @Query("DELETE FROM $TABLE_GOAL_DEFINITION")
    abstract fun deleteAll()
}
