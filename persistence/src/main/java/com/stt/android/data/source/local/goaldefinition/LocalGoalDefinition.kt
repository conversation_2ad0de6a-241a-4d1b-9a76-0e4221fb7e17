package com.stt.android.data.source.local.goaldefinition

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.source.local.IntListJsonConverter
import com.stt.android.data.source.local.TABLE_GOAL_DEFINITION

@Entity(tableName = TABLE_GOAL_DEFINITION)
@TypeConverters(IntListJsonConverter::class)
data class LocalGoalDefinition(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = ID)
    val id: Long,
    @ColumnInfo(name = USER_NAME) val userName: String,
    @ColumnInfo(name = NAME) val name: String? = null,
    @ColumnInfo(name = TYPE) val type: Int = 0,
    @ColumnInfo(name = PERIOD) val period: Int = 0,
    @ColumnInfo(name = START_TIME) val startTime: Long,
    @ColumnInfo(name = END_TIME) val endTime: Long,
    @ColumnInfo(name = TARGET) val target: Int = 0,
    @ColumnInfo(name = CREATED) val created: Long = System.currentTimeMillis(),
    @ColumnInfo(name = ACTIVITY_IDS) val activityIds: List<Int>
) {
    companion object DbFields {
        const val ID = "id"
        const val USER_NAME = "userName"
        const val NAME = "name"
        const val TYPE = "type"
        const val PERIOD = "period"
        const val START_TIME = "startTime"
        const val END_TIME = "endTime"
        const val TARGET = "target"
        const val CREATED = "created"
        const val ACTIVITY_IDS = "activityIds"
    }
}
