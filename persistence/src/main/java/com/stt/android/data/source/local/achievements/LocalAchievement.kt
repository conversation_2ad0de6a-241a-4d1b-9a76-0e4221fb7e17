package com.stt.android.data.source.local.achievements

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.generateId
import com.stt.android.data.source.local.TABLE_ACHIEVEMENTS

@Entity(tableName = TABLE_ACHIEVEMENTS)
@TypeConverters(CumulativeAchievementConverter::class, PersonalBestAchievementConverter::class)
data class LocalAchievement(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String = generateId(),
    @ColumnInfo(name = "workoutKey") val workoutKey: String,
    @ColumnInfo(name = "activityType") val activityType: Int,
    @ColumnInfo(name = "timestamp") val timestamp: Long,
    @ColumnInfo(name = "cumulativeAchievements") val cumulativeAchievements: List<LocalCumulativeAchievement>,
    @ColumnInfo(name = "personalBestAchievements") val personalBestAchievements: List<LocalPersonalBestAchievement>
)
