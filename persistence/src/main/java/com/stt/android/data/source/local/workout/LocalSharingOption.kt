package com.stt.android.data.source.local.workout

import java.lang.IllegalArgumentException

enum class LocalSharingOption(val backendId: Int) {
    NOT_SHARED(0),
    FOLLOWERS(1 shl 4),
    EVERYONE(1 shl 1),
    FACEBOOK(1 shl 2),
    TWITT<PERSON>(1 shl 3),
    LINK(1);

    companion object {
        val DEFAULT = NOT_SHARED
        const val SHARED_MASK = 1
        const val PUBLIC_MASK = 2
        fun valueOf(position: Int): LocalSharingOption {
            for (sharingOption in values()) {
                if (position == sharingOption.ordinal) {
                    return sharingOption
                }
            }
            throw IllegalArgumentException("Invalid SharingOption ordinal value")
        }
    }
}
