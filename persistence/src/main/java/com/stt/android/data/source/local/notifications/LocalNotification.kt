package com.stt.android.data.source.local.notifications

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.stt.android.data.source.local.TABLE_NOTIFICATION

@Entity(tableName = TABLE_NOTIFICATION)
class LocalNotification(
    @ColumnInfo(name = ID) @PrimaryKey val id: String,
    @ColumnInfo(name = APP_URL) val appUrl: String,
    @ColumnInfo(name = TEXT) val text: String,
    @ColumnInfo(name = IMAGE_URL, defaultValue = "") val imageUrl: String,
    @ColumnInfo(name = TIMESTAMP) val timestamp: Long,
    @ColumnInfo(name = READ) val read: Boolean,
    @ColumnInfo(name = TYPE, defaultValue = "") val type: String,
) {
    companion object DbFields {
        const val ID = "id"
        const val APP_URL = "appUrl"
        const val TEXT = "text"
        const val IMAGE_URL = "imageUrl"
        const val TIMESTAMP = "timestamp"
        const val READ = "read"
        const val TYPE = "type"
    }
}
