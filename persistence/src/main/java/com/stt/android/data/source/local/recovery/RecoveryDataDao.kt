package com.stt.android.data.source.local.recovery

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.stt.android.data.source.local.TABLE_RECOVERY_DATA
import com.stt.android.data.source.local.recovery.LocalRecoveryData.Companion.COLUMN_SYNCED_STATUS
import com.stt.android.data.source.local.recovery.LocalRecoveryData.Companion.COLUMN_TIMESTAMP_ISO
import com.stt.android.data.source.local.recovery.LocalRecoveryData.Companion.COLUMN_TIMESTAMP_SECONDS
import kotlinx.coroutines.flow.Flow

@Dao
interface RecoveryDataDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecoveryData(recoveryData: List<LocalRecoveryData>)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertRecoveryDataSafe(recoveryData: List<LocalRecoveryData>): List<Long>

    @Query(
        """
        SELECT * FROM $TABLE_RECOVERY_DATA
        WHERE $COLUMN_TIMESTAMP_SECONDS BETWEEN :fromTimestampSeconds AND :toTimestampSeconds
        ORDER BY $COLUMN_TIMESTAMP_SECONDS DESC
    """
    )
    fun fetchRecoveryDataBetween(
        fromTimestampSeconds: Long,
        toTimestampSeconds: Long
    ): Flow<List<LocalRecoveryData>>

    @Query(
        """
        SELECT *
        FROM $TABLE_RECOVERY_DATA
        WHERE $COLUMN_SYNCED_STATUS = :syncedStatus
        ORDER BY $COLUMN_TIMESTAMP_SECONDS DESC
        """
    )
    suspend fun fetchRecoveryDataByStatus(syncedStatus: Int): List<LocalRecoveryData>

    @Query(
        """
        SELECT COUNT(*) FROM (SELECT 0 FROM $TABLE_RECOVERY_DATA LIMIT 1);
    """
    )
    suspend fun isRecoveryDataAvailable(): Int

    @Query(
        """
        SELECT COUNT(*)
        FROM (SELECT DISTINCT substr($COLUMN_TIMESTAMP_ISO, 2, 10) FROM $TABLE_RECOVERY_DATA LIMIT :checkLimit);
    """
    )
    suspend fun fetchNumDaysWithRecoveryData(checkLimit: Int): Int

    @Query(
        """
        DELETE
        FROM $TABLE_RECOVERY_DATA
    """
    )
    fun deleteAll()

    @Query(
        """
        SELECT *
        FROM $TABLE_RECOVERY_DATA
        WHERE $COLUMN_SYNCED_STATUS = 1
        ORDER BY $COLUMN_TIMESTAMP_SECONDS DESC
        LIMIT 1
    """
    )
    suspend fun fetchLatestSynced(): LocalRecoveryData?
}
