package com.stt.android.data.source.local.workout.zonesense

import androidx.room.TypeConverter
import com.stt.android.moshi.buildBasicMoshi

class LocalZoneSenseConverter {
    private val moshi = buildBasicMoshi()
    private val zoneSenseAdapter = moshi.adapter(LocalZoneSense::class.java)

    @TypeConverter
    fun fromLocalZoneSense(localZoneSense: LocalZoneSense?): String? =
        localZoneSense?.let { zoneSenseAdapter.toJson(it) }

    @TypeConverter
    fun toLocalZoneSense(zoneSenseJson: String?): LocalZoneSense? =
        zoneSenseJson?.let { zoneSenseAdapter.fromJson(it) }
}
