package com.stt.android.data.source.local.jumpropeextension

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.squareup.moshi.Json
import com.stt.android.data.source.local.TABLE_JUMP_ROPE_EXTENSION
import com.stt.android.data.source.local.workoutextension.LocalWorkoutExtension

@Entity(tableName = TABLE_JUMP_ROPE_EXTENSION)
class LocalJumpRopeExtension(
    workoutId: Int,
    @ColumnInfo(name = "Rounds") val rounds: Int?,
    @ColumnInfo(name = "AvgSkipsPerRound") val avgSkipsPerRound: Int?,
    @ColumnInfo(name = "MaxConsecutiveSkips") val maxConsecutiveSkips: Int?,
) : LocalWorkoutExtension(workoutId)
