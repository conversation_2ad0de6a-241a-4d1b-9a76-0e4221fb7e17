package com.stt.android.data.source.local.routes

import android.content.ContentValues
import android.database.Cursor
import android.provider.BaseColumns
import com.stt.android.data.source.local.IntListJsonConverter
import com.stt.android.db.getDouble
import com.stt.android.db.getInt
import com.stt.android.db.getLong
import com.stt.android.db.getString
import java.util.UUID

@Suppress("DEPRECATION")
@Deprecated("old route before DB version <= 13")
data class LocalRouteUpToVersion13(
    val id: String = generateId(), // @PrimaryKey @ColumnInfo(name = ID)
    val watchRouteId: Int = 0, // @ColumnInfo(name = WATCH_ROUTE_ID)
    val key: String = "", // @ColumnInfo(name = KEY)
    val ownerUserName: String, // @ColumnInfo(name = OWNER_USER_NAME)
    val name: String, // @ColumnInfo(name = NAME)
    val visibility: String, // @ColumnInfo(name = VISIBILITY)
    val activityIds: List<Int>, // @ColumnInfo(name = ACTIVITY_IDS)
    val averageSpeed: Double, // @ColumnInfo(name = AVERAGE_SPEED)
    val totalDistance: Double, // @ColumnInfo(name = TOTAL_DISTANCE)
    val startPoint: LocalPoint, // @ColumnInfo(name = START_POINT)
    val centerPoint: LocalPoint, // @ColumnInfo(name = CENTER_POINT)
    val stopPoint: LocalPoint, // @ColumnInfo(name = STOP_POINT)
    val locallyChanged: Boolean, // @ColumnInfo(name = LOCALLY_CHANGED)
    val modifiedDate: Long, // @ColumnInfo(name = MODIFIED_DATE)
    val deleted: Boolean = false, // @ColumnInfo(name = DELETED)
    val createdDate: Long = System.currentTimeMillis(), // @ColumnInfo(name = CREATED_DATE)
    val watchSyncState: String, // @ColumnInfo(name = WATCH_SYNC_STATE)
    val watchSyncResponseCode: Int = 0, // @ColumnInfo(name = WATCH_SYNC_RESPONSE_CODE)
    val watchEnabled: Boolean = false, // @ColumnInfo(name = WATCH_ENABLED)
    val segments: List<LocalRouteSegment> // @ColumnInfo(name = SEGMENTS)
) {

    /**
     * Use with caution, expects [Cursor] parameter to come from table with DB version < 14
     */
    constructor(c: Cursor) : this(
        id = c.getString(ID),
        watchRouteId = c.getInt(WATCH_ROUTE_ID),
        key = c.getString(KEY),
        ownerUserName = c.getString(OWNER_USER_NAME),
        name = c.getString(NAME),
        visibility = c.getString(VISIBILITY),
        activityIds = c.getString(ACTIVITY_IDS)
            .let { intListJsonConverter.toIntList(it) },
        averageSpeed = c.getDouble(AVERAGE_SPEED),
        totalDistance = c.getDouble(TOTAL_DISTANCE),
        startPoint = c.getString(START_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        centerPoint = c.getString(CENTER_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        stopPoint = c.getString(STOP_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        locallyChanged = c.getInt(LOCALLY_CHANGED) != 0,
        modifiedDate = c.getLong(MODIFIED_DATE),
        deleted = c.getInt(DELETED) != 0,
        createdDate = c.getLong(CREATED_DATE),
        watchSyncState = c.getString(WATCH_SYNC_STATE),
        watchSyncResponseCode = c.getInt(WATCH_SYNC_RESPONSE_CODE),
        watchEnabled = c.getInt(WATCH_ENABLED) != 0,
        segments = c.getString(SEGMENTS)
            .let { routeSegmentJsonConverter.toRouteSegment(it) }
    )

    fun asContentValues(): ContentValues {
        return ContentValues().apply {
            put(ID, id)
            put(WATCH_ROUTE_ID, watchRouteId)
            put(KEY, key)
            put(OWNER_USER_NAME, ownerUserName)
            put(NAME, name)
            put(VISIBILITY, visibility)
            put(ACTIVITY_IDS, intListJsonConverter.fromIntList(activityIds))
            put(AVERAGE_SPEED, averageSpeed)
            put(TOTAL_DISTANCE, totalDistance)
            put(START_POINT, pointJsonConverter.fromPoint(startPoint))
            put(CENTER_POINT, pointJsonConverter.fromPoint(centerPoint))
            put(STOP_POINT, pointJsonConverter.fromPoint(stopPoint))
            put(LOCALLY_CHANGED, if (locallyChanged) 1 else 0)
            put(MODIFIED_DATE, modifiedDate)
            put(DELETED, if (deleted) 1 else 0)
            put(CREATED_DATE, createdDate)
            put(WATCH_SYNC_STATE, watchSyncState)
            put(WATCH_SYNC_RESPONSE_CODE, watchSyncResponseCode)
            put(WATCH_ENABLED, if (watchEnabled) 1 else 0)
            put(SEGMENTS, routeSegmentJsonConverter.fromRouteSegment(segments))
        }
    }

    companion object DbFields {
        const val ID = BaseColumns._ID
        const val KEY = "key"
        const val OWNER_USER_NAME = "ownerUserName"
        const val NAME = "name"
        const val VISIBILITY = "visibility"
        const val ACTIVITY_IDS = "activityIds"
        const val AVERAGE_SPEED = "avgSpeed"
        const val TOTAL_DISTANCE = "totalDistance"
        const val START_POINT = "startPoint"
        const val CENTER_POINT = "centerPoint"
        const val STOP_POINT = "stopPoint"
        const val LOCALLY_CHANGED = "locallyChanged"
        const val DELETED = "deleted"
        const val CREATED_DATE = "created"
        const val MODIFIED_DATE = "modifiedDate"
        const val WATCH_SYNC_STATE = "watchSyncState"
        const val WATCH_SYNC_RESPONSE_CODE = "watchSyncResponseCode"
        const val SEGMENTS = "segments"
        const val WATCH_ROUTE_ID = "watchRouteId"
        const val WATCH_ENABLED = "watchEnabled"

        val intListJsonConverter = IntListJsonConverter()
        val routeSegmentJsonConverter = RouteSegmentJsonConverter()
        val pointJsonConverter = PointJsonConverter()

        fun generateId() = UUID.randomUUID().toString()
    }
}
