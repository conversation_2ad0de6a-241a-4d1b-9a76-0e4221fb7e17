package com.stt.android.data.source.local.workout.attributes

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.squareup.moshi.JsonClass
import com.stt.android.data.source.local.StringListJsonConverter
import com.stt.android.data.source.local.TABLE_WORKOUT_ATTRIBUTES_UPDATE

/**
 * This table should only contain pending updates to workout attributes. After the attributes have
 * been synced to the backend, they should be removed from the local db.
 */
@Entity(tableName = TABLE_WORKOUT_ATTRIBUTES_UPDATE)
@TypeConverters(LocalWorkoutAttributesConverter::class, StringListJsonConverter::class)
class LocalWorkoutAttributesUpdate(
    @ColumnInfo(name = WORKOUT_ID) @PrimaryKey val workoutId: Int,
    @ColumnInfo(name = OWNER_USERNAME) val ownerUsername: String,
    @ColumnInfo(name = WORKOUT_ATTRIBUTES) val attributes: LocalWorkoutAttributes?,
    @ColumnInfo(name = FIELDS) val fields: List<String>,
    @ColumnInfo(name = REQUIRES_USER_CONFIRMATION) val requiresUserConfirmation: Boolean
) {
    companion object DbFields {
        const val WORKOUT_ID = "workoutId"
        const val OWNER_USERNAME = "ownerUsername"
        const val WORKOUT_ATTRIBUTES = "attributes"
        const val FIELDS = "fields"
        const val REQUIRES_USER_CONFIRMATION = "requiresUserConfirmation"
    }
}

@JsonClass(generateAdapter = true)
class LocalWorkoutAttributes(
    val startPosition: LocalWorkoutLocation?,
    val tss: LocalWorkoutAttributeTSS?,
    val maxSpeed: Double?,
    val ascent: Double?,
    val descent: Double?,
    val suuntoTags: List<String>?
)

@JsonClass(generateAdapter = true)
class LocalWorkoutLocation(
    val latitude: Double,
    val longitude: Double
)

@JsonClass(generateAdapter = true)
class LocalWorkoutAttributeTSS(
    val trainingStressScore: Float,
    val calculationMethod: LocalWorkoutAttributeTSSCalculationMethod,
    val intensityFactor: Float? = null,
    val normalizedPower: Float? = null,
    val averageGradeAdjustedPace: Float? = null
)

@JsonClass(generateAdapter = false)
enum class LocalWorkoutAttributeTSSCalculationMethod {
    POWER,
    PACE,
    HR,
    SWIM_PACE,
    MET,
    MANUAL,
    DYNAMIC_DFA,
}
