<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.stt.android.sportmode">

    <application>
        <activity
            android:name=".home.SportModeHomeActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".selectmode.SelectModeActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".selectmode.SelectModeHeaderActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".trainingmode.TrainingModeEditActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".modesetting.ModeSettingActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".datascreen.options.DataOptionsActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".datascreen.fields.DataOptionsFieldsActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".datascreen.DataScreenEditActivity"
            android:theme="@style/WhiteTheme" />
    </application>
</manifest>
