package com.suunto.scsampleapp;

import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.app.TaskStackBuilder;
import com.suunto.connectivity.repository.LogbookEntrySyncResult;
import com.suunto.connectivity.sync.SyncResultProcessor;
import com.suunto.connectivity.watch.SpartanSyncResult;
import java.util.List;
import javax.inject.Inject;
import timber.log.Timber;

public class SyncResultReceiver implements SyncResultProcessor {
    private final Context context;

    @Inject
    public SyncResultReceiver(Context context) {
        this.context = context;
    }

    @Override
    public void processSyncResult(@NonNull SpartanSyncResult spartanSyncResult) {
        Timber.d("Got sync result [%s]", spartanSyncResult);
        if (spartanSyncResult.getLogbookResult().getLogbookResult().isSuccess()) {
            List<LogbookEntrySyncResult> logbookEntriesResult =
                spartanSyncResult.getLogbookResult().getLogbookEntriesResult();
            int successfulEntries = 0;
            for (LogbookEntrySyncResult logbookEntrySyncResult : logbookEntriesResult) {
                if (logbookEntrySyncResult.getSamplesResult().isSuccess()
                    && logbookEntrySyncResult.getSummaryResult().isSuccess()) {
                    successfulEntries++;
                }
            }
            NotificationCompat.Builder builder =
                new NotificationCompat.Builder(context, NotificationChannels.CHANNEL_ID_SYNC_RESULT)
                    .setSmallIcon(R.drawable.ic_notification)
                    .setContentTitle(context.getString(R.string.notification_sync_finished))
                    .setContentText(context.getString(R.string.notification_successful_entries,
                        successfulEntries))
                    .setDefaults(Notification.DEFAULT_ALL);

            // Creates an explicit intent for an Activity in your app
            Intent resultIntent = MainActivity.startIntentOpenDevice(context, spartanSyncResult.getMacAddress());

            // The stack builder object will contain an artificial back stack for the
            // started Activity.
            // This ensures that navigating backward from the Activity leads out of
            // your application to the Home screen.
            TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
            // Adds the back stack for the Intent (but not the Intent itself)
            stackBuilder.addParentStack(MainActivity.class);
            // Adds the Intent that starts the Activity to the top of the stack
            stackBuilder.addNextIntent(resultIntent);
            PendingIntent resultPendingIntent =
                stackBuilder.getPendingIntent(
                    0,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
                );
            builder.setContentIntent(resultPendingIntent);

            builder.setAutoCancel(true);

            NotificationManager mNotificationManager =
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            // mId allows you to update the notification later on.
            mNotificationManager.notify(R.id.sync_result_notification, builder.build());
        }
    }
}
