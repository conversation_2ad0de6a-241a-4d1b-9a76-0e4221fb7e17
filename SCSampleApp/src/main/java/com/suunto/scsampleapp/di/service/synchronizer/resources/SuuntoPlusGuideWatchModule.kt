package com.suunto.scsampleapp.di.service.synchronizer.resources

import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideWatchSyncTrigger
import com.suunto.scsampleapp.mock.MockSuuntoPlusGuideWatchSyncLogic
import dagger.Binds
import dagger.Module

@Module
abstract class SuuntoPlusGuideWatchModule {

    @Binds
    abstract fun bindSuuntoPlusGuideWatchSyncTrigger(
        mockSuuntoPlusGuideWatchSyncLogic: MockSuuntoPlusGuideWatchSyncLogic
    ): SuuntoPlusGuideWatchSyncTrigger
}
