package com.stt.android.testutils

import org.junit.Test
import org.mockito.kotlin.inOrder
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify

class HandlerDelayControllerTest {

    private val controller = HandlerDelayController()
    private val handler = controller.handler
    private val runnable = mock<Runnable>()
    private val runnable2 = mock<Runnable>()

    @Test
    fun `should run posted runnable immediately`() {
        handler.post(runnable)
        verify(runnable).run()
    }

    @Test
    fun `should run delayed runnables when time is advanced`() {
        handler.postDelayed(runnable2, 200)
        handler.postDelayed(runnable, 100)

        // No runnables run immediately.
        verify(runnable, never()).run()
        verify(runnable2, never()).run()

        // Advance time by less than the first delay.
        controller.advanceTime(50)
        // No runnables run yet.
        verify(runnable, never()).run()
        verify(runnable2, never()).run()

        // Advance time to match the first delay.
        controller.advanceTime(50)
        // Only the first runnable is run.
        verify(runnable).run()
        verify(runnable2, never()).run()

        // Advance time to match the second delay.
        controller.advanceTime(100)
        // Both runnables have been run once.
        verify(runnable).run()
        verify(runnable2).run()
    }

    @Test
    fun `should run all pending runnables`() {
        handler.postDelayed(runnable2, 200)
        handler.postDelayed(runnable, 100)

        controller.runAll()
        inOrder(runnable, runnable2) {
            verify(runnable).run()
            verify(runnable2).run()
        }
    }

    @Test
    fun `should remove given runnable`() {
        handler.postDelayed(runnable, 100)
        handler.postDelayed(runnable2, 200)

        handler.removeCallbacks(runnable)
        controller.runAll()
        verify(runnable, never()).run()
        verify(runnable2).run()
    }

    @Test
    fun `should remove all runnables`() {
        handler.postDelayed(runnable, 100)
        handler.postDelayed(runnable2, 200)

        handler.removeCallbacksAndMessages(null)
        controller.runAll()
        verify(runnable, never()).run()
        verify(runnable2, never()).run()
    }
}
