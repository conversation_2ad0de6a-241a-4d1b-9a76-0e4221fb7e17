package com.stt.android.home.explore.pois.coordinates

import android.content.Context
import android.text.Editable
import android.text.InputType
import android.util.AttributeSet
import android.view.Gravity
import com.stt.android.ui.components.InlineEditor
import java.util.Locale

class GeocoordinateEditor : InlineEditor<Double> {
    enum class Mode {
        LATITUDE,
        LONGITUDE
    }

    private var range = -180.0..180.0

    constructor(context: Context?, attrs: AttributeSet?, defStyle: Int) : super(
        context,
        attrs,
        defStyle
    )

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}

    override fun init(context: Context, attrs: AttributeSet) {
        super.init(context, attrs)

        with(editorValue) {
            gravity = Gravity.START
            inputType =
                InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL or InputType.TYPE_NUMBER_FLAG_SIGNED
        }
    }

    override fun convertToValue(text: Editable): Double {
        val string = text.toString()
        return if (string.isEmpty()) {
            0.0
        } else {
            try {
                string.toDouble()
            } catch (e: NumberFormatException) {
                0.0
            }
        }
    }

    override fun getValidValue(valueToCheck: Double) = valueToCheck.coerceIn(range)

    fun setMode(mode: Mode) {
        range = if (mode == Mode.LATITUDE) {
            -90.0..90.0
        } else {
            -180.0..180.0
        }
    }

    /**
     * @return true if the value is in-between the min and max values
     */
    override fun isValid(valueToCheck: Double) = valueToCheck in range

    override fun getSubtitleForValue(value: Double?): String = if (value != null) {
        String.format(Locale.US, "%.6f", value)
    } else {
        ""
    }
}
