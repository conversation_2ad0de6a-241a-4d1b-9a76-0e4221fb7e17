package com.stt.android.home.explore.toproutes

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.toproutes.carousel.RouteFeature
import com.stt.android.maps.TopRouteType
import com.stt.android.maps.TopRouteTypes
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

@HiltViewModel
class TopRoutesSharedViewModel @Inject constructor(
    val savedStateHandle: SavedStateHandle,
    @TopRouteTypes val topRouteTypes: List<TopRouteType>
) : ViewModel() {
    private val _visibleFeatures = MutableStateFlow<List<RouteFeature>>(emptyList())
    val visibleFeatures: StateFlow<List<RouteFeature>>
        get() = _visibleFeatures

    var activityType: ActivityType? = savedStateHandle.get(BaseTopRoutesActivity.EXTRA_ACTIVITY_TYPE)
    var topRouteType: TopRouteType? = activityType?.let { topRouteActivityType ->
        topRouteTypes.firstOrNull { it.activityType == topRouteActivityType }
    }

    var latLng: LatLng = savedStateHandle.get(BaseTopRoutesActivity.EXTRA_LAT_LNG)
        ?: throw RuntimeException("Missing latLng")
    var zoom: Float = savedStateHandle.get(BaseTopRoutesActivity.EXTRA_ZOOM)
        ?: STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
    var showMarker: Boolean =
        savedStateHandle.get(BaseTopRoutesActivity.EXTRA_SHOW_MARKER) ?: false

    fun updateVisibleFeatures(features: List<RouteFeature>) {
        _visibleFeatures.value = features
    }
}
