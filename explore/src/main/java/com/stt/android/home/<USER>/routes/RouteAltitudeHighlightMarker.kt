package com.stt.android.home.explore.routes

import android.annotation.SuppressLint
import android.content.Context
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.utils.MPPointF
import com.stt.android.home.explore.R

@SuppressLint("ViewConstructor")
class RouteAltitudeHighlightMarker(
    context: Context,
) : MarkerView(context, R.layout.route_altitude_highlight_marker) {

    override fun getOffsetForDrawingAtPoint(posX: Float, posY: Float): MPPointF {
        return MPPointF(offset.x + -measuredWidth * 0.5f, offset.y - posY)
    }
}
