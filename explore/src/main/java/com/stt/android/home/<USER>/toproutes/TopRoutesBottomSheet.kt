package com.stt.android.home.explore.toproutes

import android.content.Context
import android.text.style.TextAppearanceSpan
import android.view.View
import android.view.View.OnClickListener
import android.widget.CompoundButton
import androidx.annotation.StringRes
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.soy.algorithms.ascent.VerticalDelta
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.FontRefs
import com.stt.android.ThemeColors
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.RouteVerticalDeltaCalc
import com.stt.android.domain.routes.RouteWatchSyncState
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.databinding.BottomsheetRoutePlannerBinding
import com.stt.android.home.explore.routes.RouteUtils
import com.stt.android.home.explore.routes.RouteUtils.getActivityIdsFromActivityTypes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.routes.widget.ActivityTypeIconsAdapter
import com.stt.android.ui.extensions.setGone
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.CustomFontStyleSpan
import io.reactivex.Observable
import java.util.UUID
import com.stt.android.R as BaseR

interface TopRoutesBottomSheet {
    val onSaveClicked: Observable<Unit>
    val onCancelClicked: Observable<Unit>

    fun initBottomSheet(
        binding: BottomsheetRoutePlannerBinding,
        onCheckedChangeListener: CompoundButton.OnCheckedChangeListener,
        onExplorePremiumClickListener: OnClickListener
    ): BottomSheetBehavior<*>

    fun isBottomSheetVisible(): Boolean
    fun showBottomSheet()
    fun hideBottomSheet()
    fun updateBottomSheet(
        route: TopRoute?,
        activityType: ActivityType,
        binding: BottomsheetRoutePlannerBinding,
        selectedActivitiesList: List<ActivityType>?
    )
    fun resetBottomSheet()
    fun copyRoute(): Route?
    fun showAddToWatchView()
    fun hideAddToWatch()
    fun toggleWatchEnabled(isEnabled: Boolean)
    fun setHasPremiumSubscription(hasPremium: Boolean)
}

abstract class BaseTopRoutesBottomSheetDelegate constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController
) : TopRoutesBottomSheet {

    protected lateinit var activityType: ActivityType
    private lateinit var context: Context
    protected lateinit var binding: BottomsheetRoutePlannerBinding
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<*>
    private var route: TopRoute? = null

    // Protected needed for Suunto flavor
    protected lateinit var onCheckedChangeListener: CompoundButton.OnCheckedChangeListener

    // Protected needed for Suunto flavor
    protected var syncToWatchEnabled: Boolean = false

    private val valueFont by lazy {
        ResourcesCompat.getFont(context, FontRefs.WORKOUT_VALUE_FONT_REF)
    }
    private val unitFont by lazy {
        ResourcesCompat.getFont(context, FontRefs.WORKOUT_UNIT_FONT_REF)
    }

    private val valueSpanFactory = TextFormatter.SpanFactory {
        valueFont?.let {
            listOf<Any>(
                TextAppearanceSpan(context, BaseR.style.WorkoutSummaryValue),
                CustomFontStyleSpan(it)
            )
        } ?: listOf<Any>(
            TextAppearanceSpan(context, BaseR.style.WorkoutSummaryValue)
        )
    }

    private val unitSpanFactory = TextFormatter.SpanFactory {
        unitFont?.let {
            listOf<Any>(
                TextAppearanceSpan(context, BaseR.style.WorkoutSummaryValue),
                CustomFontStyleSpan(it)
            )
        } ?: listOf<Any>(
            TextAppearanceSpan(context, BaseR.style.WorkoutSummaryValue)
        )
    }

    override val onSaveClicked: Observable<Unit> = Observable.create { emitter ->
        binding.save.setOnClickListenerThrottled {
            if (route == null) return@setOnClickListenerThrottled

            val editText = binding.bottomSheet.routeName
            val name = editText.text
            if (name.isNullOrBlank()) {
                showMessage(BaseR.string.invalid_route_name, binding)
                editText.requestFocus()
            } else {
                emitter.onNext(Unit)
            }
        }
    }

    override val onCancelClicked: Observable<Unit> = Observable.create { emitter ->
        binding.bottomSheet.cancel.setOnClickListenerThrottled {
            emitter.onNext(Unit)
        }
    }

    override fun initBottomSheet(
        binding: BottomsheetRoutePlannerBinding,
        onCheckedChangeListener: CompoundButton.OnCheckedChangeListener,
        onExplorePremiumClickListener: OnClickListener
    ): BottomSheetBehavior<*> {
        this.binding = binding
        this.context = binding.root.context
        this.onCheckedChangeListener = onCheckedChangeListener
        binding.bottomSheet.openPremiumPromotion.setOnClickListener(onExplorePremiumClickListener)
        binding.bottomSheet.routeTip.visibility = View.GONE
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSheetView)
        bottomSheetBehavior.isHideable = true
        bottomSheetBehavior.peekHeight = 0
        return bottomSheetBehavior
    }

    private fun showMessage(
        @StringRes messageId: Int,
        binding: BottomsheetRoutePlannerBinding
    ) {
        val message: String = context.getString(messageId)
        val snackbar = Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG)
        // Set same elevation as for bottom sheet to make sure snackbar is not hidden behind it
        snackbar.view.elevation = binding.root.elevation
        snackbar.show()
    }

    override fun isBottomSheetVisible(): Boolean {
        throwIfNotInitialized()
        return bottomSheetBehavior.state == BottomSheetBehavior.STATE_EXPANDED
    }

    override fun showBottomSheet() {
        throwIfNotInitialized()
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun hideBottomSheet() {
        throwIfNotInitialized()
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    override fun updateBottomSheet(
        route: TopRoute?,
        activityType: ActivityType,
        binding: BottomsheetRoutePlannerBinding,
        selectedActivitiesList: List<ActivityType>?
    ) {
        throwIfNotInitialized()
        this.route = route?.copy(id = UUID.randomUUID().toString())
        this.activityType = activityType
        binding.save.isEnabled = route != null
        updateBottomSheetActivityList(binding, selectedActivitiesList)
        updateBottomSheetAltitudeChart(route, binding)
        binding.bottomSheet.ivTouchGuidance.setGone()
        binding.bottomSheet.ivTouchPoint.setGone()
        if (route != null) {
            updateBottomSheetDistance(route, binding)
            val verticalDelta = RouteVerticalDeltaCalc.calculateVerticalDelta(route.segments)
            updateBottomSheetAscent(verticalDelta, binding)
            updateBottomSheetDurationAndSpeed(route, binding)
            updateBottomSheetDescent(verticalDelta, binding)
            binding.bottomSheet.routeInfoGroup.visibility = View.VISIBLE
        } else {
            binding.bottomSheet.routeInfoGroup.visibility = View.GONE
        }
    }

    private fun updateBottomSheetActivityList(
        binding: BottomsheetRoutePlannerBinding,
        selectedActivitiesList: List<ActivityType>?
    ) {
        val editor = binding.bottomSheet.activityTypeEditor
        binding.bottomSheet.activityTypeEditor.setAllSelectable(false)
        editor.setOnValueChangedListener { newActivities: List<ActivityType> ->
            showSelectedActivities(newActivities, binding)
        }
        binding.bottomSheet.editActivityTypes.setOnClickListener {
            editor.callOnClick()
        }
        showSelectedActivities(
            selectedActivitiesList ?: listOf(activityType),
            binding
        )
    }

    private fun updateBottomSheetAltitudeChart(
        route: TopRoute?,
        binding: BottomsheetRoutePlannerBinding
    ) {
        val chartData = RouteUtils.calculateAltitudeChartData(
            route?.segments ?: emptyList(),
            infoModelFormatter.unit
        )
        with (binding.bottomSheet.routeAltitudeChartWithAxis) {
            updateRouteAltitudeChart(chartData, ClimbGuidance.EMPTY, null)
            isVisible = true
        }
    }

    private fun updateBottomSheetDistance(
        route: TopRoute,
        binding: BottomsheetRoutePlannerBinding
    ) {
        val workoutValue =
            infoModelFormatter.formatValue(SummaryItem.DISTANCE, route.totalDistance)
        binding.bottomSheet.routeDistance.text =
            TextFormatter.formatWorkoutValue(
                context.resources,
                workoutValue,
                valueSpanFactory,
                unitSpanFactory
            )
    }

    private fun updateBottomSheetAscent(
        verticalDelta: VerticalDelta?,
        binding: BottomsheetRoutePlannerBinding
    ) {
        if (verticalDelta != null) {
            val measurementUnit: MeasurementUnit = userSettingsController.settings.measurementUnit
            val formattedAscent =
                TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(verticalDelta.ascent))
            binding.bottomSheet.routeAscentValue.text =
                TextFormatter.formatValueAndUnit(
                    formattedAscent,
                    context.getString(measurementUnit.altitudeUnit),
                    valueSpanFactory,
                    unitSpanFactory
                )
        } else {
            binding.bottomSheet.routeAscentValue.visibility = View.GONE
        }
    }

    private fun updateBottomSheetDescent(
        verticalDelta: VerticalDelta?,
        binding: BottomsheetRoutePlannerBinding
    ) {
        if (verticalDelta != null) {
            val measurementUnit: MeasurementUnit = userSettingsController.settings.measurementUnit
            val formattedDescent =
                TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(verticalDelta.descent))
            binding.bottomSheet.routeDescentValue.text =
                TextFormatter.formatValueAndUnit(
                    formattedDescent,
                    context.getString(measurementUnit.altitudeUnit),
                    valueSpanFactory,
                    unitSpanFactory
                )
        } else {
            binding.bottomSheet.routeDescentValue.visibility = View.GONE
        }
    }


    private fun updateBottomSheetDurationAndSpeed(
        route: TopRoute,
        binding: BottomsheetRoutePlannerBinding
    ) {
        val estDuration =
            infoModelFormatter.formatEstimatedRouteDuration(route.getDurationEstimation())
        val avgSpeed =
            infoModelFormatter.formatValueAsString(SummaryItem.AVGSPEED, route.averageSpeed)
        val speedLabel = binding.bottomSheet.speedLabel
        speedLabel.text = avgSpeed
        speedLabel.setTextColor(ThemeColors.primaryTextColor(speedLabel.context))
        binding.bottomSheet.routeDuration.text =
            TextFormatter.formatValueAndUnit(
                estDuration,
                null,
                valueSpanFactory,
                null
            )
    }

    private fun showSelectedActivities(
        newActivities: List<ActivityType>,
        binding: BottomsheetRoutePlannerBinding
    ) {
        binding.bottomSheet.activityTypeEditor.apply {
            value = newActivities
            removeAllViews()
            val activityTypeIcons = RecyclerView(context).apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(
                    context,
                    LinearLayoutManager.HORIZONTAL,
                    false
                )
                adapter = ActivityTypeIconsAdapter(
                    activities = newActivities,
                    background = ActivityTypeIconsAdapter.Background.GRAY,
                    visibleActivityIconLimit = 3
                )
            }
            addView(activityTypeIcons)
        }
        binding.save.isEnabled = route != null && newActivities.isNotEmpty()
    }

    override fun resetBottomSheet() {
        throwIfNotInitialized()
        hideBottomSheet()
        binding.save.isEnabled = route != null
        binding.bottomSheet.savingProgress.visibility = View.GONE
        binding.bottomSheet.routeName.text = null
        showSelectedActivities(listOf(activityType), binding)
    }

    override fun copyRoute(): Route? {
        throwIfNotInitialized()
        val editText = binding.bottomSheet.routeName
        val name = editText.text ?: ""
        val activityIds =
            binding.bottomSheet.activityTypeEditor.value
        return this.route?.run {
            Route(
                ownerUserName = currentUserController.username,
                name = name.toString(),
                activityIds = getActivityIdsFromActivityTypes(activityIds),
                totalDistance = getTotalDistanceFromMetaData(),
                ascent = getAscentFromMetaData(),
                descent = getDescentFromMetaData(),
                startPoint = startPoint,
                centerPoint = centerPoint,
                stopPoint = stopPoint,
                segments = segments,
                key = "",
                locallyChanged = true,
                watchSyncState = if (syncToWatchEnabled) RouteWatchSyncState.PENDING else RouteWatchSyncState.IGNORED,
                modifiedDate = System.currentTimeMillis(),
                createdDate = System.currentTimeMillis(),
                watchEnabled = syncToWatchEnabled
            )
        }
    }

    private fun throwIfNotInitialized() {
        if (!this::binding.isInitialized) throw IllegalStateException("TopRoutesBottomSheet not initialized. Call initBottomSheet() first.")
    }
}
