package com.stt.android.home.explore.routes.planner.kmlparser.tbulu
import com.stt.android.domain.routes.KmlPoint

data class Kml(
    val document: KmlDocument?
)

data class KmlDocument(
    val name: String?,
    val markFolder: KmlMarkFolder?,
    val trackFolder: KmlTrackFolder?
)

data class KmlMarkFolder(
    val placemarks: List<KmlPlacemark>?
)

data class KmlPlacemark(
    val name: String?,
    val marker: KmlMarker?
)

data class KmlMarker(
    val point: KmlPoint?
)

data class KmlTrackFolder(
    val kmlTrackPlacemark: KmlTrackPlacemark?
)

data class KmlTrackPlacemark(
    val kmlTrack: KmlTrack?
)

data class KmlTrack(
    val trackPoints: List<KmlPoint>?
)

