package com.stt.android.home.explore.routesv2

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.filter.TopRouteFilter
import com.stt.android.mapping.InfoModelFormatter

@Composable
fun PopularListScreen(
    viewData: RouteViewData,
    searchQuery: String,
    isFilterShown: Boolean,
    topRouteFilter: TopRouteFilter,
    infoModelFormatter: InfoModelFormatter,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    when (viewData) {
        RouteViewData.Loading -> {
            // do nothing here.
        }

        is RouteViewData.Error -> {
            RouteEmptyContent(
                emptyResId = R.string.mine_route_empty_tip,
                modifier = modifier
            )
        }

        is RouteViewData.Loaded -> {
            RouteListLoaded(
                searchQuery = searchQuery,
                emptyResId = R.string.popular_route_empty_tip,
                isFilterShown = isFilterShown,
                topRouteFilter = topRouteFilter,
                routesContainer = viewData.routesContainer,
                infoModelFormatter = infoModelFormatter,
                measurementUnit = measurementUnit,
                modifier = modifier
            )
        }
    }
}
