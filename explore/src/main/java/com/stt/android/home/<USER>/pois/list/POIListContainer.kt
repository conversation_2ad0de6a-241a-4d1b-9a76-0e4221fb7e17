package com.stt.android.home.explore.pois.list

import com.stt.android.domain.explore.pois.POI

data class POIListItem(
    val poi: POI,
    val formattedCoordinates: String,
    val formattedAltitude: String,
    val formattedDate: String,
    val showAddToWatchToggle: <PERSON><PERSON><PERSON>,
    val enableAddToWatchToggle: <PERSON><PERSON><PERSON>,
    val forceRebindNonce: Long? = null,
    val distanceFromCurrentLocation: Double? = null,
    val watchEnabled: Boolean,
    val modifiedDate: Long,
    val createdDate: Long
)

data class POIListContainer(
    val pois: List<POIListItem>,
    val onPOIClicked: (POI) -> Unit,
    val onWatchEnabledToggled: (POI) -> Unit
)
