package com.stt.android.home.explore.routes.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.content.ContextCompat
import androidx.core.graphics.withScale
import androidx.core.graphics.withTranslation
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.utils.Utils
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.soy.algorithms.climbanalysis.entities.isNotEmpty
import com.stt.android.extensions.currentlyHighlightedIndex
import com.stt.android.extensions.highlightIndex
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.RouteAltitudeHighlightMarker
import com.stt.android.home.explore.routes.RouteAltitudeLineChartRenderer
import com.stt.android.ui.fragments.workout.WorkoutLineChartBase
import com.stt.android.ui.utils.OnChartGestureListenerAdapter
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CoreR

class ClimbGuidanceAwareRouteAltitudeChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : WorkoutLineChartBase(context, attrs, defStyle) {
    var onValueHighlighted: ((x: Int) -> Unit)? = null
    private var indexToHighlight: Int? = null
    private var entryX = 0
    var currentClimbSegment: ClimbSegment? = null
        private set

    private var scrubbingCount = 0

    private var windowDrawable: WindowDrawable? = null

    init {
        isScaleXEnabled = true
        isHighlightPerTapEnabled = true
        isHighlightPerDragEnabled = true
        viewPortHandler.setMaximumScaleX(5f)
    }

    override fun addAvgLine(avgValue: Float) = Unit

    fun moveHighlightMoveEnd(climbGuidance: ClimbGuidance) {
        data.getDataSetByIndex(0)?.xMax
            ?.let { moveHighlightToX(it, climbGuidance) }
    }

    fun moveHighlightToX(x: Float, climbGuidance: ClimbGuidance) {
        highlightValue(x, 0, false)
        entryX = x.roundToInt()
        updateHighlight(climbGuidance)
    }

    fun getScrubbingCount(): Int {
        return scrubbingCount
    }

    fun isDraggedToEnd(): Boolean = data.getDataSetByIndex(0)?.xMax?.roundToInt() == entryX

    /**
     * Update altitude chart data points. The chart view is invalidated when data is updated to make
     * sure the view is refreshed immediately.
     */
    fun drawRouteAltitudeChart(chartData: RouteAltitudeChartData, climbGuidance: ClimbGuidance) {
        mRenderer = RouteAltitudeLineChartRenderer(this, mAnimator, mViewPortHandler)
        marker = RouteAltitudeHighlightMarker(context)
        if (currentlyHighlightedIndex != indexToHighlight) {
            highlightIndex(indexToHighlight)
        }
        setTouchEnabled(true)

        xAxis.initialize(resources)

        val lineData = LineData()
        LineDataSet(chartData.validEntries, "").apply {
            val delegateDrawable = if (climbGuidance.isNotEmpty() &&
                climbGuidance.totalDistance > 0.0
            ) {
                context.createColoredFillDrawable(
                    climbGuidance = climbGuidance,
                    leftSpacing = xAxis.spaceMin.roundToInt(),
                    rightSpacing = xAxis.spaceMax.roundToInt()
                )
            } else {
                context.createMonoColorFillDrawable()
                    .setSelected()
            }
            windowDrawable = delegateDrawable?.let { WindowDrawable(it) }
            fillDrawable = windowDrawable
            onChartGestureListener = object : OnChartGestureListenerAdapter() {
                override fun onChartScale(me: MotionEvent, scaleX: Float, scaleY: Float) {
                    updateFillDrawable()
                }

                override fun onChartTranslate(me: MotionEvent, dX: Float, dY: Float) {
                    updateFillDrawable()
                }

                // https://github.com/PhilJay/MPAndroidChart/issues/4739
                private fun updateFillDrawable() = post {
                    windowDrawable?.update(
                        xRange / visibleXRange,
                        (lowestVisibleX + xAxis.spaceMin) / xRange,
                    )
                }
            }
            updateDrawableForHighlightFill()
            setDrawFilled(true)
            setDrawValues(false)
            setDrawCircles(false)
            setDrawGridBackground(false)
            legend?.isEnabled = false
            lineWidth =
                resources.getDimensionPixelSize(R.dimen.altitude_line_stroke_width).toFloat()
            color = ContextCompat.getColor(context, CoreR.color.black)
            axisDependency = YAxis.AxisDependency.RIGHT
            lineData.addDataSet(this)
        }

        val waypoints = chartData.waypointEntries
        if (waypoints.isNotEmpty()) {
            LineDataSet(waypoints, "").apply {
                enableDashedLine(0f, 1f, 0f) // disables the line connecting the entries
                updateDrawableForHighlightFill()
                // draw only the icons
                setDrawValues(false)
                setDrawCircles(true)
                setDrawIcons(false)
                isHighlightEnabled = false
                circleHoleRadius = 2f
                circleRadius = 3f
                circleHoleColor = Color.WHITE
                setCircleColor(ContextCompat.getColor(context, CoreR.color.route_primary))
                axisDependency = YAxis.AxisDependency.RIGHT
                lineData.addDataSet(this)
            }
        }

        axisRight.isEnabled = false
        axisRight.setDrawGridLines(false)

        // Add 10 % headroom to axis limits to prevent the graph from being clipped
        val yRange = chartData.maxValue - chartData.minValue
        axisRight.axisMinimum = chartData.minValue - 0.1f * yRange
        axisRight.axisMaximum = chartData.maxValue + 0.1f * yRange
        extraTopOffset = 0f
        extraBottomOffset = 0f
        data = lineData

        setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry, h: Highlight) {
                scrubbingCount++
                entryX = e.x.roundToInt()
                updateHighlight(climbGuidance)
                if (entryX != indexToHighlight) {
                    onValueHighlighted?.invoke(entryX)
                }
            }

            override fun onNothingSelected() {
            }
        })
        moveHighlightMoveEnd(climbGuidance)

        invalidate()
    }

    private fun Drawable?.setSelected(): Drawable? {
        val layerDrawable = (this as? LayerDrawable) ?: return this

        val selectedDrawable = layerDrawable.findDrawableByLayerId(R.id.selected_color)
            ?.mutate()
        selectedDrawable?.level = if (xRange > 0.0F) {
            // max level for drawable is 10000
            ((entryX.toFloat() + xAxis.spaceMin) / xRange * 10000).roundToInt()
        } else {
            0
        }
        layerDrawable.setDrawableByLayerId(R.id.selected_color, selectedDrawable)

        return this
    }

    private fun LineDataSet.updateDrawableForHighlightFill() {
        highLightColor = Color.BLACK
        highlightLineWidth = 1.0f
        setDrawHorizontalHighlightIndicator(false)
    }

    private fun updateHighlight(climbGuidance: ClimbGuidance) {
        val lineDataSet = data?.getDataSetByIndex(0) ?: return
        if (climbGuidance.isEmpty()) {
            windowDrawable?.delegate.setSelected()
            invalidate()
            return
        }

        val totalDistance = climbGuidance.segments.last().climbSegment.distanceAtEnd
        val currentSelectedPercentage = entryX.toFloat() / (lineDataSet.xMax - lineDataSet.xMin)
        val currentDistance = currentSelectedPercentage * totalDistance

        val selectedGuidanceList = if (climbGuidance.segments.size == 1) {
            climbGuidance.segments
        } else {
            climbGuidance.segments.filter { it.climbSegment.distanceAtStart <= currentDistance }
        }

        val currentClimbSegment = selectedGuidanceList.last().climbSegment
        this.currentClimbSegment = currentClimbSegment

        val currentSegmentPercentage =
            (currentDistance - currentClimbSegment.distanceAtStart) / (currentClimbSegment.distanceAtEnd - currentClimbSegment.distanceAtStart)
        (windowDrawable?.delegate as? ClimbGuidanceAwareRouteAltitudeDrawable)
            ?.selectSegment(
                indexOfSelectedSegment = selectedGuidanceList.size - 1,
                currentSegmentPercentage = currentSegmentPercentage
            )
        windowDrawable?.invalidateSelf()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val result = super.onTouchEvent(event)
        if (result) {
            requestDisallowInterceptTouchEvent(true)
        }
        return result
    }

    class WindowDrawable(val delegate: Drawable) : Drawable() {
        private var scaleX: Float = 1f
        private var ratioX: Float = 0f

        override fun draw(canvas: Canvas) {
            canvas.withScale(x = scaleX) {
                this.withTranslation(x = -bounds.width() * ratioX) {
                    delegate.bounds = bounds
                    delegate.draw(this)
                }
            }
        }

        fun update(scaleX: Float, ratioX: Float) {
            this.scaleX = scaleX
            this.ratioX = ratioX
            invalidateSelf()
        }

        override fun setAlpha(alpha: Int) = Unit

        override fun setColorFilter(colorFilter: ColorFilter?) = Unit

        override fun getOpacity(): Int = PixelFormat.OPAQUE
    }

    private companion object {
        // Sometimes first few altitude values are 0, because watch has not recorded them.
        // Filter those out that altitude chart does not mistakenly start from 0
        val RouteAltitudeChartData.validEntries: List<Entry>
            get() {
                val indexOfFirstValidEntry = entries.indexOfFirst { it.y != 0F }
                return if (indexOfFirstValidEntry > 0) {
                    entries.subList(indexOfFirstValidEntry, entries.size)
                } else {
                    entries
                }
            }

        val ClimbGuidance.totalDistance: Double get() =
            segments.lastOrNull()?.climbSegment?.distanceAtEnd ?: 0.0

        fun Context.createMonoColorFillDrawable(): Drawable? =
            ContextCompat.getDrawable(this, R.drawable.route_altitude_graph_gradient)

        fun Context.createColoredFillDrawable(
            climbGuidance: ClimbGuidance,
            leftSpacing: Int,
            rightSpacing: Int
        ): Drawable = ClimbGuidanceAwareRouteAltitudeDrawable(
            context = this,
            climbGuidance = climbGuidance,
            totalDistance = climbGuidance.totalDistance,
            leftSpacing = leftSpacing,
            rightSpacing = rightSpacing
        )

        fun XAxis.initialize(resources: Resources) {
            isEnabled = false

            val space =
                Utils.convertPixelsToDp(resources.getDimension(BaseR.dimen.size_spacing_medium)) * 0.75F
            spaceMin = space
            spaceMax = space
        }
    }
}
