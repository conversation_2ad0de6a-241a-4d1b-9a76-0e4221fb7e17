package com.stt.android.home.explore.routes.planner

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
enum class RoutingMode(val index: Int, val analyticsName: String) : Parcelable {
    STRAIGHT(0, "Free"),
    FOOT(1, "Foot"),
    BIKE(2, "Bike"),
    MTB(3, "Mtb"),
    RACING_BIKE(4, "RoadBike"),
    BEARING_NAVIGATION(5, "BearingNavigation");
}

fun routingModeFromIndex(index: Int): RoutingMode? {
    return RoutingMode.entries.firstOrNull { it.index == index }
}
