package com.stt.android.home.explore.routes.list

import android.os.Bundle
import android.view.View
import com.stt.android.analytics.AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource
import com.stt.android.common.ui.observeK
import com.stt.android.premium.PremiumPurchaseFlowLauncher
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class RouteListFragment : BaseRouteListFragment() {

    @Inject
    lateinit var premiumPurchaseFlowLauncher: PremiumPurchaseFlowLauncher

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        premiumPurchaseFlowLauncher.register(this)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.openPremiumPromotion.observeK(this) {
            premiumPurchaseFlowLauncher.launchFeaturePromotionAndAskAboutAppReset(
                context = requireContext(),
                analyticsSource = PremiumPurchaseFlowScreenSource.ROUTE_LIBRARY_SCREEN
            )
        }
    }

    companion object {
        @JvmStatic
        fun newInstance() = RouteListFragment()
    }
}
