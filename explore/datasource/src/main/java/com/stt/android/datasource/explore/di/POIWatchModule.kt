package com.stt.android.datasource.explore.di

import com.stt.android.datasource.explore.pois.POIWatchSyncLogic
import com.stt.android.di.ConnectivityModule
import com.suunto.connectivity.poi.POIWatchSyncTrigger
import dagger.Binds
import dagger.Module

@Module
@ConnectivityModule
abstract class POIWatchModule {

    @Binds
    abstract fun bindPOIWatchSyncTrigger(
        poiWatchSyncLogic: POIWatchSyncLogic
    ): POIWatchSyncTrigger
}
