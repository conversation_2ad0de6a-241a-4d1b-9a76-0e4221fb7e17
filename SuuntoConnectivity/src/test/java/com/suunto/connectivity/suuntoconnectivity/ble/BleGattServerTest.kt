package com.suunto.connectivity.suuntoconnectivity.ble

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattServer
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.os.Handler
import com.google.common.truth.Truth.assertThat
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerConnectionStateChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerNotificationSentEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerServiceAddedEvent
import com.suunto.connectivity.suuntoconnectivity.utils.AndroidBtEnvironment
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectStrategy
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectStrategy.ConnectDelegate
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectionStateMonitor
import com.suunto.connectivity.util.workqueue.QueueOperation
import com.suunto.connectivity.util.workqueue.WorkQueue
import org.greenrobot.eventbus.EventBus
import org.jdeferred.Promise
import org.jdeferred.impl.DeferredObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever
import java.util.UUID

class BleGattServerTest {

    private val context = mock<Context>()
    private val bluetoothManager = mock<BluetoothManager>()
    private val connectionStateMonitor = mock<ConnectionStateMonitor>()
    private val bluetoothAdapter = mock<BluetoothAdapter>()
    private val bluetoothGattServer = mock<BluetoothGattServer>()
    private val gattService = mock<BluetoothGattService>()
    private val device1 = mock<BluetoothDevice>()
    private val device2 = mock<BluetoothDevice>()
    private val characteristic = mock<BluetoothGattCharacteristic>()
    private val androidBtEnvironment = mock<AndroidBtEnvironment>()
    private val eventBus = EventBus.getDefault()

    private lateinit var bleGattServer: BleGattServer
    private lateinit var client1: BleGattServer.Client
    private lateinit var client2: BleGattServer.Client

    private val simpleConnectStrategy = object : ConnectStrategy {
        override fun connect(
            bluetoothDevice: BluetoothDevice,
            connectDelegate: ConnectDelegate
        ): Promise<Void, Throwable, Any> {
            return connectDelegate.connect(true, false, 0)
        }

        override fun canConnect(bluetoothDevice: BluetoothDevice): Boolean {
            return true
        }

        override fun connect(
            address: String,
            connectDelegate: ConnectDelegate
        ): Promise<Void, Throwable, Any> {
            return DeferredObject<Void, Throwable, Any>().reject(IllegalAccessException())
        }

        override fun isInitialConnect(): Boolean? {
            return null
        }
    }

    @Before
    fun setUp() {
        val handler = mock<Handler> {
            on { post(any()) }.then { invocation ->
                val runnable = invocation.arguments.first() as Runnable
                runnable.run()
                true
            }
        }

        val workQueue = spy(WorkQueue(handler))
        doAnswer { invocation ->
            val runnable = invocation.arguments.first() as QueueOperation<*>
            runnable.run()
        }.whenever(workQueue).add(any())
        doAnswer { invocation ->
            val runnable = invocation.arguments.first() as QueueOperation<*>
            runnable.run()
        }.whenever(workQueue).addFirst(any())

        bleGattServer = BleGattServer(
            context,
            workQueue,
            bluetoothManager,
            connectionStateMonitor,
            androidBtEnvironment,
            handler
        )

        client1 = bleGattServer.createClient(CLIENT_ID_1)
        client1.setDefaultConnectStrategy(simpleConnectStrategy)
        client2 = bleGattServer.createClient(CLIENT_ID_2)
        client2.setDefaultConnectStrategy(simpleConnectStrategy)

        whenever(bluetoothManager.openGattServer(any(), any())).thenReturn(bluetoothGattServer)
        whenever(bluetoothManager.adapter).thenReturn(bluetoothAdapter)
        whenever(bluetoothAdapter.isDiscovering).thenReturn(true)
        whenever(gattService.uuid).thenReturn(UUID.fromString(SERVICE_UUID))
        whenever(device1.address).thenReturn(MAC_ADDRESS_1)
        whenever(device2.address).thenReturn(MAC_ADDRESS_2)
        whenever(device1.bondState).thenReturn(BluetoothDevice.BOND_BONDED)
        whenever(device2.bondState).thenReturn(BluetoothDevice.BOND_BONDED)
        whenever(characteristic.setValue(any<ByteArray>())).thenReturn(true)
    }

    @Test
    fun clientId() {
        assertThat(client1.clientId).isEqualTo(CLIENT_ID_1)
        assertThat(client2.clientId).isEqualTo(CLIENT_ID_2)
    }

    @Test
    fun addRemoveServiceSuccess() {
        // Add service
        whenever(bluetoothGattServer.addService(gattService)).thenReturn(true)
        var promise = client1.addService(gattService)
        verify(bluetoothGattServer).addService(gattService)
        assertThat(promise.isPending).isTrue()
        eventBus.post(
            BleGattServerServiceAddedEvent(BluetoothGatt.GATT_SUCCESS, gattService)
        )
        assertThat(promise.isResolved).isTrue()

        // Remove service
        whenever(bluetoothGattServer.removeService(gattService)).thenReturn(true)
        promise = client1.removeService(gattService)
        verify(bluetoothGattServer).removeService(gattService)
        assertThat(promise.isResolved).isTrue()
    }

    @Test
    fun addRemoveServiceFailure() {
        // Add service fails immediately
        whenever(bluetoothGattServer.addService(gattService)).thenReturn(false)
        var promise = client1.addService(gattService)
        assertThat(promise.isRejected).isTrue()

        // Add service fails after receiving failure event
        whenever(bluetoothGattServer.addService(gattService)).thenReturn(true)
        promise = client1.addService(gattService)
        assertThat(promise.isPending).isTrue()
        eventBus.post(
            BleGattServerServiceAddedEvent(BluetoothGatt.GATT_FAILURE, gattService)
        )
        assertThat(promise.isRejected).isTrue()

        // Remove service fails immediately
        whenever(bluetoothGattServer.removeService(gattService)).thenReturn(false)
        promise = client1.removeService(gattService)
        assertThat(promise.isRejected).isTrue()
    }

    @Test
    fun connectDisconnect() {
        // Connecting the first client creates the connection
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        var promise = client1.connect(device1)
        verify(bluetoothGattServer).connect(device1, true)
        assertThat(promise.isPending).isTrue()
        val connection = client1.connection
        assertThat(connection).isNotNull()
        assertThat(connection.isConnecting).isTrue()
        EventBus.getDefault().post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise.isResolved).isTrue()
        assertThat(connection.isConnected).isTrue()
        assertThat(connection.hasClients()).isTrue()

        // Connecting another client to the same device uses the existing connection
        promise = client2.connect(device1)
        verifyNoMoreInteractions(bluetoothGattServer)
        assertThat(promise.isResolved).isTrue()
        assertThat(connection).isSameInstanceAs(client2.connection)

        // Disconnecting any client closes the connection. The remaining clients
        // are responsible of reconnecting.
        whenever(
            connectionStateMonitor.getConnectionState(
                device1,
                BluetoothProfile.GATT_SERVER
            )
        )
            .thenReturn(BluetoothProfile.STATE_CONNECTED)
        promise = client1.disconnect()
        verify(bluetoothGattServer).cancelConnection(device1)
        Assert.assertTrue(promise.isPending)
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_DISCONNECTED
            )
        )
        assertThat(promise.isResolved).isTrue()
        assertThat(connection.isConnected).isFalse()
        assertThat(connection.hasClients()).isTrue()

        // Disconnect the other client. The device is already disconnected and
        // the promise is resolved immediately.
        whenever(
            connectionStateMonitor.getConnectionState(
                device1,
                BluetoothProfile.GATT_SERVER
            )
        )
            .thenReturn(BluetoothProfile.STATE_DISCONNECTED)
        promise = client2.disconnect()
        assertThat(promise.isResolved).isTrue()

        // BluetoothGattServer is closed after all clients have disconnected
        verify(bluetoothGattServer).close()
    }

    @Test
    fun connectSimultaneous() {
        // Start connecting the first client
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        val promise1 = client1.connect(device1)
        verify(bluetoothGattServer).connect(device1, true)
        assertThat(promise1.isPending).isTrue()

        // Connect second client while connecting the first client is still in progress
        val promise2 = client2.connect(device1)
        verifyNoMoreInteractions(bluetoothGattServer)
        assertThat(promise2.isPending).isTrue()
        assertThat(client1.connection).isSameInstanceAs(client2.connection)

        // Both connect promises are resolved when the connection is established
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(client1.connection.isConnected).isTrue()
        assertThat(promise1.isResolved).isTrue()
        assertThat(promise2.isResolved).isTrue()
    }

    @Test
    fun connectFailure() {
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(false)
        val promise = client1.connect(device1)
        verify(bluetoothGattServer).connect(device1, true)
        assertThat(promise.isRejected).isTrue()
    }

    @Test
    fun connectAgain() {
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        whenever(bluetoothGattServer.connect(device2, true)).thenReturn(true)

        // Connect client
        var promise = client1.connect(device1)
        assertThat(promise.isPending).isTrue()
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise.isResolved).isTrue()

        // Connect when already connected succeeds
        promise = client1.connect(device1)
        assertThat(promise.isResolved).isTrue()

        // Connecting different device fails when already connected
        promise = client1.connect(device2)
        assertThat(promise.isRejected).isTrue()

        // Connecting different device succeeds after disconnecting first
        promise = client1.disconnect()
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_DISCONNECTED
            )
        )
        assertThat(promise.isResolved).isTrue()
        promise = client1.connect(device2)
        assertThat(promise.isPending).isTrue()
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device2,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise.isResolved).isTrue()
    }

    @Test
    fun notifyCharacteristicChanged() {
        val value = ByteArray(10)
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                device1,
                characteristic,
                false
            )
        ).thenReturn(true)

        // Notify before client is connected fails
        val promise1 = client1.notifyCharacteristicChanged(characteristic, false, value)
        assertThat(promise1.isRejected).isTrue()

        // Connect client
        val promise2 = client1.connect(device1)
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise2.isResolved).isTrue()

        // Notify is successful
        val promise3 = client1.notifyCharacteristicChanged(characteristic, false, value)
        Mockito.verify(bluetoothGattServer).notifyCharacteristicChanged(
            device1,
            characteristic,
            false
        )
        assertThat(promise3.isPending).isTrue()
        eventBus.post(
            BleGattServerNotificationSentEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS
            )
        )
        assertThat(promise3.isResolved).isTrue()

        // Notify fails
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                device1,
                characteristic,
                false
            )
        )
            .thenReturn(false)
        val promise4 = client1.notifyCharacteristicChanged(characteristic, false, value)
        assertThat(promise4.isRejected).isTrue()
    }

    @Test
    fun notifyCharacteristicChangedLongValue() {
        val value = ByteArray(50)
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                device1,
                characteristic,
                false
            )
        ).thenReturn(true)

        // Connect client
        val promise1 = client1.connect(device1)
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise1.isResolved).isTrue()

        // Long value is split to multiple notifications
        val promise2 = client1.notifyCharacteristicChanged(characteristic, false, value)
        verify(bluetoothGattServer, Mockito.times(3)).notifyCharacteristicChanged(
            device1,
            characteristic,
            false
        )
        assertThat(promise2.isPending).isTrue()
        eventBus.post(
            BleGattServerNotificationSentEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS
            )
        )
        assertThat(promise2.isResolved).isTrue()
    }

    @Test
    fun sendResponse() {
        val requestId = 123
        val status = BluetoothGatt.GATT_SUCCESS
        val offset = 0
        val value = ByteArray(10)
        whenever(bluetoothGattServer.connect(device1, true)).thenReturn(true)
        whenever(
            bluetoothGattServer.sendResponse(
                device1,
                requestId,
                status,
                offset,
                value
            )
        )
            .thenReturn(true)

        // Send response before client is connected fails
        val promise1 = client1.sendResponse(requestId, status, offset, value)
        assertThat(promise1.isRejected).isTrue()

        // Connect client
        val promise2 = client1.connect(device1)
        eventBus.post(
            BleGattServerConnectionStateChangedEvent(
                device1,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(promise2.isResolved).isTrue()

        // Send response is successful
        val promise3 = client1.sendResponse(requestId, status, offset, value)
        verify(bluetoothGattServer).sendResponse(device1, requestId, status, offset, value)
        assertThat(promise3.isResolved).isTrue()

        // Send response fails
        whenever(
            bluetoothGattServer.sendResponse(
                device1,
                requestId,
                status,
                offset,
                value
            )
        )
            .thenReturn(false)
        val promise4 = client1.sendResponse(requestId, status, offset, value)
        assertThat(promise4.isRejected).isTrue()
    }

    companion object {
        private const val CLIENT_ID_1 = "client1"
        private const val CLIENT_ID_2 = "client2"
        private const val SERVICE_UUID = "a647661a-6deb-11e6-8b77-86f30ca893d3"
        private const val MAC_ADDRESS_1 = "08:00:69:02:01:FC"
        private const val MAC_ADDRESS_2 = "08:00:69:02:01:FD"
    }
}
