package com.suunto.connectivity.sdsmanager;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.movesense.mds.Mds;
import com.movesense.mds.MdsNotificationListener;
import com.movesense.mds.MdsResponseListener;
import com.movesense.mds.MdsSubscription;
import com.suunto.connectivity.sdsmanager.model.MdsConnectedDevice;
import com.suunto.connectivity.sdsmanager.model.MdsEvent;
import com.suunto.connectivity.sdsmanager.model.MdsUri;
import com.suunto.connectivity.util.FileUtils;
import java.io.IOException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import rx.observers.TestSubscriber;

import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_URI_ACTIVE_SYNC_CLIENT;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_URI_CONNECTEDDEVICES;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_URI_EVENTLISTENER;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Tests for class MdsRx
 */
@RunWith(MockitoJUnitRunner.class)
public class MdsRxTest {

    private static final String NAME = "Spartan Sport";
    private static final String SERIAL = "016311400010";

    @Mock
    private Mds mds;

    @Mock
    private MdsSubscription subscription;

    @Captor
    private ArgumentCaptor<MdsNotificationListener> notificationCaptor;

    @Captor
    private ArgumentCaptor<MdsResponseListener> responseCaptor;

    private Gson gson;
    private MdsRx mdsRx;

    @Before
    public void setUp() {
        gson = new GsonBuilder().create();

        when(mds.subscribe(anyString(), anyString(), any(MdsNotificationListener.class),
            any(MdsResponseListener.class)))
            .thenReturn(subscription);

        mdsRx = new MdsRx(mds, gson);
    }

    @Test
    public void shouldGetConnectedDevice() {
        TestSubscriber<MdsConnectedDevice> testSubscriber = new TestSubscriber<>();
        mdsRx.deviceConnectionObservable().subscribe(testSubscriber);

        // Verify that the subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri(MDS_URI_CONNECTEDDEVICES))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Now pass a device notification
        notificationCaptor.getValue()
            .onNotification(getJsonString("connectedDeviceEventBody.json"));

        // Verify that correct device was passed
        testSubscriber.assertValueCount(1);
        MdsConnectedDevice device = testSubscriber.getOnNextEvents().get(0);
        assertTrue(device.isConnected());
        assertEquals(SERIAL, device.getSerial());
    }

    @Test
    public void shouldGetDisconnectedDevice() {
        TestSubscriber<MdsConnectedDevice> testSubscriber = new TestSubscriber<>();
        mdsRx.deviceConnectionObservable().subscribe(testSubscriber);

        // Verify that the subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri(MDS_URI_CONNECTEDDEVICES))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Now pass a device notification
        notificationCaptor.getValue()
            .onNotification(getJsonString("disconnectedDeviceEventBody.json"));

        // Verify that correct device was passed
        testSubscriber.assertValueCount(1);
        MdsConnectedDevice device = testSubscriber.getOnNextEvents().get(0);
        assertFalse(device.isConnected());
        assertEquals(SERIAL, device.getSerial());
    }

    @Test
    public void shouldNotDoubleSubscribeToConnectedDevices() {
        TestSubscriber<MdsConnectedDevice> testSubscriber = new TestSubscriber<>();
        mdsRx.deviceConnectionObservable().subscribe(testSubscriber);
        mdsRx.deviceConnectionObservable().subscribe(testSubscriber);

        verify(mds, times(1)).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri(MDS_URI_CONNECTEDDEVICES))),
            any(MdsNotificationListener.class),
            any(MdsResponseListener.class));
    }

    @Test
    public void shouldGetSyncEvents() {
        TestSubscriber<Integer> testSubscriber = new TestSubscriber<>();
        mdsRx.syncEventObservable(SERIAL).subscribe(testSubscriber);

        // Verify that subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri(SERIAL + "/" + MDS_URI_ACTIVE_SYNC_CLIENT))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Then pass notification
        notificationCaptor.getValue()
            .onNotification(getJsonString("activeSyncClientEventBody.json"));

        // Verify result
        testSubscriber.assertValueCount(1);
        Integer event = testSubscriber.getOnNextEvents().get(0);
        assertEquals(2, event.intValue());
    }

    @Test
    public void shouldSubscribe() {
        TestSubscriber<MdsEvent> testSubscriber = new TestSubscriber<>();
        mdsRx.subscribe("testurl", false).subscribe(testSubscriber);

        // Verify that the subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri("testurl"))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Pass some notification
        notificationCaptor.getValue()
            .onNotification("{\"Body\":3,\"Uri\":\"testuri\",\"Method\":\"POST\"}");

        // Verify result
        testSubscriber.assertValueCount(1);
        MdsEvent event = testSubscriber.getOnNextEvents().get(0);
        assertEquals(3, event.getBody().getAsInt());
        assertEquals("testuri", event.getUri());
        assertEquals(MdsEvent.Method.POST, event.getMethod());
    }

    static class TestBody {
        String str;
    }

    @Test
    public void shouldSubscribeGeneric() {
        TestSubscriber<TestBody> testSubscriber = new TestSubscriber<>();
        mdsRx.subscribe("testurl", TestBody.class, false).subscribe(testSubscriber);

        // Verify that the subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri("testurl"))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Pass notification
        notificationCaptor.getValue()
            .onNotification("{\"Body\":{\"str\":\"val\"},\"Uri\":\"testuri\",\"Method\":\"PUT\"}");

        // Verify result
        testSubscriber.assertValueCount(1);
        TestBody body = testSubscriber.getOnNextEvents().get(0);
        assertEquals("val", body.str);
    }

    @Test
    public void shouldReturnIntegerOnSubscription() {
        TestSubscriber<Integer> testSubscriber = new TestSubscriber<>();
        mdsRx.subscribe("testurl", Integer.class, false).subscribe(testSubscriber);

        // Verify that the subscription went through
        verify(mds).subscribe(
            eq(MDS_URI_EVENTLISTENER),
            eq(gson.toJson(new MdsUri("testurl"))),
            notificationCaptor.capture(),
            responseCaptor.capture());

        // Pass some notification
        notificationCaptor.getValue()
            .onNotification("{\"Body\":3,\"Uri\":\"testuri\",\"Method\":\"POST\"}");

        // Verify result
        testSubscriber.assertValueCount(1);
        Integer value = testSubscriber.getOnNextEvents().get(0);
        assertEquals(3, value.intValue());
    }

    private String getJsonString(String file) {
        try {
            return FileUtils.getResourceAsString("sdsmanager/" + file);
        } catch (IOException ex) {
            fail();
            return null;
        }
    }
}
