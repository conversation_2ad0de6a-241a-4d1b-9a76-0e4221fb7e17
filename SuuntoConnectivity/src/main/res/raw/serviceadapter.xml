<?xml version="1.0" encoding="UTF-8"?>
<adapters>

    <adapter name="Movescount">
        <tree name="device">
            <node key="device.model" target="DeviceName" type="string"/>
            <node key="device.version.software" target="FirmwareVersion" type="string"/>
            <node key="device.version.hardware" target="HardwareVersion" type="string"/>
            <node key="device.serial" target="SerialNumber" type="string"/>
        </tree>

        <!--SUNRISE/ SUNSET
         Based on GPS
         To be defined
         To be defined
         LANGUAGE
         EN, FR, ES, GE, FI, SW, IT, PT, NL
         Default: English (initial settings only)
         Options: English, French, Spanish, German, Finnish, Swedish, Italian, Portuguese, Dutch
         <node target="Settings.Language" type="uint8" default="0"/>
         -->


        <tree name="settings">

            <!--MAIN SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 147.-->

            <!--Set automatically (GPS TIME SYNC) On/off, time zone, auto daylight saving) MUST MUST-->
            <node key="settings.device.time.nogpssync" target="Settings.GPSTimeKeeping" type="uint8" default="0"  max="1" min="0"/>

            <!--DATE format Dd/mm/yyyy or mm/dd/yyyy MUST MUST-->
            <node key="settings.device.date.format" target="Settings.DisplayDateMode" type="uint8" default="0"  max="1" min="0"/>

            <!--Reference field missing from REST -->
            <!-- SDK IMPLEMENTATION MISSING!!!!-->

            <!--All on/Buttons off/All off MUST MUST-->
            <node key="settings.device.audio.tones.tonesmode" target="Settings.TonesMode" type="uint8" default="0"  max="2" min="0"/>

            <!--BACKLIGHT MODE Normal (activated from light button), Off (no light activation), Night (activated from any button), Toggle-->
            <node key="settings.device.display.backlight.mode" target="Settings.BacklightMode" type="uint8" default="0" max="3" min="0"/>

            <!--BACKLIGHT BRIGHTNESS 0-100% MUST MUST-->
            <node key="settings.device.display.backlight.brightness" target="Settings.BacklightBrightness" type="uint8" default="50" min="0" max="100"/>

            <!--Contrast 0-100% -->
            <node key="settings.device.display.contrast" target="Settings.DisplayBrightness" type="uint8" default="50" min="0" max="100"/>

            <!--COMPASS DECLINATION -90.0 - 90.0° MUST MUST-->
            <node key="settings.device.declination" target="Settings.CompassDeclination" type="float" default="0.0" unit="degrees" min="-90.0" max="90.0"/>

            <!--ALTI-BARO MODE Alti, Baro, Automatic MUST MUST-->
            <node key="settings.device.altibaro.profile" target="Settings.AltiBaroMode" type="uint8" default="2" min="0" max="2"/>
            <node key="settings.device.altibaro.fusedaltidisabled" target="Settings.FusedAltiDisabled" type="uint8" default="0" max="1" min="0"/>

            <!--STORM ALARM-->
            <node key="settings.device.altibaro.stormalarm" target="Settings.StormAlarming" type="uint8" default="0" max="1" min="0"/>

            <!--Language 0 - 10 -->
            <node key="settings.device.formats.language" target="Settings.Language" type="uint8" default="2" min="0" max="10"/>

            <!-- DISPLAY INVERT Positive/Negative MUST MUST-->
            <node key="settings.device.display.invert" target="Settings.DisplayIsNegative" type="uint8" default="0" max="1" min="0"/>

            <!--Position format (Hddd.ddddd° , Hddd°mm.mmm’ , Hddd°mm’ss.s” , UTM, MGRS) MUST MUST-->
            <node key="settings.device.formats.positionformat" target="Settings.GPSPositionFormat" type="uint8" default="0" min="0" max="15"/>

            <!--Navigation style / orientation-->
            <node key="settings.device.formats.orientation" target="Settings.NavigationStyle" type="uint8" default="1" min="0" max="1"/>

            <!--GPS Datum Currently valid datums Default: WGS84 Other Datums than WGS84 only adjustable from MC-->
            <!-- SDK and REST IMPLEMENTATION MISSING!!!!-->


            <!--PERSONAL SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 9.-->
            <node key="settings.personal.height" target="Settings.Height" type="float" min="0.5" max="3.0" unit="meters"/>

            <!--Weight MUST MUST-->
            <!-- Is default 75 kg ok? Is 10 - 300 kg range ok?-->
            <node key="settings.personal.weight" target="Settings.Weight" type="float" min="10" max="300" unit="kg"/>

            <!--Gender no MUST-->
            <node key="settings.personal.gender" target="Settings.IsMale" type="bool" max="1" min="0"/>

            <!--Year of Birth no MUST-->
            <node key="settings.personal.birthyear" target="Settings.BirthYear" type="uint16" min="1900" max="2050"/>

            <!--Max HR MUST MUST-->
            <!-- Is default 200bpm ok?-->
            <node key="settings.personal.heartrate.max" target="Settings.MaxHR" type="uint8" min="30" max="240" unit="bpm"/>

            <!--Rest HR no MUST-->
            <node key="settings.personal.heartrate.rest" target="Settings.RestHR" type="uint8" min="30" max="240" unit="bpm"/>

            <!--Fitness level no MUST-->
            <node key="settings.personal.activitylevel" target="Settings.FitnessLevel" type="uint8" min="10" max="100" unit="fitness level"/>


            <!--UNIT SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 11.-->
            <!--TEMPERATURE °C / °F no MUST-->
            <node key="settings.device.units.temperature" target="Settings.TemperatureUnit" type="uint8" default="0"  max="1" min="0"/>

            <!--ALTI meter / feet no MUST-->
            <node key="settings.device.units.altitude" target="Settings.AltitudeUnit" type="uint8" default="0"  max="1" min="0"/>

            <!--SPEED meters/feet per second, minute, or hour no MUST-->
            <node key="settings.device.formats.speed" target="Settings.SpeedUnit" type="uint8" default="0"  max="3" min="0"/>

            <!--VERTICAL SPEED meters/feet per second, minute, or hour no MUST-->
            <node key="settings.device.units.verticalspeed" target="Settings.VerticalSpeedUnit" type="uint8" default="0"  max="3" min="0"/>

            <!--DISTANCE Km/miles meter / feet no MUST-->
            <node key="settings.device.units.distance" target="Settings.DistanceUnit" type="uint8" default="0"  max="1" min="0"/>

            <!--Height UNIT cm feet-->
            <node key="settings.device.units.height" target="Settings.HeightUnit" type="uint8" default="0" max="1" min="0"/>

            <!-- kg/lb no MUST -->
            <node key="settings.device.units.weight" target="Settings.WeightUnit" type="uint8" default="0"  max="1" min="0"/>

            <!--COMPASS degrees/mils Default: Degrees MILS only adjustable from MC -->
            <node key="settings.device.formats.compass" target="Settings.CompassUnit" type="uint8" default="0"  max="2" min="0"/>

            <!--PRESSURE hPa/inHg no MUST-->
            <node key="settings.device.units.airpressure" target="Settings.AirPressureUnit" type="uint8" default="0" max="1" min="0"/>

            <!--Energy consumption Kcal no MUST-->
            <!-- SDK & REST & MC IMPLEMENTATION MISSING!!!!-->

            <!--Unit system Metric/imperial * MUST MUST-->
            <node key="settings.device.units.mode" target="Settings.UnitsMode" type="uint8" default="0" max="2" min="0"/>

            <!--TIME format 12h/24h MUST MUST-->
            <node key="settings.device.time.format" target="Settings.Use24hClock" type="uint8" converter="invert" default="0"  max="1" min="0"/>

            <!--HR UNIT %/bmp-->
            <node key="settings.device.formats.heartrate" target="Settings.HRUnit" type="uint8" default="0"  max="1" min="0"/>

            <!--ButtonLockMode 0: all buttons, 1: actions only-->
            <node key="settings.device.buttonlock.timemode" target="Settings.TimeModeButtonLock" type="uint8" default="0" max="1" min="0"/>
            <node key="settings.device.buttonlock.sportmode" target="Settings.SportModeButtonLock" type="uint8" default="1" max="1" min="0"/>

            <!--Position Format Hddd.ddddd° , Hddd°mm.mmm’ , Hddd°mm’ss.s” , UTM, MGRS MUST Default: Hddd°mm.mmm’ MUST-->
            <!--Already among general settings-->

            <!--GPS Datum Currently valid datums Default: WGS84 Other Datums than WGS84 only adjustable from MC-->
            <!--Already among general settings-->

            <!--date format day&month, month&day, weekday MUST MUST-->
            <!--Already among general settings-->

            <!--Bike POD calibration coefficients-->
            <node key="settings.exercise.bikepodcalibcoeff" target="Settings.BikePODCalibration" type="float" default="1.0000" min="0.1000" max="2.0000" unit="float coeff"/>
            <node key="settings.exercise.bikepodcalibcoeff2" target="Settings.BikePODCalibration2" type="float" default="1.0000" min="0.1000" max="2.0000" unit="float coeff"/>
            <node key="settings.exercise.bikepodcalibcoeff3" target="Settings.BikePODCalibration3" type="float" default="1.0000" min="0.1000" max="2.0000" unit="float coeff"/>
            <!--Foot POD calibration coefficient-->
            <node key="settings.exercise.footpodcalibcoeff" target="Settings.FootPODCalibration" type="float" default="1.0000" min="0.1000" max="2.0000" unit="float coeff"/>
            <!--Foot POD automatic calibration-->
            <node key="settings.exercise.footpodautocalib" target="Settings.FootPODAutoCalibration" type="bool" default="1" min="0" max="1"/>
            <!--Swimming calibration coefficients-->
            <!--
             <node key="settings.exercise.swimmingstyle.other.classification1" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.other.classification2" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.other.classification3" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.other.classification4" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.butterfly.classification1" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.butterfly.classification2" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.butterfly.classification3" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.butterfly.classification4" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.back.classification1" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.back.classification2" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.back.classification3" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.back.classification4" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.breast.classification1" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.breast.classification2" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.breast.classification3" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.breast.classification4" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.free.classification1" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.free.classification2" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.free.classification3" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             <node key="settings.exercise.swimmingstyle.free.classification4" target="Settings.TBD" type="uint16" default="65535" min="0" max="65534"/>
             -->

            <node key="settings.exercise.powerpodautocalib" target="Settings.AutomaticBikePowerCalibration" type="uint8" default="1" max="1" min="0"/>

            <!-- Training plan source (0 - off, 1 - Manual from MovesCount) -->
            <node key="settings.sports.plans.source" target="Settings.UseTrainingProgram" type="uint8" default="0" min="0" max="1"/>
          </tree>


        <tree name="settings_sml2rest" format="2">

            <!--MAIN SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 147.-->

            <!--Set automatically (GPS TIME SYNC) On/off, time zone, auto daylight saving) MUST MUST-->
            <node key="sml.DeviceSettings.Time.GPSTimeKeeping" target="Settings.GPSTimeKeeping" type="uint8" default="0"  max="1" min="0">
                <value source="true" target="0"/>
                <value source="false" target="1"/>
            </node>

            <!--DATE format Dd/mm/yyyy or mm/dd/yyyy MUST MUST-->
            <node key="sml.DeviceSettings.Date.Format" target="Settings.DisplayDateMode" type="uint8" default="0"  max="1" min="0">
                <value source="DDMM" target="0"/>
                <value source="MMDD" target="1"/>
                <!-- NOT IN SML <value source="<TBD?>" target="2"/> --> <!-- Weekday -->
            </node>

            <!--Reference field missing from REST -->
            <!-- SDK IMPLEMENTATION MISSING!!!!-->

            <!--All on/Buttons off/All off MUST MUST-->
            <node key="sml.DeviceSettings.Audio.Mode" target="Settings.TonesMode" type="uint8" default="0"  max="2" min="0">
                 <value source="Buttons off" target="0"/>
                 <value source="All on" target="1"/>
                 <value source="All off" target="2"/>
            </node>

            <!--BACKLIGHT MODE Normal (activated from light button), Off (no light activation), Night (activated from any button), Toggle-->
            <node key="sml.DeviceSettings.Display.Backlight.Mode" target="Settings.BacklightMode" type="uint8" default="0" max="3" min="0">
                <value source="Normal" target="0"/>
                <value source="Off" target="1"/>
                <value source="Night" target="2"/>
                <value source="Toggle" target="3"/>
            	 <!-- NOT IN REST <value source="Manual" target="?"/> -->
                <!-- NOT IN REST <value source="Automatic" target="?"/> -->
            </node>

            <node key="sml.DeviceSettings.Display.Backlight.Color" target="Settings.BacklightColor" type="uint8" default="0" max="1" min="0">
                <value source="White" target="0"/>
                <value source="Red" target="1"/>
            </node>

            <!--BACKLIGHT BRIGHTNESS 0-100% MUST MUST-->
            <node key="sml.DeviceSettings.Display.Backlight.Brightness" target="Settings.BacklightBrightness" type="uint8" default="50" min="0" max="100"/>

            <!--Contrast 0-100% -->
            <node key="sml.DeviceSettings.Display.Contrast" target="Settings.DisplayBrightness" type="uint8" default="50" min="0" max="100"/>

            <!--COMPASS DECLINATION -90.0 - 90.0° MUST MUST-->
            <node key="sml.DeviceSettings.Compass.Declination" target="Settings.CompassDeclination" type="float" default="0.0" unit="degrees" min="-90.0" max="90.0" decimals="1" converter="complement"/> <!-- in MC east is negative and west positive !!! -->

            <!--ALTI-BARO MODE Alti, Baro, Automatic MUST MUST-->
            <node key="sml.DeviceSettings.AltiBaro.Profile" target="Settings.AltiBaroMode" type="uint8" default="2" min="0" max="2">
                <value source="Altitude" target="0"/>
                <value source="Barometer" target="1"/>
                <value source="Automatic" target="2"/>
            </node>
            <node key="sml.DeviceSettings.AltiBaro.StormAlarm" target="Settings.StormAlarming" type="uint8" default="0" max="1" min="0">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="sml.DeviceSettings.AltiBaro.FusedAltitude" target="Settings.FusedAltiDisabled" type="uint8" default="0" max="1" min="0">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>

            <node key="sml.DeviceSettings.EventsToUser.SunriseAlarm.Type" target="Settings.SunriseAlarming" type="uint8" default="0" max="1" min="0">
                <value source="Off" target="0"/>
                <value source="EveryDay" target="1"/>
            </node>
            <node key="sml.DeviceSettings.EventsToUser.SunriseAlarm.Time" target="Settings.SunriseAlarmingTime" type="uint16" default="0" min="0" max="7200"/>

            <node key="sml.DeviceSettings.EventsToUser.SunsetAlarm.Type" target="Settings.SunsetAlarming" type="uint8" default="0" max="1" min="0">
                <value source="Off" target="0"/>
                <value source="EveryDay" target="1"/>
            </node>
            <node key="sml.DeviceSettings.EventsToUser.SunsetAlarm.Time" target="Settings.SunsetAlarmingTime" type="uint16" default="0" min="0" max="7200"/>

            <!--Language 0 - 16 -->
            <node key="sml.DeviceSettings.Units.Language" target="Settings.Language" type="uint8" default="2" min="0" max="16">
                <value source="Dansk" target="0"/>
                <value source="Deutsch" target="1"/>
                <value source="English" target="2"/>
                <value source="Espanol" target="3"/>
                <value source="Francais" target="4"/>
                <value source="Italiano" target="5"/>
                <value source="Nederlands" target="6"/>
                <value source="Norsk" target="7"/>
                <value source="Portugues" target="8"/>
                <value source="Suomi" target="9"/>
                <value source="Svenska" target="10"/>
                <value source="Chinese" target="11"/>
                <value source="Japanese" target="12"/>
                <value source="Korean" target="13"/>
                <value source="Cestina" target="14"/>
                <value source="Polski" target="15"/>
                <value source="Russian" target="16"/>
            </node>

            <!-- DISPLAY INVERT Positive/Negative MUST MUST-->
            <node key="sml.DeviceSettings.Display.Invert" target="Settings.DisplayIsNegative" type="uint8" default="0" max="1" min="0">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>

            <!--Position format (Hddd.ddddd° , Hddd°mm.mmm’ , Hddd°mm’ss.s” , UTM, MGRS) MUST MUST-->
            <node key="sml.DeviceSettings.GpsPositionFormat" target="Settings.GPSPositionFormat" type="uint8" default="0" min="0" max="15">
                <value source="WGS84 d" target="0"/>
                <value source="WGS84 dm" target="1"/>
                <value source="WGS84 dms" target="2"/>
                <value source="UTM" target="3"/>
                <value source="MGRS" target="4"/>
                <value source="British (BNG)" target="5"/>
                <value source="Finnish (ETRS-TM35FIN)" target="6"/>
                <value source="Finnish (KKJ)" target="7"/>
                <value source="Irish (IG)" target="8"/>
                <value source="Swedish (RT90)" target="9"/>
                <value source="Swiss (CH1903)" target="10"/>
                <value source="UTM - NAD27 Alaska" target="11"/>
                <value source="UTM - NAD27 Conus" target="12"/>
                <value source="UTM - NAD83" target="13"/>
                <value source="NZTM2000" target="14"/>
            </node>

            <!--Navigation style / orientation-->
            <node key="sml.DeviceSettings.Units.Orientation" target="Settings.NavigationStyle" type="uint8" default="1" min="0" max="1">
                <value source="Orientation" target="1"/>
                <value source="HeadingUp" target="0"/>
            </node>

            <!--GPS Datum Currently valid datums Default: WGS84 Other Datums than WGS84 only adjustable from MC-->
            <!-- SDK and REST IMPLEMENTATION MISSING!!!!-->


            <!--PERSONAL SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 9.-->
            <node key="sml.DeviceSettings.Personal.Height" target="Settings.Height" type="float" min="0.5" max="3.0" unit="meters" decimals="3"/>

            <!--Weight MUST MUST-->
            <!-- Is default 75 kg ok? Is 10 - 300 kg range ok?-->
            <node key="sml.DeviceSettings.Personal.Weight" target="Settings.Weight" type="float" min="10" max="300" unit="kg" decimals="1"/>

            <!--Gender no MUST-->
            <node key="sml.DeviceSettings.Personal.Gender" target="Settings.IsMale" type="bool" max="1" min="0">
                <value source="Female" target="0"/>
                <value source="Male" target="1"/>
            </node>

            <!--Year of Birth no MUST-->
            <node key="sml.DeviceSettings.Personal.BirthDay" target="Settings.BirthYear" type="uint16" min="1900" max="2050" converter="dateYear"/>

            <!--Max HR MUST MUST-->
            <!-- Is default 200bpm ok?-->
            <node key="sml.DeviceSettings.Personal.MaxHR" target="Settings.MaxHR" type="uint8" min="30" max="240" unit="bpm"/>

            <!--Rest HR no MUST-->
            <node key="sml.DeviceSettings.Personal.RestHR" target="Settings.RestHR" type="uint8" min="30" max="240" unit="bpm"/>

            <!--Fitness level no MUST-->
            <node key="sml.DeviceSettings.Personal.ActivityLevel" target="Settings.FitnessLevel" type="uint8" min="10" max="100" unit="fitness level"/>


            <!--UNIT SETTINGS FROM BLUE BIRD MAIN FEATURES SPECIFICATION PAGE 11.-->
            <!--TEMPERATURE °C / °F no MUST-->
            <node key="sml.DeviceSettings.Units.Temperature" target="Settings.TemperatureUnit" type="uint8" default="0" max="1" min="0">
                <value source="C" target="0"/>
                <value source="F" target="1"/>
            </node>

            <!--ALTI meter / feet no MUST-->
            <node key="sml.DeviceSettings.Units.Altitude" target="Settings.AltitudeUnit" type="uint8" default="0" max="1" min="0">
                <value source="m" target="0"/>
                <value source="ft" target="1"/>
                <!-- NOT IN REST <value source="km" target="?"/> -->
                <!-- NOT IN REST<value source="mi" target="?"/> -->
            </node>

            <!--SPEED meters/feet per second, minute, or hour no MUST-->
            <!-- AMBIT takes this from the distance unit thus it is not used there as such. -->
            <node key="sml.DeviceSettings.Units.Speed" target="Settings.SpeedUnit" type="uint8" default="0" max="3" min="0"/>

            <!--VERTICAL SPEED meters/feet per second, minute, or hour no MUST-->
            <node key="sml.DeviceSettings.Units.VerticalSpeed" target="Settings.VerticalSpeedUnit" type="uint8" default="0" max="3" min="0">
                <value source="m/min" target="0"/>
                <value source="ft/min" target="1"/>
                <!-- NOT IN REST <value source="km/h" target="?"/> -->
                <!-- NOT IN REST <value source="mi/h" target="?"/> -->
                <!-- NOT IN REST <value source="m/s" target="?"/> -->
                <!-- NOT IN REST <value source="ft/s" target="?"/> -->
            </node>

            <!--DISTANCE Km/miles meter / feet no MUST-->
            <node key="sml.DeviceSettings.Units.Distance" target="Settings.DistanceUnit" type="uint8" default="0"  max="1" min="0">
                <value source="km" target="0"/>
                <value source="mi" target="1"/>
                <!-- NOT IN REST <value source="m" target="?"/> -->
                <!-- NOT IN REST <value source="ft" target="?"/> -->
            </node>

            <!--Height UNIT cm feet-->
            <node key="sml.DeviceSettings.Units.Height" target="Settings.HeightUnit" type="uint8" default="0" max="1" min="0">
                <value source="cm" target="0"/>
                <value source="ft" target="1"/>
                <!-- NOT IN REST <value source="m" target="?"/> -->
                <!-- NOT IN REST <value source="km" target="?"/> -->
                <!-- NOT IN REST <value source="mi" target="?"/> -->
            </node>

            <!-- kg/lb no MUST -->
            <node key="sml.DeviceSettings.Units.Weight" target="Settings.WeightUnit" type="uint8" default="0"  max="1" min="0">
                <value source="kg" target="0"/>
                <value source="lbs" target="1"/>
            </node>

            <!--COMPASS degrees/mils Default: Degrees MILS only adjustable from MC -->
            <node key="sml.DeviceSettings.Units.Compass" target="Settings.CompassUnit" type="uint8" default="0"  max="2" min="0">
                <value source="degree" target="0"/>
                <value source="mil" target="1"/>
                <value source="cardinal" target="2"/>
                <!-- NOT IN REST <value source="grad" target="?"/> -->
            </node>

            <!--PRESSURE hPa/inHg no MUST-->
            <node key="sml.DeviceSettings.Units.AirPressure" target="Settings.AirPressureUnit" type="uint8" default="0" max="1" min="0">
                <value source="hPa" target="0"/>
                <value source="inHg" target="1"/>
                <!-- NOT IN REST <value source="bar" target="?"/> -->
                <!-- NOT IN REST <value source="psi" target="?"/> -->
                <!-- NOT IN REST <value source="mbar" target="?"/> -->
            </node>

            <!--Energy consumption Kcal no MUST-->
            <!-- SDK & REST & MC IMPLEMENTATION MISSING!!!!-->

            <!--Unit system Metric/imperial * MUST MUST-->
            <node key="sml.DeviceSettings.Units.Mode" target="Settings.UnitsMode" type="uint8" default="0" max="2" min="0">
                <value source="Metric" target="0"/>
                <value source="Imperial" target="1"/>
                <value source="Advanced" target="2"/>
            </node>
            <!--TIME format 12h/24h MUST MUST-->
            <node key="sml.DeviceSettings.Time.Format" target="Settings.Use24hClock" type="uint8" default="0"  max="1" min="0">
                <value source="12h" target="0"/>
                <value source="24h" target="1"/>
            </node>

            <!--HR UNIT %/bmp-->
            <node key="sml.DeviceSettings.Units.Heartrate" target="Settings.HRUnit" type="uint8" default="0" max="1" min="0">
                <value source="bpm" target="0"/>
                <value source="%" target="1"/>
            </node>

            <!--ButtonLockMode 0: all buttons, 1: actions only-->
            <node key="sml.DeviceSettings.ButtonLock.TimeMode" target="Settings.TimeModeButtonLock" type="uint8" default="0" max="1" min="0">
                <value source="All buttons" target="0"/>
	         <value source="Actions only" target="1"/>
            </node>
            <node key="sml.DeviceSettings.ButtonLock.SportMode" target="Settings.SportModeButtonLock" type="uint8" default="1" max="1" min="0">
                <value source="All buttons" target="0"/>
                <value source="Actions only" target="1"/>
            </node>

            <!--Position Format Hddd.ddddd° , Hddd°mm.mmm’ , Hddd°mm’ss.s” , UTM, MGRS MUST Default: Hddd°mm.mmm’ MUST-->
            <!--Already among general settings-->

            <!--GPS Datum Currently valid datums Default: WGS84 Other Datums than WGS84 only adjustable from MC-->
            <!--Already among general settings-->

            <!--date format day&month, month&day, weekday MUST MUST-->
            <!--Already among general settings-->

            <!--Bike POD calibration coefficients-->
            <node key="sml.DeviceSettings.Pods.Pod[].Calib" index="1" target="Settings.BikePODCalibration" type="float" default="1.0" min="0.1" max="2.0" unit="float coeff" decimals="3">
                <!-- About ref: "field" is another field in the same array item (in this case same level as "Calib" and just below sequenced field)
                     and if the value of that field is "BikePod" corresponding target field is filled.
                     ref is currently limited to support fields just in the same data hierarchy/structure as the actual field and its supported value type is string only.
                     These limitations will/should be handled when such scenario surfaces.
                     Note! Index is fixed index of the item in a sequence. -->
                <ref field="Type" value="BikePOD"/>
            </node>
            <node key="sml.DeviceSettings.Pods.Pod[].Calib" index="2" target="Settings.BikePODCalibration2" type="float" default="1.0" min="0.1" max="2.0" unit="float coeff" decimals="3">
                <ref field="Type" value="BikePOD"/>
            </node>
            <node key="sml.DeviceSettings.Pods.Pod[].Calib" index="3" target="Settings.BikePODCalibration3" type="float" default="1.0" min="0.1" max="2.0" unit="float coeff" decimals="3">
                <ref field="Type" value="BikePOD"/>
            </node>

            <!--Foot POD calibration coefficient-->
            <node key="sml.DeviceSettings.Pods.Pod[].Calib" target="Settings.FootPODCalibration" type="float" default="1.0" min="0.1" max="2.0" unit="float coeff" decimals="3">
                <ref field="Type" value="FootPOD"/>
            </node>

            <node key="sml.DeviceSettings.Pods.Pod[].AutoCalib" target="Settings.FootPODAutoCalibration" type="bool" default="1" min="0" max="1">
                <ref field="Type" value="FootPOD"/>
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>

            <!--Swimming calibration coefficients-->
            <node key="sml.DeviceSettings.Sports.Swimming.StyleCoeff" target="Settings.SwimmingStyleCalibration" type="array" converter="listOfLists">
                <keyList item="Vectors.Item" type="int16"/>
                <fieldMatch name="Style">                <!-- Field in the "key" side list to be matched list index on the target side -->
                    <field value="Other" index="0"/>     <!-- Value of the field and index of it in target side array -->
                    <field value="Butterfly" index="1"/>
                    <field value="Backstroke" index="2"/>
                    <field value="Breaststroke" index="3"/>
                    <field value="Freestyle" index="4"/>
                    <field value="Drill" index="5"/>
                </fieldMatch>
            </node>

            <!-- NOT IN SML <node key="" target="Settings.AutomaticBikePowerCalibration" type="uint8" default="1" max="1" min="0"/>  -->

            <!-- Training plan source (0 - off, 1 - Manual from MovesCount) -->
            <node key="sml.DeviceSettings.Sports.Plans.Source" target="Settings.UseTrainingProgram" type="uint8" default="0" min="0" max="1">
                <value source="Off" target="0"/>
                <value source="Manual" target="1"/>
            </node>

           <!--Rule IDs-->
            <node key="sml.DeviceSettings.Rules" target="Settings.RuleIDs" type="array">
                <listItem key="Id" target="RuleIDs" type="int32"/>
            </node>

          <!-- Vibration mode (0 - off, 1- on, 2- buttons off from Movescount -->
          <node key="sml.DeviceSettings.Vibration.Mode" target="Settings.VibrationMode" type="uint8" default="0" min="0" max="2">
            <value source="Buttons off" target="2"/>
            <value source="All on" target="1"/>
            <value source="All off" target="0"/>
          </node>
        </tree>

        <tree name="member_sml2rest" format="2">
          <node key="sml.DeviceSettings.Personal.HeartRateZones.Moderate" target="HRZone1" type="uint8" min="0" max="100" unit="percent"/>
          <node key="sml.DeviceSettings.Personal.HeartRateZones.Hard" target="HRZone2" type="uint8" min="0" max="100" unit="percent"/>
          <node key="sml.DeviceSettings.Personal.HeartRateZones.VeryHard" target="HRZone3" type="uint8" min="0" max="100" unit="percent"/>
          <node key="sml.DeviceSettings.Personal.HeartRateZones.Maximal" target="HRZone4" type="uint8" min="0" max="100" unit="percent"/>
        </tree>

        <tree name="waypoints_sml2rest" format="2">
            <node key="sml.WayPoints.WayPoint.Name" target="Name" type="string"/>
            <node key="sml.WayPoints.WayPoint.RouteName" target="RouteName" type="string"/>
            <node key="sml.WayPoints.WayPoint.Timestamp" target="CreationLocalTime" type="string"/>
            <node key="sml.WayPoints.WayPoint.RouteIndex" target="RouteIndex" type="uint8"/>
            <node key="sml.WayPoints.WayPoint.Type" target="Type" type="uint8"/>
            <node key="sml.WayPoints.WayPoint.SubType" target="SubType" type="uint8"/>
            <node key="sml.WayPoints.WayPoint.TypeIndex" target="TypeIndex" type="uint8"/>
            <node key="sml.WayPoints.WayPoint.Flags" target="Flags" type="uint8"/>
            <node key="sml.WayPoints.WayPoint.Location.Latitude" target="Latitude" type="int32" unit="degrees"/>
            <node key="sml.WayPoints.WayPoint.Location.Longitude" target="Longitude" type="int32" unit="degrees"/>
            <node key="sml.WayPoints.WayPoint.Altitude" target="Altitude" type="float" unit="meters"/>
        </tree>

        <tree name="waypoint_sml2rest" format="2">
            <node key="Name" target="Name" type="string"/>
            <node key="RouteName" target="RouteName" type="string"/>
            <node key="Timestamp" target="CreationLocalTime" type="string"/>
            <node key="RouteIndex" target="RouteIndex" type="uint8"/>
            <node key="Type" target="Type" type="uint8"/>
            <node key="SubType" target="SubType" type="uint8"/>
            <node key="TypeIndex" target="TypeIndex" type="uint8"/>
            <node key="Flags" target="Flags" type="uint8"/>
            <node key="Location.Latitude" target="Latitude" type="float" unit="degrees"/>
            <node key="Location.Longitude" target="Longitude" type="float" unit="degrees"/>
            <node key="Altitude" target="Altitude" type="float" unit="meters"/>
        </tree>

        <tree name="waypoints">
            <node key="latitude" target="Latitude" type="float" unit="degrees"/>
            <node key="longitude" target="Longitude" type="float" unit="degrees"/>
            <node key="altitude" target="Altitude" type="float" unit="meters"/>
            <node key="name" target="Name" type="string"/>
            <node key="type" target="Type" type="uint8"/>
            <node key="timestamp" target="CreationLocalTime" type="string"/>
            <node key="TypeIndex" target="TypeIndex" type="uint8"/>
        </tree>

        <tree name="routepoints">
            <node key="latitude" target="Latitude" type="float" unit="degrees"/>
            <node key="longitude" target="Longitude" type="float" unit="degrees"/>
            <node key="altitude" target="Altitude" type="float" unit="meters" default="30000"/>
            <node key="type" target="Type" type="uint8" default="255"/>
        </tree>

        <tree name="hardware">
            <node key="hardware.firmware.uri" target="LatestFirmwareURI" type="string"/>
            <node key="hardware.firmware.version" target="LatestFirmwareVersion" type="string"/>
            <node key="hardware.device.name" target="DeviceName" type="string"/>
            <node key="hardware.device.version" target="Version" type="string"/>
        </tree>

        <tree name="firmware">
            <node key="firmware.binaryUri" target="BinaryURI" type="string"/>
            <node key="firmware.metadata" target="MetaData" type="string"/>
            <node key="firmware.version" target="Version" type="string"/>
        </tree>
        <tree name="move">
            <!-- how does the mapping work? -->
            <node key="ActivityType" target="ActivityID" type="uint8" />
            <node key="" target="DeviceName" type="string"/>
            <node key="" target="DeviceSerialNumber" type="string"/>
            <!-- required when saving track -->
            <node key="Distance" target="Distance" type="uint32" min="0" unit="meters"/>
            <!-- is there any min/max. Required? -->
            <node key="Duration" target="Duration" type="float" unit="seconds"/>
            <!-- Required -->
            <node key="DateTime" target="LocalStartTime" type="string"/>
            <!-- Required -->
            <node key="" target="SerialNumber" type="string"/>
            <!-- accuracy?-->
            <node key="" target="StartLatitude" type="float" unit="degrees"/>
            <!-- accuracy?-->
            <node key="" target="StartLongitude" type="float" unit="degrees"/>
            <node key="Energy" target="Energy" type="uint16" min="0" max="30000" unit="kcal"/>
            <!-- accuracy?, decimal?-->

            <node key="Altitude.Max" target="HighAltitude" type="float" min="-1000" max="10000"/>
            <!-- accuracy?, decimal?-->
            <node key="Altitude.Min" target="LowAltitude" type="float" min="-1000" max="10000"/>
            <!-- accuracy?, decimal?-->
            <node key="Ascent" target="AscentAltitude" type="float" unit="meters"/>
            <!-- accuracy?, decimal?-->
            <node key="Descent" target="DescentAltitude" type="float" unit="meters"/>
            <!-- accuracy?-->
            <node key="AscentTime" target="AscentTime" type="float" min="0" unit="seconds"/>
            <!-- accuracy?-->
            <node key="DescentTime" target="DescentTime" type="float" min="0" unit="seconds"/>
            <!-- accuracy?-->
            <node key="RecoveryTime" target="RecoveryTime" type="float" min="0" unit="seconds"/>
            <node key="" target="FlatTime" type="float"/>
            <node key="HR.Min" target="MinHR" type="uint8" min="0" max="255" unit="bpm"/>
            <node key="HR.Max" target="PeakHR" type="uint8" min="0" max="255" unit="bpm"/>
            <node key="HR.Avg" target="AvgHR" type="uint8" min="0" max="255" unit="bpm"/>
            <!-- check unit: m/s? -->
            <node key="Speed.Max" target="MaxSpeed" type="float" min="0" max="556" unit="m/s"/>-->
            <node key="Speed.Avg" target="AvgSpeed" type="float" min="0" max="556" unit="m/s"/>
            <!-- accuracy?, decimal?-->
            <node key="Temperature.Min" target="MinTemp" type="float" min="-100" max="100" unit="celsius"/>
            <!-- accuracy?, decimal?-->
            <node key="Temperature.Max" target="MaxTemp" type="float" min="-100" max="100" unit="celsius"/>
            <!-- accuracy?, decimal?-->
            <!--<node key="Temperature.Avg" target="AvgTemp" type="uint8" min="-100" max="100" unit="celsius"/>-->
            <node key="PeakTrainingEffect" target="PeakTrainingEffect" type="float" min="1" max="5"/>
            <node key="Cadence.Avg" target="AvgCadence" type="float" min="0" max="1000" unit="rpm"/>
            <node key="Cadence.Max" target="MaxCadence" type="float" min="0" max="1000" unit="rpm"/>
        </tree>
        <tree name="Sample">
            <node key="RelativePerformanceLevel" target="RelativePerformanceLevel" type="double"/>
            <node key="Altitude" target="Altitude" type="float" min="-1000" max="10000" unit="meters"/>
            <!-- check datatype? We need a converter for this, ie device internal datetime to YYYY-MM-DDTHH:MM:SS.t-->
            <node key="Time" target="LocalTime" type="string"/>
            <!-- What is MAX?-->
            <node key="Distance" target="Distance" type="uint32" min="0" unit="meters"/>
            <!-- check unit: m/s? -->
            <node key="Speed" target="Speed" type="float" min="0" max="556" unit="m/s"/>
            <node key="EnergyConsumption" target="EnergyConsumption" type="float" min="0" max="1000" unit="kcal/min"/>
            <node key="HR" target="HeartRate" type="uint16" min="0" max="300" unit="bpm"/>
            <!-- check datatype? -->
            <node key="Temperature" target="Temperature" type="float" min="-100" max="100" unit="celsius"/>
            <!-- do we need this? 0 = Pod change -->
            <node key="" target="DeviceEvent" type="uint16"/>
            <node key="VerticalSpeed" target="VerticalSpeed" type="float" min="-59" max="59" unit="m/s"/>
            <node key="SeaLevelPressure" target="SeaLevelPressure" type="uint16" min="850" max="1100" unit="hpa"/>
            <node key="Cadence" target="Cadence" type="float" min="0" max="1000" unit="rpm"/>
            <node key="BikePower" target="Power" type="uint16" min="0" max="2000" unit="watt"/>
        </tree>

        <tree name="MoveMark">
            <!-- which one of next two should be transfered, ie unnessary to transfer both? -->
            <node key="" target="SplitTimeFromPrevious" type="float"/>
            <node key="" target="SplitTimeFromStart" type="float"/>
            <!--Types: "0 = Manual Lap
             1 = Log stopped
             2 = ? what is this ?
             3 = Time interval lap
             4 = Warm-up interval lap
             5 = Distance lap
             6 = Log started"
             -->
            <node key="" target="Type" type="uint8"/>
        </tree>

        <tree name="Track">
            <!-- check datatype? -->
            <node key="" target="Trackpoint.Altitude" type="float"/>
            <node key="" target="Trackpoint.Bearing" type="float" unit="degrees"/>
            <node key="" target="Trackpoint.Latitude" type="float" unit="degrees"/>
            <node key="" target="Trackpoint.LocalTime" type="string"/>
            <node key="" target="Trackpoint.Longitude" type="float" unit="degrees"/>
            <node key="" target="Trackpoint.EHPE" type="float" unit="m"/>
        </tree>

        <tree name="multi_mode_sml" format="2">
			<node key="Name" target="Name" type="string"/>
            <node key="ID" target="CustomModeGroupID" type="int32"/>
            <node key="Type" target="ActivityID" type="uint16"/>
        </tree>

        <tree name="mode_sml" format="2">
            <node key="ShowInExerciseMenu" target="IsVisible" type="uint8" min="0" max="1">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="TypeID" target="ActivityID" type="uint16"/>
            <node key="Name" target="Name" type="string"/>
        </tree>

        <tree name="2nd_mode_sml" format="2">
            <node key="HeartRateLimits.Low" target="HRLimitLow" type="uint16"/>
            <node key="HeartRateLimits.High" target="HRLimitHigh" type="uint16"/>
            <node key="Display.Backlight.Mode" target="BacklightMode" type="uint8" default="4" min="0" max="4">
                <value source="Normal" target="0"/>
                <value source="Off" target="1"/>
                <value source="Night" target="2"/>
                <value source="Toggle" target="3"/>
                <value source="Automatic" target="4"/>
            </node>
            <node key="Display.Invert" target="DisplayIsNegative" type="uint8" default="2" min="0" max="2">
                <value source="No" target="0"/>
                <value source="Yes" target="1"/>
                <value source="Automatic" target="2"/>
            </node>
            <node key="AutoLap.Distance" target="AutolapDistance" type="uint16"/>
            <node key="QuickNavigation" target="ShowNavigationSelection" type="uint8" default="0" min="0" max="2">
                <value source="Off" target="0"/>
                <value source="ShowPOIs" target="1"/>
                <value source="ShowRoutes" target="2"/>
            </node>
            <node key="AutomaticScreenScrolling.Delay" target="AutoScrolling" type="uint16"/>
            <node key="RecordingInterval" target="RecordingInterval" type="uint16"/>
            <node key="AltiBaroProfile" target="AltiBaroMode" type="uint8" default="0" min="0" max="2">
                <value source="Altitude" target="0"/>
                <value source="Barometer" target="1"/>
                <value source="Automatic" target="2"/>
            </node>
            <node key="GPS.FixInterval" target="GPSInterval" type="float"/>
            <node key="PodsToSearch.HRBelt" target="UseHRBelt" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="PodsToSearch.FootPOD" target="UseFootPOD" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="PodsToSearch.BikePOD" target="UseBikePOD" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="PodsToSearch.CadencePOD" target="UseCadencePOD" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="PodsToSearch.PowerPOD" target="UsePowerPOD" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="AutoPause.Delay" target="AutoPause" type="uint16"/>
            <node key="AutoPause.Speed" target="AutoPauseSpeed" type="float"/>
            <node key="FusedSpeed" target="UseAccelerometer" type="uint8">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
        </tree>

        <tree name="sport_mode">
            <node key="name" target="Name" type="string"/>
            <node key="activityid" target="ActivityID" type="uint16"/>
            <node key="custommodegroupid" target="CustomModeGroupID" type="uint16"/>
            <node key="isvisible" target="IsVisible" type="bool"/>
        </tree>

        <tree name="custom_mode">
            <node key="settings.activityid" target="ActivityID" type="uint16"/>
            <node key="settings.custommodeid" target="CustomModeID" type="uint32"/>
            <node key="settings.name" target="Name" type="string"/>

            <node key="settings.usehw.hrbelt" target="UseHRBelt" type="bool"/>
            <node key="settings.usehw.pods" target="UsePODs" type="bool"/>
            <node key="settings.usehw.accelerometer" target="UseAccelerometer" type="bool"/>
            <node key="settings.usehw.powerpod" target="UsePowerPOD" type="bool"/>
            <node key="settings.usehw.cadencepod" target="UseCadencePOD" type="bool"/>
            <node key="settings.usehw.footpod" target="UseFootPOD" type="bool"/>
            <node key="settings.usehw.bikepod1" target="UseBikePOD" type="bool"/>

            <node key="settings.altibaromode" target="AltiBaroMode" type="uint8" default="2" min="0" max="2"/>

            <node key="settings.gpsinterval" target="GPSInterval" type="uint16"/>
            <node key="settings.recordinginterval" target="RecordingInterval" type="uint16"/>

            <node key="settings.autolap.distance" target="AutolapDistance" type="uint16"/>
            <node key="settings.autolap.use" target="UseAutolap" type="bool"/>

            <node key="settings.hrlimits.high" target="HRLimitHigh" type="uint8"/>
            <node key="settings.hrlimits.low" target="HRLimitLow" type="uint8"/>
            <node key="settings.hrlimits.use" target="UseHRLimits" type="bool"/>

            <node key="settings.intervaltimer.use" target="UseIntervals" type="bool"/>
            <node key="settings.intervaltimer.repetitions" target="IntervalRepetitions" type="uint16"/>
            <node key="settings.intervaltimer.interval1.time" target="Interval1Time" type="uint16"/>
            <node key="settings.intervaltimer.interval1.distance" target="Interval1Distance" type="uint16"/>
            <node key="settings.intervaltimer.interval2.time" target="Interval2Time" type="uint16"/>
            <node key="settings.intervaltimer.interval2.distance" target="Interval2Distance" type="uint16"/>

            <!-- added for gps pod: -->
            <node key="settings.autostart.use" target="UseAutomaticLogRecording" type="bool"/>
            <node key="settings.autopause.minutes" target="AutoPause" type="uint8" default="0" min="0" max="60"/>
            <node key="settings.autopause.speed" target="AutoPauseSpeed" type="float" default="0" min="0" max="556"/>
            <node key="settings.tonesmode" target="TonesMode" type="uint8" default="0"  max="2" min="0"/>
            <node key="settings.autoscrolling" target="AutoScrolling" type="uint16" default="0" min="0" max="60"/>

            <node key="settings.displaymode.backlightmode" target="BacklightMode" type="uint16" default="255" min="0" max="255"/>
            <node key="settings.displaymode.displayinvert" target="DisplayIsNegative" type="bool" default="255" min="0" max="255"/>

            <node key="settings.navigationselection" target="ShowNavigationSelection" type="uint8" default="0" min="0" max="2"/>
        </tree>

        <tree name="custom_mode_display">
            <node key="type" target="Type" type="uint16"/>
            <node key="row1" target="Row1" type="uint16"/>
            <node key="row2" target="Row2" type="uint16"/>
            <node key="requiresHRBelt" target="RequiresHRBelt" type="bool"/>
        </tree>

        <tree name="Plan" format="2">
            <node key="sml.Plans.Plan.ID" target="PlannedTrainingProgramMoveID" type="int32"/>
            <node key="sml.Plans.Plan.Date" target="StartTime" type="string" converter="dateOnly"/>
            <node key="sml.Plans.Plan.DailyOrdinal" target="Ordinal" type="uint8" min="0"/>
            <node key="sml.Plans.Plan.Duration" target="Duration" unit="minute" type="uint16" min="0" max="6000"/>
            <node key="sml.Plans.Plan.Distance" target="Distance" type="int32" min="0" max="9999000"/>
            <node key="sml.Plans.Plan.Intensity" target="Intensity" type="uint8" min="1" max="5">
                <value source="Easy" target="1"/>
                <value source="Moderate" target="2"/>
                <value source="Hard" target="3"/>
                <value source="VeryHard" target="4"/>
                <value source="Maximal" target="5"/>
            </node>
            <node key="sml.Plans.Plan.Activity.ID" target="ActivityID" type="uint16"/>
            <node key="sml.Plans.Plan.Activity.LocalizedName" target="ActivityNameLocalized" type="string"/>
            <node key="sml.Plans.Plan.Notes" target="Description" type="string"/>
        </tree>

        <tree name="rule" format="2">
            <node key="LastModifiedDate" target="LastModifiedDate" type="string"/>
            <node key="Name" target="Name" type="string"/>
            <node key="Description" target="Description" type="string"/>
            <node key="RuleID" target="RuleID" type="int32"/>
            <node key="SelfURI" target="SelfURI" type="string"/>
            <node key="Source" target="Source" type="string"/>
            <node key="Type" target="Type" type="string"/>
            <node key="TargetVirtualMachineVersion" target="TargetVirtualMachineVersion" type="string"/>
            <node key="Activity" target="ActivityID" type="int32" default="1" converter="activityMap"/>
            <node key="Category" target="Category" type="string" default="guidance"/>
            <node key="IsPublic" target="IsPublic" type="bool" default="0">
                <value source="false" target="0"/>
                <value source="true" target="1"/>
            </node>
            <node key="OutputFormat" target="OutputFormat" type="string"/>
        </tree>

        <tree name="timeline" format="2">
            <node key="TimeISO8601" target="TimeISO8601" type="string"/>
            <node key="Source" target="Source" type="string"/>
            <node key="Attributes" target="Attributes" type="string"/>
            <node key="Altitude" target="Location.Altitude" type="double" unit="meters"/>
            <node key="EHPE" target="Location.EHPE" type="double" min="0" unit="meters"/>
            <node key="Latitude" target="Location.Latitude" type="double" decimals="6"/>
            <node key="Longitude" target="Location.Longitude" type="double" decimals="6"/>
        </tree>

    </adapter>

</adapters>
