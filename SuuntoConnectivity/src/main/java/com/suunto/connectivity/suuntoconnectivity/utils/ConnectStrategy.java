package com.suunto.connectivity.suuntoconnectivity.utils;

import android.bluetooth.BluetoothDevice;
import androidx.annotation.NonNull;

import androidx.annotation.Nullable;
import org.jdeferred.Promise;

/**
 * Connect strategy determines autoconnect, discovery and timeout parameters
 * used when connecting. Can also introduce additional behavior like retry logic.
 */
public interface ConnectStrategy {

    interface ConnectDelegate {
        /**
         * Does the actual connecting. Called by connect strategy.
         *
         * @param autoConnect Use autoconnect
         * @param discover    Turn bluetooth discovery on during connecting
         * @param timeout     Timeout in milliseconds
         * @return Promise
         */
        Promise<Void, Throwable, Object> connect(
                boolean autoConnect, boolean discover, long timeout);
    }

    /**
     * Connects to the remote device.
     *
     * @param bluetoothDevice Bluetooth device to connect to
     * @param connectDelegate Connect delegate, does the actual connecting
     * @return Promise
     */
    Promise<Void, Throwable, Object> connect(@NonNull BluetoothDevice bluetoothDevice,
                                             @NonNull ConnectDelegate connectDelegate);

    /**
     * Connects to the remote device.
     *
     * @param address Address of the device to connect to
     * @param connectDelegate Connect delegate, does the actual connecting
     * @return Promise
     */
    Promise<Void, Throwable, Object> connect(@NonNull String address,
                                             @NonNull ConnectDelegate connectDelegate);

    /**
     * Checks if connecting is possible to remote bluetooth device.
     * @param bluetoothDevice Bluetooth device to connect to
     * @return True if connecting is possible
     */
    boolean canConnect(@NonNull BluetoothDevice bluetoothDevice);

    /**
     * Is initial connect attempt?
     *
     * @return True if initial connect. Null in not unknown.
     */
    @Nullable
    Boolean isInitialConnect();
}
