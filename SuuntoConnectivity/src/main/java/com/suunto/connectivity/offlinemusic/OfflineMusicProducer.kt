package com.suunto.connectivity.offlinemusic

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.AddOrUpdatePlayListQuery
import com.suunto.connectivity.repository.commands.AddOrUpdatePlayListResponse
import com.suunto.connectivity.repository.commands.DeletePlayListQuery
import com.suunto.connectivity.repository.commands.DeletePlayListResponse
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.GetAllSongQuery
import com.suunto.connectivity.repository.commands.GetAllSongResponse
import com.suunto.connectivity.repository.commands.GetOfflineMusicInfoQuery
import com.suunto.connectivity.repository.commands.GetOfflineMusicInfoResponse
import com.suunto.connectivity.repository.commands.GetOfflineMusicVersionQuery
import com.suunto.connectivity.repository.commands.GetOfflineMusicVersionResponse
import com.suunto.connectivity.repository.commands.GetPlayListDetailQuery
import com.suunto.connectivity.repository.commands.GetPlayListDetailResponse
import com.suunto.connectivity.repository.commands.GetPlayListsQuery
import com.suunto.connectivity.repository.commands.GetPlayListsResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.repository.commands.SortPlayListsQuery
import com.suunto.connectivity.repository.commands.SortPlayListsResponse
import com.suunto.connectivity.repository.commands.SubscribeMusicUpdateQuery
import com.suunto.connectivity.repository.commands.SubscribeMusicUpdateResponse
import com.suunto.connectivity.util.handleDeviceSpecificQuery
import com.suunto.connectivity.util.handleDeviceSpecificQueryObservable
import com.suunto.connectivity.watch.WatchBt
import rx.Observable
import rx.Single

class OfflineMusicProducer(
    private val suuntoRepositoryService: SuuntoRepositoryService,
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean {
        return messageType in listOf(
            SuuntoRepositoryService.MSG_GET_MUSIC_PLAY_LISTS,
            SuuntoRepositoryService.MSG_GET_MUSIC_ALL_SONG_DETAIL,
            SuuntoRepositoryService.MSG_GET_MUSIC_PLAYLIST_DETAIL,
            SuuntoRepositoryService.MSG_GET_MUSIC_INFO,
            SuuntoRepositoryService.MSG_ADD_OR_UPDATE_PLAYLIST,
            SuuntoRepositoryService.MSG_DELETE_PLAYLIST,
            SuuntoRepositoryService.MSG_SORT_PLAYLISTS,
            SuuntoRepositoryService.MSG_SUBSCRIBE_MUSIC_UPDATE,
            SuuntoRepositoryService.MSG_GET_OFFLINE_MUSIC_VERSION
        )
    }

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> {
        return when (messageType) {
            SuuntoRepositoryService.MSG_GET_MUSIC_PLAY_LISTS ->
                handleQuery<GetPlayListsQuery>(bundle) { watchBt, _ ->
                    watchBt.offlineMusicSettings?.getAllPlayList()?.value?.map {
                        it as Response
                    } ?: Single.just(GetPlayListsResponse(emptyList()))
                }

            SuuntoRepositoryService.MSG_GET_MUSIC_ALL_SONG_DETAIL ->
                handleQuery<GetAllSongQuery>(bundle) { watchBt, _ ->
                    watchBt.offlineMusicSettings?.getAllSongPlayListDetail()?.value?.map {
                        it as Response
                    } ?: Single.just(GetAllSongResponse(PlayList()))
                }

            SuuntoRepositoryService.MSG_GET_MUSIC_PLAYLIST_DETAIL ->
                handleQuery<GetPlayListDetailQuery>(bundle) { watchBt, getPlayListDetailQuery ->
                    val queryPlayListId = getPlayListDetailQuery.playListId
                    watchBt.offlineMusicSettings?.getCustomisePlayListDetail(queryPlayListId)?.value?.map {
                        it as Response
                    } ?: Single.just(GetPlayListDetailResponse(PlayList()))
                }

            SuuntoRepositoryService.MSG_GET_MUSIC_INFO ->
                handleQuery<GetOfflineMusicInfoQuery>(bundle) { watchBt, musicInfoQuery ->
                    val musicKey = musicInfoQuery.key
                    watchBt.offlineMusicSettings?.getOfflineMusicInfo(musicKey)?.value?.map {
                        it as Response
                    } ?: Single.just(GetOfflineMusicInfoResponse(MusicInfo()))
                }

            SuuntoRepositoryService.MSG_ADD_OR_UPDATE_PLAYLIST ->
                handleQuery<AddOrUpdatePlayListQuery>(bundle) { watchBt, addOrUpdatePlayListQuery ->
                    val isUpdate = addOrUpdatePlayListQuery.isUpdate
                    val playList = addOrUpdatePlayListQuery.playList
                    watchBt.offlineMusicSettings?.addOrUpdateCustomizePlayList(playList, isUpdate)
                        ?.andThen(Single.just(AddOrUpdatePlayListResponse(true)))
                        ?: Single.just(AddOrUpdatePlayListResponse(false))
                }

            SuuntoRepositoryService.MSG_DELETE_PLAYLIST ->
                handleQuery<DeletePlayListQuery>(bundle) { watchBt, deletePlayListQuery ->
                    val playListId = deletePlayListQuery.playListId
                    watchBt.offlineMusicSettings?.deleteCustomizePlayList(playListId)
                        ?.andThen(Single.just(DeletePlayListResponse(true)))
                        ?: Single.just(DeletePlayListResponse(false))
                }

            SuuntoRepositoryService.MSG_SORT_PLAYLISTS ->
                handleQuery<SortPlayListsQuery>(bundle) { watchBt, sortPlayListsQuery ->
                    watchBt.offlineMusicSettings?.sortPlayLists(sortPlayListsQuery.sorts)
                        ?.andThen(Single.just(SortPlayListsResponse(true)))
                        ?: Single.just(SortPlayListsResponse(false))
                }

            SuuntoRepositoryService.MSG_SUBSCRIBE_MUSIC_UPDATE -> handleQueryObservable<SubscribeMusicUpdateQuery>(
                bundle
            ) { watchBt, _ ->
                watchBt.subscribeOfflineMusicUpdate()
                    .map { SubscribeMusicUpdateResponse(updated = it) }
            }

            SuuntoRepositoryService.MSG_GET_OFFLINE_MUSIC_VERSION -> handleQuery<GetOfflineMusicVersionQuery>(
                bundle
            ) { watchBt, _ ->
                watchBt.offlineMusicSettings?.getOfflineMusicVersion()?.value?.map {
                    GetOfflineMusicVersionResponse(it)
                } ?: Single.just(GetOfflineMusicVersionResponse(0))
            }

            else -> Observable.just(ErrorResponse("Unknown query"))
        }
    }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Single<Response>
    ): Observable<Response> =
        handleDeviceSpecificQuery(suuntoRepositoryService.activeDevices, bundle, handler)

    private fun <T : DeviceSpecificQuery> handleQueryObservable(
        bundle: Bundle,
        handler: (WatchBt, T) -> Observable<Response>
    ): Observable<Response> =
        handleDeviceSpecificQueryObservable(suuntoRepositoryService.activeDevices, bundle, handler)
}
