package com.suunto.connectivity.ngBleManager;

import com.suunto.connectivity.suuntoconnectivity.BleCentral;
import com.suunto.connectivity.suuntoconnectivity.DeviceHandle;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityClient;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityListener;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;

import java.util.HashSet;
import java.util.Set;

import timber.log.Timber;

public class NgBLECentral extends BleCentral implements SuuntoConnectivityListener {

    private final Set<DeviceHandle> foundDevices;
    private NgBleCentralListener ngBleCentralListener;

    public NgBLECentral(SuuntoConnectivityClient suuntoConnectivityClient) {
        super(suuntoConnectivityClient, true);

        foundDevices = new HashSet<>();

        suuntoConnectivityClient.setNgListener(this);
    }

    void setNgBleCentralListener(NgBleCentralListener ngBleCentralListener) {
        this.ngBleCentralListener = ngBleCentralListener;
    }

    int dataWrite(int handle, byte[] data, long wbLocalDataPointer) {
        int result = dataWrite(handle, data);

        // TODO: should we return without waiting and call dataSent when the write completes?
        if (result == BleCore.BLE_OK && ngBleCentralListener != null) {
            ngBleCentralListener.dataSent(wbLocalDataPointer, true);
        }

        return result;
    }

    @Override
    public void onDeviceFound(DeviceHandle deviceHandle) {
        synchronized (foundDevices) {
            if (foundDevices.contains(deviceHandle)) {
                Timber.d("Device already found, handle = %s", deviceHandle);
                return;
            }

            foundDevices.add(deviceHandle);
        }

        if (ngBleCentralListener != null) {
            ngBleCentralListener.deviceFound(deviceHandle.getHandle());
        }
    }

    @Override
    public void onDeviceLostSpontaneously(DeviceHandle deviceHandle) {
        onDeviceLost(deviceHandle);
    }

    @Override
    public void onDeviceLostAsPerCommand(DeviceHandle deviceHandle) {
        onDeviceLost(deviceHandle);
    }

    @Override
    public void onDataAvailable(DeviceHandle deviceHandle) {
        if (ngBleCentralListener != null) {
            ngBleCentralListener.dataAvailableCallback(deviceHandle.getHandle());
        }
    }

    private void onDeviceLost(DeviceHandle deviceHandle) {
        synchronized (foundDevices) {
            if (!foundDevices.contains(deviceHandle)) {
                // Device already lost
                return;
            }

            foundDevices.remove(deviceHandle);
        }

        if (ngBleCentralListener != null) {
            ngBleCentralListener.deviceLost(deviceHandle.getHandle());
        }
    }
}
