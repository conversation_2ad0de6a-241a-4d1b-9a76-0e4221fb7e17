package com.suunto.connectivity.repository

import android.content.SharedPreferences
import com.suunto.connectivity.WeatherPreferences
import com.suunto.connectivity.sync.SynchronizerFinalizer
import com.suunto.connectivity.watch.WatchBt
import timber.log.Timber
import javax.inject.Inject
import androidx.core.content.edit

class SynchronizerPrefsFinalizer @Inject constructor(
    @WeatherPreferences private val weatherPreferences: SharedPreferences
) : SynchronizerFinalizer {
    override fun cleanup(watchBt: WatchBt) {
        Timber.d("Cleaning up preferences for watch %s", watchBt.serial)
        weatherPreferences.edit { remove(watchBt.serial) }
    }
}
