package com.suunto.connectivity.repository;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringDef;

import com.stt.android.utils.NearbyDevicesUtilsKt;
import com.suunto.connectivity.ngBleManager.NgBleManager;
import com.suunto.connectivity.ngBleManager.NgBleManagerException;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothOffException;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BleException;
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason;
import com.suunto.connectivity.watch.WatchBt;
import com.suunto.connectivity.watch.WatchConnector;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class ConnectionAnalyticsSequence {
    private static final String BLE_OFF_ERROR = "BluetoothOffError";
    private static final String BLE_CONNECTION_LOST_ERROR = "BleConnectionLostError";
    private static final String NG_BLE_MANAGER_EXCEPTION = "NgBLeManagerException";
    private static final String START_DATA_NOTIFY_ERROR = "StartDataNotifyError";
    private static final String GENERAL_ERROR = "GeneralError";
    private static final String MDS_FAILED_TO_CONNECT_ERROR = "MdsFailedToConnectError";
    private static final String MDS_CONNECT_TIMEOUT_ERROR = "MdsConnectTimeOutError";

    @StringDef({
        BLE_OFF_ERROR,BLE_CONNECTION_LOST_ERROR, GENERAL_ERROR, START_DATA_NOTIFY_ERROR,
        MDS_FAILED_TO_CONNECT_ERROR, NG_BLE_MANAGER_EXCEPTION
    })
    @Retention(RetentionPolicy.SOURCE)
    @interface AnalyticsConnectionError {
    }

    private static final String SERVICE_STARTING = "ServiceStarting";
    private static final String INITIAL_CONNECT = "InitialConnect";
    private static final String WATCH_DISCONNECTED = "WatchDisconnected";
    private static final String BT_TURNED_ON = "BtTurnedOn";
    private static final String CONNECTION_INSTABILITY_DELAY_PASSED = "ConnectionInstabilityPassed";
    private static final String CONNECTION_INSTABILITY_CLEARED = "ConnectionInstabilityCleared";
    private static final String ANCS_SERVICE_STATUS_CHANGED = "ANCSServiceStatusChange";

    @StringDef({
        SERVICE_STARTING, INITIAL_CONNECT, WATCH_DISCONNECTED, BT_TURNED_ON,
        CONNECTION_INSTABILITY_DELAY_PASSED, CONNECTION_INSTABILITY_CLEARED,
        ANCS_SERVICE_STATUS_CHANGED
    })
    @Retention(RetentionPolicy.SOURCE)
    @interface AnalyticsConnectReason {
    }

    private WatchBt watchBt;
    private BluetoothAdapter bluetoothAdapter;
    private long startTimestamp;
    private PairingState deviceAdvertisedPairingState;
    private PairingState phonePairingStateBeforeConnect;
    private final ConnectSuccessRateCounter connectSuccessRateCounter;

    ConnectionAnalyticsSequence(WatchBt watchBt, BluetoothAdapter bluetoothAdapter,
        ConnectSuccessRateCounter connectSuccessRateCounter) {
        this.watchBt = watchBt;
        this.bluetoothAdapter = bluetoothAdapter;
        this.connectSuccessRateCounter = connectSuccessRateCounter;
    }

    public void connectingStarted(PairingState deviceAdvertisedPairingState) {
        this.deviceAdvertisedPairingState = deviceAdvertisedPairingState;
        this.phonePairingStateBeforeConnect = getWatchPairingState();
    }

    public void connectingStarted(PairingState deviceAdvertisedPairingState,
        PairingState phonePairingStateBeforeConnect) {
        this.deviceAdvertisedPairingState = deviceAdvertisedPairingState;
        this.phonePairingStateBeforeConnect = getWatchPairingState();
    }

    public void connectionAttemptStarted() {
        startTimestamp = System.currentTimeMillis();
    }

    public void connectionAttemptFailed(Context context,
        @NonNull Throwable throwable,
        ConnectMetadata connectMetadata,
        boolean alreadyConnected,
        boolean invalidPacketsDetected) {
        String errorType = mapConnectExceptionToErrorTypeString(throwable);
        String analyticsConnectReason =
            mapConnectReasonToAnalyticsString(connectMetadata.getConnectReason());
        String errorMessage =
            mapConnectExceptionToErrorMessage(throwable, GENERAL_ERROR.equals(errorType));
        if (errorMessage == null) {
            errorMessage = "";
        }
        long attemptDuration = (System.currentTimeMillis() - startTimestamp) / 1000;
        String deviceModel =
            AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(
                watchBt.getSuuntoBtDevice().getDeviceType());
        AnalyticsUtils.sendConnectionErrorEvent(
            context,
            errorType,
            analyticsConnectReason,
            errorMessage,
            connectMetadata.getTotalConnectAttempts(),
            attemptDuration,
            deviceAdvertisedPairingState,
            phonePairingStateBeforeConnect,
            getWatchPairingState(),
            alreadyConnected,
            connectSuccessRateCounter.getConnectSuccessRateData(watchBt.getMacAddress()),
            connectMetadata.getConnectReason() == ConnectReason.InitialConnect,
            deviceModel,
            invalidPacketsDetected);
    }

    public void pairingConnectionSucceeded(Context context,
        @Nullable String previousModel,
        @Nullable String previousSerial) {
            AnalyticsUtils.sendWatchPairedAmplitudeEvent(
                context,
                watchBt,
                deviceAdvertisedPairingState,
                phonePairingStateBeforeConnect,
                previousModel,
                previousSerial);
    }

    public void pairingConnectionFailed(Context context) {
        AnalyticsUtils.sendWatchPairingErrorAmplitudeEvent(
            context,
            watchBt,
            deviceAdvertisedPairingState,
            phonePairingStateBeforeConnect);
    }

    private boolean isBluetoothEnabled() {
        return bluetoothAdapter.isEnabled();
    }

    @SuppressLint("MissingPermission")
    public PairingState getWatchPairingState() {
        try {
            if (!isBluetoothEnabled() || !NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(watchBt.context)) {
                return PairingState.Unknown;
            }
            if (watchBt.getSuuntoBtDevice().getDeviceType().isDataLayerDevice()) {
                // For data layer device pairing state is for now defined as unknown because this
                // process is not BT connected into device directly.
                // Todo: Study how pairing state should be correctly resolved for data layer device.
                return PairingState.Unknown;
            }
            BluetoothDevice btDevice = bluetoothAdapter.getRemoteDevice(
                watchBt.getSuuntoBtDevice().getMacAddress());
            return (btDevice.getBondState() == BluetoothDevice.BOND_BONDED) ?
                PairingState.Paired : PairingState.Unpaired;
        } catch (Exception e) {

            // On any exception return Unknown.
            return PairingState.Unknown;
        }
    }

    private boolean wasUnpaired() {
        return deviceAdvertisedPairingState == PairingState.Unpaired
            || phonePairingStateBeforeConnect == PairingState.Unpaired;
    }

    private static @AnalyticsConnectReason
    String mapConnectReasonToAnalyticsString(
        ConnectReason connectReason) {

        switch (connectReason) {
            case ServiceStarting:
                return SERVICE_STARTING;
            case InitialConnect:
                return INITIAL_CONNECT;
            case WatchDisconnected:
                return WATCH_DISCONNECTED;
            case BtTurnedOn:
                return BT_TURNED_ON;
            case ConnectionInstabilityDelayPassed:
                return CONNECTION_INSTABILITY_DELAY_PASSED;
            case ConnectionInstabilityCleared:
                return CONNECTION_INSTABILITY_CLEARED;
            case ANCSServiceStatusChange:
                return ANCS_SERVICE_STATUS_CHANGED;
            default:
                return null;
        }
    }

    private static String mapConnectExceptionToErrorTypeString(Throwable throwable) {
        if (throwable instanceof BluetoothOffException) {
            return BLE_OFF_ERROR;
        } else if (throwable instanceof BleException) {
            return ((BleException) throwable).getAnalyticsTag();
        } else if (throwable instanceof NgBleManager.BleConnectionLostError) {
            return BLE_CONNECTION_LOST_ERROR;
        } else if (throwable instanceof NgBleManager.StartDataNotifyError) {
            return START_DATA_NOTIFY_ERROR;
        } else if (throwable instanceof WatchConnector.MdsConnectTimeoutError) {
            return MDS_CONNECT_TIMEOUT_ERROR;
        } else if (throwable instanceof WatchConnector.MdsFailedToConnectError) {
            return MDS_FAILED_TO_CONNECT_ERROR;
        } else if (throwable instanceof NgBleManagerException) {
            return NG_BLE_MANAGER_EXCEPTION;
        }
        return GENERAL_ERROR;
    }

    private static @AnalyticsConnectionError
    @Nullable
    String mapConnectExceptionToErrorMessage(Throwable throwable, boolean isGeneralError) {
        if (throwable instanceof NgBleManager.StartDataNotifyError) {
            Exception bleException =
                ((NgBleManager.StartDataNotifyError) throwable).getBleException();
            if (bleException != null) {
                return bleException.toString();
            }
            return throwable.toString();
        }
        if (isGeneralError) {
            return throwable.toString();
        } else {
            return throwable.getMessage();
        }
    }
}
