package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.SystemClock;
import android.text.format.DateUtils;
import androidx.core.content.ContextCompat;
import com.suunto.connectivity.repository.stateMachines.base.StateMachineImplementation;
import timber.log.Timber;

/**
 * Helper class used with Legacy watches by ReconnectState. Uses AlarmManager to wake up phone and
 * LegacyDeviceRefreshAlarm trigger. LegacyDeviceRefreshAlarm will make reconnectState to cancel
 * current connection attempt and restart new connection attempt.
 */
public class LegacyDeviceConnectRefresher extends BroadcastReceiver {

    private static final long RECONNECT_REFRESH_INTERVAL_MILLIS = 20 * DateUtils.MINUTE_IN_MILLIS;
    private static final String RECONNECT_REFRESH_INTENT =
        "com.suunto.connectivity.repository.RECONNECT_REFRESH";

    final private Context context;
    final private StateMachineImplementation stateMachine;
    private boolean started;

    public LegacyDeviceConnectRefresher(Context context,
        StateMachineImplementation stateMachine) {
        this.context = context;
        this.stateMachine = stateMachine;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        Timber.d("Reconnect refresh alarm went on!");
        stateMachine.fire(Triggers.LegacyDeviceRefreshAlarm);
    }

    public synchronized void setupRefresh() {
        if (started) {
            return;
        }
        started = true;
        Timber.d("New reconnect refresh setup.");
        ContextCompat.registerReceiver(
            context,
            this,
            new IntentFilter(RECONNECT_REFRESH_INTENT),
            ContextCompat.RECEIVER_EXPORTED
        );
        AlarmManager alarmManager =
            (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            PendingIntent
                refreshPendingIntent = PendingIntent.getBroadcast(context, 0,
                new Intent(RECONNECT_REFRESH_INTENT), PendingIntent.FLAG_IMMUTABLE);
            alarmManager.setAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP,
                SystemClock.elapsedRealtime() + RECONNECT_REFRESH_INTERVAL_MILLIS,
                refreshPendingIntent);
        }
    }

    public synchronized void cancelRefresh() {
        if (!started) {
            return;
        }
        started = false;
        Timber.d("Reconnect refresh cancel request.");
        AlarmManager alarmManager =
            (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            PendingIntent
                refreshPendingIntent = PendingIntent.getBroadcast(context, 0,
                new Intent(RECONNECT_REFRESH_INTENT), PendingIntent.FLAG_IMMUTABLE);
            alarmManager.cancel(refreshPendingIntent);
        }
        context.unregisterReceiver(this);
    }
}
