package com.suunto.connectivity.repository.stateMachines.connectionStateMachine

import rx.Observable
import rx.functions.Func1
import java.util.concurrent.TimeUnit

/**
 * Class to be used with Rx retryWhen method for re-subscribing observable
 * maxRetries -times with retryDelaySeconds -seconds timeout between
 * re-subscribes.
 */
class RetryWithDelay(private val maxRetries: Int, private val retryDelaySeconds: Int) :
    Func1<Observable<out Throwable>, Observable<*>> {
    private var retryCount: Int = 0

    override fun call(attempts: Observable<out Throwable>): Observable<*> {
        return attempts
            .flatMap { throwable ->
                if (++retryCount < maxRetries) {
                    // When this Observable calls onNext, the original
                    // Observable will be retried (i.e. re-subscribed).
                    Observable.timer(
                        retryDelaySeconds.toLong(),
                        TimeUnit.SECONDS
                    )
                } else {
                    // Max retries hit. Just pass the error along.
                    Observable.error<Any>(throwable)
                }
            }
    }
}
