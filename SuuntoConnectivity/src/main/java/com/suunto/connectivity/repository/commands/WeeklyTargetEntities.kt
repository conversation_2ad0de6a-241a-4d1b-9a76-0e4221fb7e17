package com.suunto.connectivity.repository.commands

import android.annotation.SuppressLint
import androidx.annotation.RestrictTo
import com.suunto.connectivity.repository.SuuntoRepositoryService
import kotlinx.parcelize.Parcelize

// Query and response for setting the weekly target duration
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class SetWeeklyTargetDurationQuery(
    val macAddress: String,
    val weeklyTargetDuration: Float
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SET_WEEKLY_TARGET_DURATION
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class SetWeeklyTargetDurationResponse(
    val isSuccessful: Boolean
) : Response

// Query and response for getting the weekly target duration
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetWeeklyTargetDurationQuery(
    val macAddress: String
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_WEEKLY_TARGET_DURATION
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetWeeklyTargetDurationResponse(
    val weeklyTargetDuration: Float
) : Response
