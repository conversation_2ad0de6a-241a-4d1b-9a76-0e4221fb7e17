package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import android.content.Context;
import android.content.SharedPreferences;
import com.suunto.connectivity.repository.ConnectSuccessRateCounter;
import com.suunto.connectivity.repository.ConnectionAnalytics;
import com.suunto.connectivity.repository.ConnectionAnalyticsSequence;
import com.suunto.connectivity.repository.PairingState;
import com.suunto.connectivity.repository.SuuntoRepositoryUtils;
import com.suunto.connectivity.repository.stateMachines.base.Transition;
import com.suunto.connectivity.repository.stateMachines.base.Trigger;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.States.InitialConnecting;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason;
import com.suunto.connectivity.watch.WatchBt;
import rx.Subscription;
import timber.log.Timber;

/**
 * Initial connect state, which us used when connection is requested directly by the
 * end user. Will try to connect one time.
 */
public class InitialConnectingState extends ConnectionStateBase {

    public enum EntryTriggers implements Trigger {
        Connect
    }

    public enum ExitTriggers implements Trigger {
        Connected,
        ConnectFailed,
    }

    private Subscription connectSubscription;
    private final WatchBt watchBt;
    private final ConnectionAnalytics analytics;
    private final Context context;
    private final SharedPreferences sharedPreferences;

    InitialConnectingState(WatchBt watchBt,
        ConnectionAnalytics analytics, Context context,
        SharedPreferences sharedPreferences) {
        super(InitialConnecting.name());

        this.watchBt = watchBt;
        this.analytics = analytics;
        this.context = context;
        this.sharedPreferences = sharedPreferences;
    }

    @Override
    protected <TArg> void onEntry(TArg arg, Transition transition) {
        Timber.d("Entry %s", transition.toString());
        Timber.d("Initial connect watch %s", watchBt.getMacAddress());

        if (arg instanceof PairingState) {
            final ConnectionAnalyticsSequence analyticsSequence =
                analytics.createNewSequence(watchBt);
            final ConnectSuccessRateCounter analyticsSuccessRateCounter =
                analytics.getConnectSuccessRateCounter();
            final PairingState deviceAdvertisedPairingState = (PairingState) arg;
            final PairingState phonePairingStateBeforeConnect =
                analyticsSequence.getWatchPairingState();
            final ConnectMetadata connectMetadata =
                new ConnectMetadata(ConnectReason.InitialConnect,
                    true,
                    deviceAdvertisedPairingState, phonePairingStateBeforeConnect);
            analyticsSequence.connectingStarted(deviceAdvertisedPairingState,
                phonePairingStateBeforeConnect);
            boolean forAnalyticsAlreadyConnected = watchBt.isAlreadyConnected();
            connectSubscription =
                watchBt.connect(connectMetadata)
                    .doOnSubscribe(() -> {
                        analyticsSequence.connectionAttemptStarted();
                        analyticsSuccessRateCounter.initialConnectStarting(watchBt.getMacAddress());
                    })
                    .subscribe(s -> {
                        final String macAddress = watchBt.getMacAddress();
                        Timber.d("Initial connect succeeded %s", macAddress);
                        analyticsSuccessRateCounter.countSuccessOrFail(macAddress, true);
                        analyticsSuccessRateCounter.resetCounterIfNeeded(
                            ConnectSuccessRateCounter.ResetReason.InitialConnectSucceeded,
                            macAddress);

                        String prevModel = sharedPreferences.getString(SuuntoRepositoryUtils.PREVIOUS_MODEL_KEY, null);
                        String prevSerial = sharedPreferences.getString(SuuntoRepositoryUtils.PREVIOUS_SERIAL_KEY, null);

                        analyticsSequence.pairingConnectionSucceeded(context, prevModel, prevSerial);
                        stateMachine().fire(ExitTriggers.Connected);
                    }, throwable -> {
                        analyticsSuccessRateCounter.countSuccessOrFail(watchBt.getMacAddress(),
                            false);
                        analyticsSequence.connectionAttemptFailed(context, throwable,
                            connectMetadata, forAnalyticsAlreadyConnected,
                            watchBt.isInvalidPacketDetectedAfterLastWatchConnect());
                        analyticsSequence.pairingConnectionFailed(context);
                        stateMachine().fire(ExitTriggers.ConnectFailed, throwable,
                            Throwable.class);
                    });
        }
    }

    @Override
    protected void onExit(Transition transition) {
        Timber.d("Exit %s", transition.toString());
        if (connectSubscription != null) {
            connectSubscription.unsubscribe();
        }
        if (!(transition.getTrigger() instanceof ExitTriggers)) {
            Timber.d("exited before connect result");
            /*
             * Unexpected exit trigger - Clean up connect attempt.
             */
            watchBt.disconnect()
                .onErrorComplete()
                .subscribe();
        }
    }
}
