package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import com.suunto.connectivity.repository.stateMachines.base.Trigger;

/**
 * Connection state machine triggers.
 */
public enum Triggers implements Trigger {

    /**
     * Triggers
     */
    // Mds connected.
    Connected,
    // Mds disconnected.
    Disconnected,
    // Bluetooth turned on.
    BTon,
    // Bluetooth turned off.
    BToff,
    // Device unpaired.
    Unpaired,
    // Device unpaired forcefully by user.
    ForceUnpaired,
    // Unstable connection detected.
    ConnectionInstabilityDetected,
    // Connection instability delay passed. After that reconnection will tried again.
    ConnectionInstabilityDelayPassed,
    // Connection instability cleared by the user. After that reconnection will tried again.
    ConnectionInstabilityCleared,
    // Connection reset delay has passed. After that device is reconnected.
    ConnectionResetDelayPassed,
    // Pending reconnection is refresh is requested.
    LegacyDeviceRefreshAlarm,
    // ANCS state changed. Causes disconnect and reconnect in order start or shutdown ANCS server
    // on BLE level.
    AncsStateChanged,

    /**
     * Command like triggers.
     */
    // Service has started.
    ServiceStartConnect,
    // User has requested unpairing of the device
    ForceUnpair,
    // User has requested connect reset.
    ConnectReset,

    /**
     * State machine is to be destroyed.
     */
    StateMachineDestroy
}
