package com.suunto.connectivity.repository.workoutFileImport

import com.squareup.moshi.FromJson
import com.squareup.moshi.JsonReader
import com.squareup.moshi.JsonWriter
import com.squareup.moshi.ToJson
import okio.Buffer
import org.json.JSONException
import org.json.JSONObject

internal object JSONObjectAdapter {
    @FromJson
    fun from<PERSON><PERSON>(reader: <PERSON><PERSON><PERSON>ead<PERSON>): JSONObject? {
        return (reader.readJsonValue() as? Map<*, *>)?.let {
            try {
                JSONObject(it)
            } catch (e: JSONException) {
                null
            }
        }
    }

    @ToJson
    fun toJson(writer: <PERSON><PERSON><PERSON>rite<PERSON>, value: JSONObject?) {
        value?.let { writer.value(Buffer().writeUtf8(value.toString())) }
    }
}
