package com.suunto.connectivity.findphone

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.Ringtone
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import com.stt.android.coroutines.runSuspendCatching
import com.suunto.connectivity.ServiceCoroutineScope
import dagger.hilt.android.scopes.ServiceScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@ServiceScoped
class AlarmVibrator @Inject constructor(
    @ServiceCoroutineScope private val serviceScope: CoroutineScope,
    private val context: Context
) {

    @Suppress("DEPRECATION")
    private val vibrator by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager =
                context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }
    }

    private val audioManager by lazy {
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    private val ringtone: Ringtone? by lazy {
        kotlin.runCatching {
            RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)?.let { uri ->
                RingtoneManager.getRingtone(context, uri)?.apply {
                    this.audioAttributes = AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_ALARM)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .setLegacyStreamType(AudioManager.STREAM_ALARM)
                        .build()
                }
            }
        }.onFailure {
            Timber.w(it, "get ringtone by default alarm uri failed.")
        }.getOrNull()
    }

    private var alarmJob: Job? = null
    private var primitiveVolume: Int? = null

    private fun startAlarm() {
        if (alarmJob?.isActive == true) return
        alarmJob = serviceScope.launch(Dispatchers.IO) {
            launch {
                setAlarmVolume()
            }
            launch {
                ringtonePlay()
            }
        }
        alarmJob?.invokeOnCompletion {
            if (isRingtonePlaying()) {
                ringtone?.stop()
            }
            primitiveVolume?.let { volume ->
                audioManager.setStreamVolume(
                    AudioManager.STREAM_ALARM,
                    volume,
                    AudioManager.FLAG_PLAY_SOUND
                )
            }
        }
    }

    private suspend fun setAlarmVolume() {
        if (audioManager.isVolumeFixed) return
        runSuspendCatching {
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_ALARM)
            var volume =
                audioManager.getStreamVolume(AudioManager.STREAM_ALARM)
                    .also { primitiveVolume = it }
            Timber.v("volume: $volume, maxVolume: $maxVolume")
            while (volume < maxVolume) {
                Timber.v("adjust stream volume $volume/$maxVolume.")
                audioManager.adjustStreamVolume(
                    AudioManager.STREAM_ALARM,
                    AudioManager.ADJUST_RAISE,
                    AudioManager.FLAG_PLAY_SOUND
                )
                delay(500)
                volume = audioManager.getStreamVolume(AudioManager.STREAM_ALARM)
            }
        }.onFailure {
            Timber.w(it, "set stream volume failed.")
        }
    }

    private suspend fun ringtonePlay() {
        runSuspendCatching {
            ringtone?.play()
            while (true) {
                delay(500)
                if (!isRingtonePlaying()) {
                    Timber.v("ringtone has finished and will play again.")
                    ringtone?.play()
                }
            }
        }.onFailure {
            Timber.w(it, "play ringtone failed.")
        }
    }

    private fun stopAlarm() {
        Timber.v("stop alarm.")
        alarmJob?.cancel()
    }

    @Suppress("DEPRECATION")
    private fun startVibrate() {
        Timber.v("start vibrate.")
        val pattern = longArrayOf(500, 500)
        vibrator.vibrate(VibrationEffect.createWaveform(pattern, 0))
    }

    private fun stopVibrate() {
        Timber.v("stop vibrate.")
        vibrator.cancel()
    }

    fun start() {
        Timber.v("start alarm and vibrate.")
        startAlarm()
        startVibrate()
    }

    fun stop() {
        Timber.v("stop alarm and vibrate.")
        if (isRingtonePlaying()) {
            stopAlarm()
        }
        stopVibrate()
    }

    fun reStart() {
        Timber.v("restart alarm and vibrate.")
        stop()
        start()
    }

    private fun isRingtonePlaying(): Boolean {
        return ringtone?.isPlaying == true
    }
}
