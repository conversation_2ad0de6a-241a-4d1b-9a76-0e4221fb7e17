package com.suunto.connectivity.findphone

import com.stt.android.utils.toV2
import com.suunto.connectivity.ServiceCoroutineScope
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.watch.WatchBt
import dagger.hilt.android.scopes.ServiceScoped
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asFlow
import org.json.JSONObject
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@ServiceScoped
class FindPhoneModel @Inject constructor(
    @ServiceCoroutineScope private val serviceScope: CoroutineScope,
    private val alarmVibrator: AlarmVibrator,
    private val screenObserver: ScreenObserver,
    private val alarmNotification: AlarmNotification
) {

    private val exceptionHandler = CoroutineExceptionHandler { _, thr ->
        Timber.w(thr, "find phone error[${thr.javaClass.simpleName}].")
        alarmVibrator.stop()
        alarmNotification.cancelNotification()
    }

    private val coroutineContext: CoroutineContext = Dispatchers.IO + exceptionHandler

    private var stateChangeJob: Job? = null
    private var observerJob: Job? = null
    private var subscribeWatchJob: Job? = null

    private fun isCapabilitySupported(watchBt: WatchBt) =
        SuuntoDeviceCapabilityInfoProvider[watchBt.suuntoBtDevice.deviceType]
            .supportsFindPhone(watchBt.currentState.deviceInfo?.capabilities)

    fun startObservingDevice(watchBt: WatchBt) {
        stateChangeJob?.cancel()
        stateChangeJob = serviceScope.launch(coroutineContext) {
            watchBt.stateChangeObservable
                .toV2()
                .filter { isCapabilitySupported(watchBt) }
                .map { it.isConnected }
                .distinctUntilChanged()
                .asFlow()
                .collect { connected ->
                    Timber.v("watch is connected: $connected")
                    if (connected) {
                        subscribeFindPhone(watchBt)
                    } else {
                        unSubscribeFindPhone()
                    }
                }
        }

        observerJob?.cancel()
        observerJob = serviceScope.launch(coroutineContext) {
            launch {
                screenObserver.observing().collect {
                    Timber.v("screen state changed: $it")
                    alarmAndVibrator(watchBt, false)
                }
            }
            launch {
                alarmNotification.observing().collect {
                    Timber.v("alarm notification canceled.")
                    alarmAndVibrator(watchBt, false)
                }
            }
        }
        observerJob?.invokeOnCompletion {
            alarmVibrator.stop()
            screenObserver.unregister()
            alarmNotification.apply {
                cancelNotification()
                unregister()
            }
        }
        screenObserver.register()
        alarmNotification.register()
    }

    private fun subscribeFindPhone(watchBt: WatchBt) {
        subscribeWatchJob?.cancel()
        subscribeWatchJob = serviceScope.launch(coroutineContext) {
            launch {
                watchBt.subscribeFindPhoneObservable()
                    .toV2()
                    // Temp solution to avoid ESW bug(it'll call back twice when subscribed).
                    .debounce(1, TimeUnit.SECONDS)
                    .asFlow()
                    .collect {
                        alarmAndVibrator(watchBt, it)
                    }
            }
        }
    }

    fun alarmAndVibrator(watchBt: WatchBt, start: Boolean) {
        Timber.v("start find phone job? $start")
        if (start) {
            putFindPhoneState(watchBt, PhoneState.Ringing, onSuccess = {
                val notification = alarmNotification.buildNotification()
                alarmNotification.notifyNotification(notification)
                alarmVibrator.reStart()
            })
        } else {
            alarmNotification.cancelNotification()
            alarmVibrator.stop()
            putFindPhoneState(watchBt, PhoneState.Finished)
        }
    }

    private fun putFindPhoneState(
        watchBt: WatchBt,
        state: PhoneState,
        onSuccess: (() -> Unit)? = null
    ) {
        val isSupported = isCapabilitySupported(watchBt)
        val isConnected = watchBt.currentState.isConnected
        if (!isSupported || !isConnected) {
            Timber.v("Supports find my phone:$isSupported, isConnected:$isConnected")
            return
        }
        watchBt.putFindPhoneState(state.toContract()).subscribe(
            {
                Timber.v("put find phone state[$state] succeed.")
                onSuccess?.invoke()
            },
            {
                Timber.w(it, "put find phone state[$state] failed.")
            }
        )
    }

    private fun unSubscribeFindPhone() {
        subscribeWatchJob?.cancel()
        subscribeWatchJob = null
    }

    enum class PhoneState(val state: Int) {
        Ringing(0),
        Timeout(1),
        Finished(2);

        fun toContract() = JSONObject().apply { put("State", state) }.toString()
    }
}
