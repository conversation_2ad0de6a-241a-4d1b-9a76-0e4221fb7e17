package com.suunto.connectivity.notifications

import android.app.Notification
import android.content.Context
import android.content.Intent
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import androidx.core.content.IntentCompat
import com.stt.android.coroutines.runSuspendCatching
import com.suunto.connectivity.mediacontrols.domain.MediaNotificationActionRequest
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.sync.SynchronizerStorage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AncsService : NotificationListenerService() {
    private val suuntoNotificationClient: SuuntoNotificationClient by lazy {
        SuuntoNotificationClient.create(applicationContext)
    }

    @Inject
    internal lateinit var synchronizerStorage: SynchronizerStorage

    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private val messagesById = mutableMapOf<Int, AncsMessage>()
    private val ignoredMessageIds = mutableSetOf<Int>()

    override fun onCreate() {
        super.onCreate()
        Timber.d("onCreate")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent != null) {
            val request = IntentCompat.getParcelableExtra(
                intent,
                EXTRA_NOTIFICATION_ACTION_KEY,
                MediaNotificationActionRequest::class.java,
            )
            if (request != null) {
                val deviceInfo = IntentCompat.getParcelableExtra(
                    intent,
                    EXTRA_DEVICE_INFO_KEY,
                    MdsDeviceInfo::class.java,
                )
                handleNotificationAction(request, deviceInfo)
            }
        }

        return super.onStartCommand(intent, flags, startId)
    }

    private fun handleNotificationAction(
        request: MediaNotificationActionRequest,
        deviceInfo: MdsDeviceInfo?,
    ) {
        coroutineScope.launch {
            runSuspendCatching {
                val sbn = activeNotifications.find { sbn ->
                    AncsMessage.createId(sbn) == request.notificationId
                }
                if (sbn == null) {
                    Timber.d("Notification already dismissed on phone, removing from watch")
                    suuntoNotificationClient.removeNotification(request.notificationId)
                    messagesById.remove(request.notificationId)
                    ignoredMessageIds.remove(request.notificationId)
                    return@runSuspendCatching
                }

                NotificationActionHelper.handleNotificationAction(
                    context = applicationContext,
                    synchronizerStorage = synchronizerStorage,
                    request = request,
                    sbn = sbn,
                    deviceInfo = deviceInfo,
                    dismissNotification = { dismissNotification(sbn) },
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to handle notification action")
            }
        }
    }

    private fun dismissNotification(sbn: StatusBarNotification) {
        Timber.d("Dismiss notification")
        cancelNotification(sbn.key)

        // If the dismissed notification belongs to a group, and the only left notification in this
        // group is a group summary, dismiss the summary too.
        val groupKey = sbn.notification.group.takeUnless { it.isNullOrEmpty() } ?: return
        val notificationsOfSameGroup = activeNotifications.filter { it.notification.group == groupKey }
        if (notificationsOfSameGroup.size == 1 &&
            (notificationsOfSameGroup.first().notification.flags and Notification.FLAG_GROUP_SUMMARY) != 0) {
            Timber.d("Dismiss group summary notification")
            cancelNotification(notificationsOfSameGroup.first().key)
        }
    }

    override fun onDestroy() {
        Timber.d("onDestroy")
        super.onDestroy()
    }

    override fun onListenerConnected() {
        Timber.d("onListenerConnected")
        super.onListenerConnected()
        sendConnectedStatusBroadcast(true)

        coroutineScope.launch {
            runSuspendCatching {
                val messageIds = mutableSetOf<Int>()

                // Go through existing notifications on the phone, sync them with the watch.
                activeNotifications.forEach { sbn ->
                    handleNotification(sbn)?.id
                        ?.let(messageIds::add)
                }

                // In case a notification is removed while the app is disconnected from the notification
                // service, make sure it is removed from the watch.
                val iterator = messagesById.iterator()
                while (iterator.hasNext()) {
                    val (messageId, _) = iterator.next()
                    if (!messageIds.contains(messageId)) {
                        removeNotification(messageId)
                        iterator.remove()
                    }
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to handle notifications when connected to notification service")
            }
        }
    }

    /**
     * If [sbn] should be posted to watch, sync it to watch. Otherwise, remove it from the watch.
     */
    private suspend fun handleNotification(sbn: StatusBarNotification): AncsMessage? {
        val ancsMessage = createAncsMessage(this, sbn) ?: return null

        if (ancsMessage.shouldIgnore) {
            Timber.d("Ignoring notification")
            ignoreNotification(ancsMessage.id)
            return ancsMessage
        }

        if (ancsMessage.hasSameContent(messagesById[ancsMessage.id])) {
            Timber.d("Ignoring duplicated notification")
            return ancsMessage
        }

        postNotification(ancsMessage)

        return ancsMessage
    }

    override fun onListenerDisconnected() {
        Timber.d("onListenerDisconnected")
        super.onListenerDisconnected()
        sendConnectedStatusBroadcast(false)
    }

    private fun sendConnectedStatusBroadcast(isConnected: Boolean) {
        val intent = Intent(ACTION_NOTIFICATION_LISTENER_CONNECTED_STATUS_CHANGED)
            .putExtra(EXTRA_CONNECTED_STATUS, isConnected)
        sendBroadcast(intent)
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        Timber.d("onNotificationPosted")

        coroutineScope.launch {
            runSuspendCatching {
                handleNotification(sbn)
            }.onFailure { e ->
                Timber.w(e, "Could not post notification")
            }
        }
    }

    private suspend fun ignoreNotification(messageId: Int) {
        if (!ignoredMessageIds.contains(messageId)) {
            removeNotification(messageId)
            ignoredMessageIds.add(messageId)
        }
    }

    private suspend fun postNotification(ancsMessage: AncsMessage) {
        suuntoNotificationClient.postNotification(ancsMessage)
        messagesById[ancsMessage.id] = ancsMessage
        ignoredMessageIds.remove(ancsMessage.id)
    }

    private suspend fun removeNotification(messageId: Int) {
        if (messagesById.containsKey(messageId)) {
            suuntoNotificationClient.removeNotification(messageId)
        }
        messagesById.remove(messageId)
        ignoredMessageIds.remove(messageId)
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        Timber.d("onNotificationRemoved")

        coroutineScope.launch {
            runSuspendCatching {
                removeNotification(AncsMessage.createId(sbn))
            }.onFailure { e ->
                Timber.w(e, "Could not remove notification")
            }
        }
    }

    companion object {
        const val ACTION_NOTIFICATION_LISTENER_CONNECTED_STATUS_CHANGED: String =
            "com.suunto.connectivity.notifications.AncsService.ACTION_NOTIFICATION_LISTENER_CONNECTED_STATUS_CHANGED"

        const val EXTRA_CONNECTED_STATUS: String =
            "com.suunto.connectivity.notifications.AncsService.EXTRA_CONNECTED_STATUS"

        private const val EXTRA_NOTIFICATION_ACTION_KEY: String =
            "com.suunto.connectivity.notifications.AncsService.EXTRA_NOTIFICATION_ACTION_KEY"

        private const val EXTRA_DEVICE_INFO_KEY: String =
            "com.suunto.connectivity.notifications.AncsService.EXTRA_DEVICE_INFO_KEY"

        fun newHandleNotificationActionIntent(
            context: Context,
            request: MediaNotificationActionRequest,
            deviceInfo: MdsDeviceInfo?,
        ): Intent = Intent(context, AncsService::class.java).apply {
            putExtra(EXTRA_NOTIFICATION_ACTION_KEY, request)
            deviceInfo?.let { putExtra(EXTRA_DEVICE_INFO_KEY, it) }
        }

        private fun AncsMessage.hasSameContent(other: AncsMessage?): Boolean = other?.let {
            title == other.title &&
                message == other.message &&
                categoryId == other.categoryId
        } ?: false

        private fun createAncsMessage(context: Context, sbn: StatusBarNotification): AncsMessage? =
            AncsMessage.create(context, sbn)
                ?: run {
                    Timber.w(
                        IllegalArgumentException("Failed to convert StatusBarNotification to AncsMessage"),
                        "Package: %s",
                        sbn.packageName,
                    )
                    null
                }
    }
}
