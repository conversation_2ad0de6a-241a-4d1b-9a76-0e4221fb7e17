package com.suunto.connectivity.util.workqueue;

import android.os.Looper;
import android.os.OperationCanceledException;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import rx.Single;
import rx.SingleSubscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * Queue operation for running RX singles in a work queue.
 *
 * @param <T> Result type.
 */
public class QueueRxSingleOperation<T> extends QueueOperation {
    @NonNull
    private final Single<T> single;
    @Nullable
    private Subscription subscription;
    @Nullable
    private SingleSubscriber<? super T> singleSubscriber;

    QueueRxSingleOperation(@NonNull Single<T> single) {
        this.single = single;
    }

    void init(
        @NonNull SingleSubscriber<? super T> singleSubscriber) {
        this.singleSubscriber = singleSubscriber;
    }

    void unsubscribe() {
        if (subscription != null) {
            subscription.unsubscribe();
        }
    }

    @Override
    protected void protectedRun() throws Throwable {
        if (singleSubscriber == null) {
            // Should never happen.
            onCompleted();
        }
        subscription =
            single
                .subscribe(t -> {
                    onCompleted();
                    singleSubscriber.onSuccess(t);
                }, throwable -> {
                    onCompleted();
                    singleSubscriber.onError(throwable);

                });
    }

    @Override
    public void cancel() {
        super.cancel();
        unsubscribe();
        if (singleSubscriber != null) {
            singleSubscriber.onError(new OperationCanceledException());
        }
        subscription = null;
        singleSubscriber = null;
    }
}
