package com.suunto.connectivity.util;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.text.TextUtils;

import com.suunto.connectivity.suuntoconnectivity.notification.NotificationSettingsContract;
import com.suunto.connectivity.suuntoconnectivity.notification.NotificationSettingsHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import timber.log.Timber;

/**
 * Common storage for all NotificationSettings.
 */
public class NotificationSettingsStorage {

    private static final String[] PROJECTION = new String[]{
            NotificationSettingsContract.Packages.NAME,
            NotificationSettingsContract.Packages.ENABLED
    };

    private final Context context;
    private final AtomicReference<Map<String, Boolean>> packages;
    final private NotificationSettingsHelper helper;

    public NotificationSettingsStorage(Context context) {
        this.context = context;
        this.packages = new AtomicReference<>();
        helper = new NotificationSettingsHelper(context);
        readPackages();
    }


    public void addPackage(String pkg) {
        if (packages.get().containsKey(pkg))
            return;

        // Check if the package already exists
        Cursor cursor = null;
        try {
            cursor = helper.queryPackage(
                    pkg,
                    NotificationSettingsContract.Packages.PROJECTION_ALL,
                    null,
                    null,
                    null
            );

            if (cursor != null && cursor.getCount() > 0)
                return;
        } finally {
            if (cursor != null)
                IOUtils.closeQuietly(cursor);
        }


        ContentValues values = new ContentValues();
        values.put(NotificationSettingsContract.Packages.NAME, pkg);
        values.put(NotificationSettingsContract.Packages.ENABLED, NotificationSettingsContract.Packages.TRUE);
        if (helper.insert(values)) {
            packages.get().put(pkg, true);
        }
    }

    /**
     * Is notification from package allowed to be sent to watch.
     *
     * @param pkg Package name.
     * @return True, if allowed.
     */
    public boolean notificationAllowed(String pkg) {
        Boolean allowed = packages.get().get(pkg);

        // Assume all notifications coming from unknown packages are allowed.
        // For known packages use the value stored for the package.
        return allowed == null || allowed;
    }

    /**
     * Is package known and are notifications enabled for that.
     *
     * @param pkg Package name.
     * @return True, if package is known and notifications are enabled.
     */
    private boolean isPackageKnownAndEnabled(String pkg) {
        Boolean enabled = packages.get().get(pkg);
        return enabled != null && enabled;
    }

    public List<String> knownPackages() {
        return new ArrayList<>(packages.get().keySet());
    }

    public void enablePackage(String pkg) {
        // If already enabled or empty
        if (isPackageKnownAndEnabled(pkg) || TextUtils.isEmpty(pkg))
            return;

        Timber.d("Enabling notifications for %s", pkg);

        //ContentResolver resolver = context.getContentResolver();
        ContentValues values = new ContentValues();
        values.put(NotificationSettingsContract.Packages.NAME, pkg);
        values.put(NotificationSettingsContract.Packages.ENABLED, NotificationSettingsContract.Packages.TRUE);

        helper.updatePackage(pkg, values, null, null);
        packages.get().put(pkg, true);
    }

    public void disablePackage(String pkg) {
        // If already disabled or empty
        if (!isPackageKnownAndEnabled(pkg) || TextUtils.isEmpty(pkg))
            return;

        Timber.d("Disabling notifications for %s", pkg);

        //ContentResolver resolver = context.getContentResolver();
        ContentValues values = new ContentValues();
        values.put(NotificationSettingsContract.Packages.NAME, pkg);
        values.put(NotificationSettingsContract.Packages.ENABLED, NotificationSettingsContract.Packages.FALSE);

        helper.updatePackage(pkg, values, null, null);
        packages.get().put(pkg, false);
    }

    public boolean incomingCallNotificationsEnabled() {
        return notificationAllowed(NotificationSettingsHelper.NOTIFICATION_INCOMING_CALL_PACKAGE);
    }

    public boolean missedCallNotificationsEnabled() {
        return notificationAllowed(NotificationSettingsHelper.NOTIFICATION_MISSED_CALL_PACKAGE);
    }

    public boolean smsNotificationsEnabled() {
        return notificationAllowed(NotificationSettingsHelper.NOTIFICATION_TEXT_MESSAGE_PACKAGE);
    }


    private void readPackages() {

        // Fetch all packages to be cached locally
        Cursor cursor =
                helper.queryList(PROJECTION,
                        null,
                        null,
                        null);

        // Update packages map from the results
        Map<String, Boolean> newPackages = new HashMap<>();
        if (cursor != null && cursor.moveToFirst()) {
            do {
                String name = cursor.getString(0);
                boolean enabled = cursor.getInt(1) != 0;
                newPackages.put(name, enabled);
            } while (cursor.moveToNext());
        }
        packages.set(newPackages);

        IOUtils.closeQuietly(cursor);
    }
}
