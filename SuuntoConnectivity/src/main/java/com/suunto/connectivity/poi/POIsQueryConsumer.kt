package com.suunto.connectivity.poi

import com.stt.android.coroutines.awaitSuspend
import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.commands.Response

class POIsQueryConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {
    override fun isResponseRelated(response: Response): Boolean = response is PostPOIsResponse

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*> {
        return ResponseMessage(messageId, response)
    }

    suspend fun syncPOIs(macAddress: String) {
        suuntoRepositoryClient.waitForServiceReady()
            .andThen(
                suuntoRepositoryClient.sendQuery(PostPOIsQuery(macAddress))
                    .first()
                    .toCompletable()
            )
            .awaitSuspend()
    }
}
