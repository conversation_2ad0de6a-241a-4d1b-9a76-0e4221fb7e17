package com.suunto.connectivity.routes

import android.os.Bundle
import androidx.core.os.BundleCompat
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_POST_ROUTES
import rx.Observable

class RoutesResponseProducer(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<PostRoutesResponse> {
    override fun isRelated(messageType: Int): Boolean = messageType == MSG_POST_ROUTES

    override fun provideResponseObservable(messageType: Int, bundle: Bundle): Observable<PostRoutesResponse> {
        val query = BundleCompat.getParcelable(
            bundle,
            SuuntoRepositoryService.ArgumentKeys.ARG_DATA,
            PostRoutesQuery::class.java
        )
        return suuntoRepositoryService.activeDevices
            .syncRoutes(query?.macAddress, query?.doNotRemoveNonAppRoutesFromWatch)
            .andThen(Observable.just(PostRoutesResponse())
        )
    }
}
