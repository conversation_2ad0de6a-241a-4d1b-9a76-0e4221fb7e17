package com.suunto.connectivity.sportmodes

import com.suunto.connectivity.Spartan
import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryException
import com.suunto.connectivity.repository.commands.DeleteSportModeQuery
import com.suunto.connectivity.repository.commands.DeleteSportModeResponse
import com.suunto.connectivity.repository.commands.GetSportModeObjectQuery
import com.suunto.connectivity.repository.commands.GetSportModeObjectResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.repository.commands.SetSportModeObjectQuery
import com.suunto.connectivity.repository.commands.SetSportModeObjectResponse
import com.suunto.connectivity.repository.commands.SportModePart
import rx.Completable
import rx.Single

class SportModesConsumer
constructor(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {
    fun getSportModeObject(device: Spartan, id: Int, sportModePart: SportModePart): Single<String> {
        val suuntoBtDevice = device.suuntoBtDevice
        val macAddress = suuntoBtDevice.macAddress
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(GetSportModeObjectQuery(macAddress, id, sportModePart))
                .first()
                .toSingle()
                .map { response ->
                    if (response is GetSportModeObjectResponse && response.sportModePart == sportModePart) {
                        return@map response.response
                    }
                    throw SuuntoRepositoryException("Invalid response [$response]")
                }
        )
    }

    fun deleteSportMode(device: Spartan, id: Int): Completable {
        val suuntoBtDevice = device.suuntoBtDevice
        val macAddress = suuntoBtDevice.macAddress
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(DeleteSportModeQuery(macAddress, id))
                .first()
                .toSingle()
                .flatMapCompletable { response ->
                    if (response is DeleteSportModeResponse) {
                        if (response.isSuccessful) {
                            Completable.complete()
                        } else {
                            Completable.error(SuuntoRepositoryException("Unable to delete sport mode"))
                        }
                    } else {
                        Completable.error(SuuntoRepositoryException("Invalid response [$response]"))
                    }
                }
        )
    }

    fun setSportModeObject(device: Spartan, id: Int, json: String, sportModePart: SportModePart): Completable {
        val suuntoBtDevice = device.suuntoBtDevice
        val macAddress = suuntoBtDevice.macAddress
        return suuntoRepositoryClient.waitForServiceReady().andThen(
            suuntoRepositoryClient.sendQuery(SetSportModeObjectQuery(macAddress, id, json, sportModePart))
                .first()
                .toSingle()
                .flatMapCompletable { response ->
                    if (response is SetSportModeObjectResponse && response.sportModePart == sportModePart) {
                        if (response.isSuccessful) {
                            Completable.complete()
                        } else {
                            Completable.error(SuuntoRepositoryException("Unable to set sport mode value"))
                        }
                    } else {
                        Completable.error(SuuntoRepositoryException("Invalid response [$response]"))
                    }
                }
        )
    }

    override fun isResponseRelated(response: Response): Boolean {
        return when (response) {
            is GetSportModeObjectResponse -> true
            is SetSportModeObjectResponse -> true
            is DeleteSportModeResponse -> true
            else -> false
        }
    }

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*>? {
        return when (response) {
            is GetSportModeObjectResponse,
            is SetSportModeObjectResponse,
            is DeleteSportModeResponse -> ResponseMessage(messageId, response)
            else -> {
                null
            }
        }
    }
}
