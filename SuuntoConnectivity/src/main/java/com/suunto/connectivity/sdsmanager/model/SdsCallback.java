package com.suunto.connectivity.sdsmanager.model;

public class SdsCallback {

    private final int taskId;
    private final int sdsCallType;
    private final SdsHeader header;
    private final String body;

    public SdsCallback(int taskId, int sdsCallType, SdsHeader header, String body) {
        this.taskId = taskId;
        this.sdsCallType = sdsCallType;
        this.header = header;
        this.body = body;
    }

    public int getTaskId() {
        return taskId;
    }

    public int getSdsCallType() {
        return sdsCallType;
    }

    public SdsHeader getHeader() {
        return header;
    }

    public String getBody() {
        return body;
    }
}
