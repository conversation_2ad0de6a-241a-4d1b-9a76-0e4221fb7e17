package com.suunto.connectivity.location

import android.content.Context
import android.content.SharedPreferences
import android.location.Location
import android.net.Uri
import androidx.core.content.edit
import com.movesense.mds.MdsException
import com.movesense.mds.MdsResource
import com.movesense.mds.MdsResponse
import com.stt.android.TestOpen
import com.stt.android.analytics.LocationAnalytics
import com.stt.android.utils.toV1
import com.suunto.connectivity.R
import com.suunto.connectivity.repository.AnalyticsUtils.sendAssistedGpsStartAnalytics
import com.suunto.connectivity.repository.AnalyticsUtils.sendAssistedGpsStopAnalytics
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.util.FileUtils
import io.reactivex.BackpressureStrategy
import kotlinx.coroutines.rx2.asObservable
import rx.Completable
import rx.Observable
import rx.exceptions.Exceptions
import rx.subjects.BehaviorSubject
import rx.subjects.PublishSubject
import rx.subjects.Subject
import timber.log.Timber
import java.io.IOException
import java.net.HttpURLConnection.HTTP_NOT_FOUND
import java.net.HttpURLConnection.HTTP_NOT_IMPLEMENTED
import java.net.HttpURLConnection.HTTP_NO_CONTENT
import java.net.HttpURLConnection.HTTP_OK
import java.util.concurrent.atomic.AtomicReference

@TestOpen
class FusionLocationResource(
    val context: Context,
    val mdsRx: MdsRx,
    val locationProvider: LocationProvider,
    val sharedPreferences: SharedPreferences
) : MdsResource {

    companion object {
        const val RESOURCE_DESCRIPTOR = "suunto_fusion_location.wbr"
        const val URI_GEO_COORDINATE = "/Fusion/Location/GeoCoordinates"
        const val URI_READINESS = "/Fusion/Location/Readiness"
        const val URI_GEO_ACCURACY = "/Fusion/Location/GeoAccuracy"
        const val URI_SERVICE_LEVEL = "/Fusion/Location/ServiceLevel"
        const val NO_CONTENT_BODY = "{}"
        const val ASSISTED_GPS_ACTIVE = "assisted_gps_active"

        @JvmStatic
        fun installResourceDescriptor(context: Context): Completable {
            return Completable.fromAction {
                try {
                    FileUtils.copyRawResourceToStream(
                        context,
                        R.raw.suunto_fusion_location,
                        context.openFileOutput(RESOURCE_DESCRIPTOR, Context.MODE_PRIVATE)
                    )
                } catch (e: IOException) {
                    throw Exceptions.propagate(e)
                }
            }
        }
    }

    private val serviceLevelSubject: BehaviorSubject<LocationServiceLevel> =
        BehaviorSubject.create(LocationServiceLevel.UNKNOWN)
    private val subscriptionStatusSubject: BehaviorSubject<Boolean> =
        BehaviorSubject.create(false)

    private val locationSubscriberChanges: Subject<Int, Int> =
        PublishSubject.create<Int>().toSerialized()
    private val accuracySubscriberChanges: Subject<Int, Int> =
        PublishSubject.create<Int>().toSerialized()
    private val readinessSubscriberChanges: Subject<Int, Int> =
        PublishSubject.create<Int>().toSerialized()

    private var locationAnalytics: AtomicReference<LocationAnalytics> = AtomicReference()

    /**
     * True, if the latest GPS tracking was never finished. This potentially indicates that connectivity service crashed or was shut down by operating system.
     */
    final var latestGpsTrackingWasNotCompleted: Boolean = false
        private set

    fun register(): Completable {
        latestGpsTrackingWasNotCompleted = sharedPreferences.getBoolean(ASSISTED_GPS_ACTIVE, false)
        sharedPreferences.edit {
            putBoolean(ASSISTED_GPS_ACTIVE, false)
        }
        initializeLocationSource()
        initializeReadinessSource()
        initializeLocationSubscribersMonitoring()

        return mdsRx.registerResource(RESOURCE_DESCRIPTOR, this)
            .toCompletable()
    }

    fun locationUpdatesUsage() = locationSubscriberChanges.asObservable()

    override fun onResourceReady() {
        Timber.d("Resource registered")
    }

    override fun onError(e: MdsException) {
        Timber.w(e, "Resource error")
    }

    override fun get(uri: Uri, body: String): MdsResponse {
        Timber.d("Received GET: uri = $uri, body = $body")
        return when (uri.toString()) {
            URI_GEO_COORDINATE -> currentLocationResponse()
            URI_GEO_ACCURACY -> currentAccuracyResponse()
            URI_READINESS -> currentReadinessResponse()
            URI_SERVICE_LEVEL -> currentServiceLevelResponse()
            else -> {
                MdsResponse(HTTP_NOT_FOUND, NO_CONTENT_BODY)
            }
        }
    }

    override fun subscribe(uri: Uri, body: String): MdsResponse {
        Timber.d("Received SUBSCRIBE: uri = $uri, body = $body")
        return when (uri.toString()) {
            URI_GEO_COORDINATE -> {
                // TODO: parse requested service level from body
                serviceLevelSubject.onNext(LocationServiceLevel.BEST)
                locationSubscriberChanges.onNext(+1)
                currentLocationResponse()
            }
            URI_GEO_ACCURACY -> {
                accuracySubscriberChanges.onNext(+1)
                currentAccuracyResponse()
            }
            URI_READINESS -> {
                readinessSubscriberChanges.onNext(+1)
                currentReadinessResponse()
            }
            URI_SERVICE_LEVEL -> {
                currentServiceLevelResponse()
            }
            else -> {
                MdsResponse(HTTP_NOT_FOUND, NO_CONTENT_BODY)
            }
        }
    }

    override fun unsubscribe(uri: Uri, body: String): MdsResponse {
        Timber.d("Received UNSUBSCRIBE: uri = $uri, body = $body")

        // Reset subscriber count to zero instead of decrementing on unsubscribe.
        // Currently the watch may subscribe multiple times and then unsubscribe only once,
        // which means that it is not interested in the events anymore.

        return when (uri.toString()) {
            URI_GEO_COORDINATE -> {
                // locationSubscriberChanges.onNext(-1)
                locationSubscriberChanges.onNext(0)
                MdsResponse(HTTP_OK, NO_CONTENT_BODY)
            }
            URI_GEO_ACCURACY -> {
                // accuracySubscriberChanges.onNext(-1)
                accuracySubscriberChanges.onNext(0)
                MdsResponse(HTTP_OK, NO_CONTENT_BODY)
            }
            URI_READINESS -> {
                // readinessSubscriberChanges.onNext(-1)
                readinessSubscriberChanges.onNext(0)
                MdsResponse(HTTP_OK, NO_CONTENT_BODY)
            }
            URI_SERVICE_LEVEL -> {
                MdsResponse(HTTP_OK, NO_CONTENT_BODY)
            }
            else -> {
                MdsResponse(HTTP_NOT_FOUND, NO_CONTENT_BODY)
            }
        }
    }

    override fun put(uri: Uri, s: String): MdsResponse {
        return MdsResponse(HTTP_NOT_IMPLEMENTED, NO_CONTENT_BODY)
    }

    override fun put(uri: Uri, data: ByteArray, offset: Long, total: Long): MdsResponse {
        return MdsResponse(HTTP_NOT_IMPLEMENTED, NO_CONTENT_BODY)
    }

    override fun post(uri: Uri, s: String): MdsResponse {
        return MdsResponse(HTTP_NOT_IMPLEMENTED, NO_CONTENT_BODY)
    }

    override fun delete(uri: Uri, s: String): MdsResponse {
        return MdsResponse(HTTP_NOT_IMPLEMENTED, NO_CONTENT_BODY)
    }

    private fun currentLocationResponse(): MdsResponse {
        var location: Location? = null
        try {
            location = locationProvider.lastLocation()
        } catch (t: Throwable) {
            Timber.w(t, "Failed to get location")
        }

        return if (location != null) {
            MdsResponse(HTTP_OK, makeGeoCoordinateContract(location))
        } else {
            MdsResponse(HTTP_NO_CONTENT, NO_CONTENT_BODY)
        }
    }

    private fun currentAccuracyResponse(): MdsResponse {
        var accuracy: LocationAccuracy? = null
        try {
            accuracy = locationProvider.lastLocation()?.toLocationAccuracy()
        } catch (t: Throwable) {
            Timber.w(t, "Failed to get location accuracy")
        }

        return if (accuracy != null) {
            MdsResponse(HTTP_OK, makeGeoAccuracyContract(accuracy))
        } else {
            MdsResponse(HTTP_NO_CONTENT, NO_CONTENT_BODY)
        }
    }

    private fun currentReadinessResponse(): MdsResponse {
        return try {
            val readiness = locationProvider.readiness()
            MdsResponse(HTTP_OK, makeReadinessContract(readiness))
        } catch (t: Throwable) {
            Timber.w(t, "Failed to get location provider readiness")
            MdsResponse(HTTP_NO_CONTENT, NO_CONTENT_BODY)
        }
    }

    private fun currentServiceLevelResponse(): MdsResponse {
        val serviceLevel = serviceLevelSubject.value ?: LocationServiceLevel.UNKNOWN
        return MdsResponse(HTTP_OK, makeLocationServiceLevelContract(serviceLevel))
    }

    private fun getSubscriberCount(subscriberChanges: Observable<Int>): Observable<Int> {
        return subscriberChanges
            .startWith(0)
            .scan { accumulator, value ->
                if (value == 0) {
                    // Reset count to zero
                    0
                } else {
                    // Change count by the value, negative count not allowed
                    Math.max(accumulator + value, 0)
                }
            }
    }

    private fun initializeLocationSource() {
        val accuracySubscriberCount = getSubscriberCount(accuracySubscriberChanges)
        val locationSubscriberCount = getSubscriberCount(locationSubscriberChanges)

        Observable.combineLatest(
            accuracySubscriberCount,
            locationSubscriberCount,
            serviceLevelSubject
        ) { accuracyCount, locationCount, serviceLevel ->
            val hasSubscribers = accuracyCount + locationCount > 0
            LocationSubscriptionStatus(hasSubscribers, serviceLevel)
        }
            .distinctUntilChanged()
            .switchMap {
                if (it.hasSubscribers) {
                    locationProvider.locationUpdates(it.serviceLevel)
                        .asObservable()
                        .toV1(BackpressureStrategy.LATEST)
                        .doOnSubscribe {
                            Timber.d("Subscribing location updates")
                            locationAnalytics
                                .set(LocationAnalytics(System.currentTimeMillis(), context))
                            sendAssistedGpsStartAnalytics(
                                locationAnalytics.get()
                                    .startGpsAnalyticsProperties()
                            )
                            sharedPreferences.edit {
                                putBoolean(ASSISTED_GPS_ACTIVE, true)
                            }
                            latestGpsTrackingWasNotCompleted = false
                            sendNotification(
                                URI_SERVICE_LEVEL,
                                makeLocationServiceLevelContract(it.serviceLevel)
                            )
                        }
                        .doOnUnsubscribe {
                            sendAssistedGpsStopAnalytics(
                                locationAnalytics
                                    .get()?.stopGpsAnalyticsProperties()
                            )
                            sharedPreferences.edit {
                                putBoolean(ASSISTED_GPS_ACTIVE, false)
                            }
                            Timber.d("Unsubscribing location updates")
                        }
                        .onErrorResumeNext {
                            sharedPreferences.edit {
                                putBoolean(ASSISTED_GPS_ACTIVE, false)
                            }
                            Timber.w(it, "Error while receiving location updates")
                            Observable.empty()
                        }
                } else {
                    Observable.empty()
                }
            }
            .subscribe(
                { location ->
                    locationAnalytics.get()?.addLocation(location)
                    sendNotification(URI_GEO_COORDINATE, makeGeoCoordinateContract(location))
                    sendNotification(
                        URI_GEO_ACCURACY,
                        makeGeoAccuracyContract(
                            location.toLocationAccuracy()
                        )
                    )
                },
                {
                    Timber.w(it, "Error in location source, should never happen")
                }
            )
    }

    private fun initializeLocationSubscribersMonitoring() {
        val accuracySubscriberCount = getSubscriberCount(accuracySubscriberChanges)
        val locationSubscriberCount = getSubscriberCount(locationSubscriberChanges)
        Observable.combineLatest(
            accuracySubscriberCount,
            locationSubscriberCount
        ) { accuracyCount, locationCount ->
            subscriptionStatusSubject.onNext(accuracyCount + locationCount > 0)
        }
            .subscribe()
    }

    fun hasLocationSubscribers(): Observable<Boolean> {
        return subscriptionStatusSubject.asObservable()
    }

    private fun initializeReadinessSource() {
        getSubscriberCount(readinessSubscriberChanges)
            .map { it > 0 }
            .switchMap { hasSubscribers ->
                if (hasSubscribers) {
                    locationProvider.readinessUpdates()
                        .asObservable()
                        .toV1(BackpressureStrategy.LATEST)
                        .doOnSubscribe { Timber.d("Subscribing readiness updates") }
                        .doOnUnsubscribe { Timber.d("Unsubscribing readiness updates") }
                        .onErrorResumeNext {
                            Timber.w(it, "Error while receiving readiness updates")
                            Observable.empty()
                        }
                } else {
                    Observable.empty()
                }
            }
            .subscribe(
                { readiness ->
                    sendNotification(URI_READINESS, makeReadinessContract(readiness))
                },
                {
                    Timber.w(it, "Error in readiness source, should never happen")
                }
            )
    }

    private fun sendNotification(uri: String, body: String) {
        Timber.d("Sending notification: uri = $uri, body = $body")
        mdsRx.sendNotification(uri, body).subscribe(
            { },
            {
                Timber.w(it, "Failed to send notification")
            }
        )
    }

    data class LocationSubscriptionStatus(
        val hasSubscribers: Boolean,
        val serviceLevel: LocationServiceLevel
    )
}
