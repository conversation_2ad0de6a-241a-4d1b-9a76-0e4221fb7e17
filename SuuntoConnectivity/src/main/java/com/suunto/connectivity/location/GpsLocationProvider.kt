package com.suunto.connectivity.location

import android.annotation.SuppressLint
import android.content.Context
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Looper
import dagger.hilt.android.scopes.ServiceScoped
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.channels.trySendBlocking
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@SuppressLint("MissingPermission")
@ServiceScoped
class GpsLocationProvider @Inject constructor(context: Context) : LocationProvider {

    private val locationManager =
        context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    override fun lastLocation(provider: String): Location? =
        locationManager.getLastKnownLocation(provider)

    override fun readiness(): Int =
        if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            100
        } else {
            0
        }

    override fun locationUpdates(
        serviceLevel: LocationServiceLevel,
        provider: String,
    ): Flow<Location> = callbackFlow {
        val locationListener = object : LocationUpdatesListenerAdapter() {
            override fun onLocationChanged(location: Location) {
                trySendBlocking(location)
            }
        }

        val interval = TimeUnit.SECONDS.toMillis(
            when (serviceLevel) {
                LocationServiceLevel.UNKNOWN -> 60
                LocationServiceLevel.OK -> 60
                LocationServiceLevel.GOOD -> 20
                LocationServiceLevel.BEST -> 1
            }
        )

        locationManager.requestLocationUpdates(
            provider,
            interval,
            0f,
            locationListener,
            Looper.getMainLooper()
        )

        awaitClose {
            locationManager.removeUpdates(locationListener)
        }
    }

    override fun readinessUpdates(): Flow<Int> = callbackFlow {
        val locationListener = object : LocationUpdatesListenerAdapter() {
            override fun onProviderDisabled(provider: String) {
                if (provider == LocationManager.GPS_PROVIDER) {
                    trySendBlocking(0)
                }
            }

            override fun onProviderEnabled(provider: String) {
                if (provider == LocationManager.GPS_PROVIDER) {
                    trySendBlocking(100)
                }
            }
        }

        locationManager.requestLocationUpdates(
            LocationManager.GPS_PROVIDER,
            TimeUnit.HOURS.toMillis(1),
            0f,
            locationListener,
            Looper.getMainLooper()
        )

        awaitClose {
            locationManager.removeUpdates(locationListener)
        }
    }

    override fun isProviderEnabled(provider: String): Boolean {
        return locationManager.isProviderEnabled(provider)
    }

    // empty abstract class to hide unneeded empty implementations that are not in use
    private abstract class LocationUpdatesListenerAdapter : LocationListener {
        override fun onLocationChanged(location: Location) {
        }

        @Deprecated("Deprecated in Java")
        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {
        }

        override fun onProviderEnabled(provider: String) {
        }

        override fun onProviderDisabled(provider: String) {
        }
    }
}
