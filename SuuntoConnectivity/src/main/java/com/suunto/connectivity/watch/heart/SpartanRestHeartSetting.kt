package com.suunto.connectivity.watch.heart

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import com.suunto.connectivity.watch.SpartanMoshiSetting
import com.suunto.connectivity.watch.userprofile.UserProfileRestHeartItem

class SpartanRestHeartSetting(
    moshi: Moshi,
    serialNumber: String,
    mdsRx: MdsRx
) : SpartanMoshiSetting<UserProfileRestHeartItem>(
    moshi,
    serialNumber,
    mdsRx,
    UserProfileRestHeartItem::class.java
) {

    override val uri = "$MDS_SCHEME_PREFIX$serialNumber/Settings/User/RestHR"

    private val requestAdapter: JsonAdapter<SpartanRestHRValue> =
        moshi.adapter(SpartanRestHRValue::class.java)

    override fun buildRequestJson(value: UserProfileRestHeartItem): String {
        return requestAdapter.toJson(SpartanRestHRValue(value))
    }
}
