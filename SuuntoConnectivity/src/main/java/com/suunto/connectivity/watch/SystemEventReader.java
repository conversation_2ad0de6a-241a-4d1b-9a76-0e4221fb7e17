package com.suunto.connectivity.watch;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.stt.android.data.TimeUtils;
import com.suunto.connectivity.repository.GsonFactory;
import com.suunto.connectivity.sdsmanager.model.MdsContent;
import com.suunto.connectivity.sdsmanager.model.MdsExtensionsKt;
import com.suunto.connectivity.sdsmanager.model.MdsSystemEvent;
import com.suunto.connectivity.sdsmanager.model.MdsSystemEvents;
import com.suunto.connectivity.util.IOUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Locale;
import javax.inject.Inject;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import rx.Emitter;
import rx.Observable;
import rx.Single;
import rx.exceptions.Exceptions;
import rx.functions.Action1;

/**
 * SystemEventReader contains the logic for fetching all system events from
 * a spartan device
 */
public class SystemEventReader {

    private static final Charset UTF_8_CHARSET = Charset.forName("UTF-8");
    private static final String DEVICE_SYSTEM_EVENT_URI = "Device/SystemEvent";
    private static final String SEQUENCE_NUMBER_CONTRACT = "{\"SequenceNumber\":%d}";

    private final DateTimeFormatter systemEventsDateFormatter =
        DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");

    private final Gson gson = GsonFactory.buildGson();

    @Inject
    public SystemEventReader() {
    }

    /**
     * Gets system events from Spartan device and writes them to a temporary file
     *
     * @param spartan Device from which to read the system events
     * @param logDir Pathname of the directory where the temporary file is to be created, or
     * <code>null</code> if the default temporary-file directory is to be used
     * @return Single that emits the generated log file when ready
     */
    public Single<File> writeSystemEventsToFile(@NonNull final WatchBt spartan,
        @Nullable final String logDir) {
        return Single.fromEmitter(fileSingleEmitter -> {
            try {
                File directory = logDir != null ? new File(logDir) : null;
                final File logFile = File.createTempFile(
                    "systemevents_" + spartan.getSerial() + "_",
                    ".log",
                    directory);
                logFile.deleteOnExit();
                final OutputStreamWriter logWriter = new OutputStreamWriter(
                    new FileOutputStream(logFile), UTF_8_CHARSET);

                getSystemEvents(spartan)
                    .doOnCompleted(() -> {
                        // Finish the returned Single with the File written
                        IOUtils.closeQuietly(logWriter);
                        fileSingleEmitter.onSuccess(logFile);
                    })
                    .subscribe(event -> {
                        try {
                            Instant instant = Instant.ofEpochSecond(event.getTimestamp());
                            ZonedDateTime dateTime = ZonedDateTime.ofInstant(instant, ZoneOffset.UTC);
                            logWriter.write(
                                String.format(Locale.US, "#%s %s : %s %s : %s\n",
                                    event.getSequenceNumber(),
                                    systemEventsDateFormatter.format(dateTime),
                                    MdsExtensionsKt.eventType(event),
                                    MdsExtensionsKt.moduleName(event),
                                    event.getEvent()
                                )
                            );
                        } catch (IOException e) {
                            throw Exceptions.propagate(e);
                        }
                    }, throwable -> {
                        IOUtils.closeQuietly(logWriter);
                        fileSingleEmitter.onError(throwable);
                    });
            } catch (IOException ex) {
                throw Exceptions.propagate(ex);
            }
        });
    }

    /**
     * Gets system events from Spartan device
     *
     * @param spartan Spartan device to get the system events from
     * @return Observable emitting all system events in chronological order, starting from the
     * oldest
     */
    public Observable<MdsSystemEvent> getSystemEvents(final WatchBt spartan) {

        // Create emitter to emit all system events
        Observable<List<MdsSystemEvent>> events =
            Observable.create(new Action1<Emitter<List<MdsSystemEvent>>>() {
                Emitter<List<MdsSystemEvent>> emitter;
                long sequenceNumber = 0;

                private void getNextBatch() {
                    // Gets the next batch of events after given sequence number
                    getSystemEvents(spartan, sequenceNumber)
                        .subscribe(mdsSystemEvents -> {
                            // If there were events, then call onNext with them and update
                            // sequence number for next batch
                            if (!mdsSystemEvents.isEmpty()) {
                                emitter.onNext(mdsSystemEvents);
                                sequenceNumber = mdsSystemEvents
                                    .get(mdsSystemEvents.size() - 1).getSequenceNumber() + 1;
                                getNextBatch();
                            } else {
                                // We are done
                                emitter.onCompleted();
                            }
                        }, throwable -> emitter.onError(throwable));
                }

                @Override
                public void call(Emitter<List<MdsSystemEvent>> emitter) {
                    this.emitter = emitter;
                    getNextBatch();
                }
            }, Emitter.BackpressureMode.BUFFER);

        return events.flatMap(Observable::from);
    }

    /**
     * Internal method to get one batch of events from the watch
     *
     * @param spartan Spartan from which to get SystemEvents
     * @param sequenceNumber Sequence number from latest batch, or 0 if supposed to get from the
     * start
     * @return Single emitting the batch as a list
     */
    private Single<List<MdsSystemEvent>> getSystemEvents(WatchBt spartan, long sequenceNumber) {
        String contract = String.format(Locale.US, SEQUENCE_NUMBER_CONTRACT, sequenceNumber);
        return spartan.get(DEVICE_SYSTEM_EVENT_URI, contract)
            .map(s -> {
                TypeToken<MdsContent<MdsSystemEvents>> tt =
                    new TypeToken<MdsContent<MdsSystemEvents>>() {
                    };

                try {
                    MdsContent<MdsSystemEvents> content = gson.getAdapter(tt).fromJson(s);
                    return content.getContent().getEvents();
                } catch (IOException e) {
                    throw Exceptions.propagate(e);
                }
            });
    }
}
