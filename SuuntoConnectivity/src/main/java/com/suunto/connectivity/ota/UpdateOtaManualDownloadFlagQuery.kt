package com.suunto.connectivity.ota

import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import kotlinx.parcelize.Parcelize

@Parcelize
data class UpdateOtaManualDownloadFlagQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_UPDATE_OTA_MANUAL_DOWNLOAD_FLAG
}


