package com.suunto.connectivity.logbook.json;

import com.google.gson.annotations.SerializedName;
import com.stt.android.logbook.SmlZip;
import com.stt.android.logbook.SuuntoLogbookSummaryContent;
import com.suunto.connectivity.logbook.Logbook;
import com.stt.android.logbook.SuuntoLogbookSamples;
import rx.Single;

/**
 * POJO for a logbook entry received from SDS device
 */
@SuppressWarnings({ "UnusedDeclaration" })
public
class LogbookEntryJson implements Logbook.Entry {

    @SerializedName("Id")
    private long id;

    @SerializedName("ModificationTimestamp")
    private long modificationTimestamp;

    @SerializedName("Size")
    private int size;

    public LogbookEntryJson() {
    }

    public LogbookEntryJson(long id, long modificationTimestamp, int size) {
        this.id = id;
        this.modificationTimestamp = modificationTimestamp;
        this.size = size;
    }

    @Override
    public long getId() {
        return id;
    }

    @Override
    public long getModificationTimestamp() {
        return modificationTimestamp;
    }

    @Override
    public int getSize() {
        return size;
    }

    @Override
    public Single<SuuntoLogbookSummaryContent> getSummary() {
        return Single.error(new IllegalStateException("LogbookEntryJson may not fetch summary"));
    }

    @Override
    public Single<SuuntoLogbookSamples> getSamples() {
        return Single.error(new IllegalStateException("LogbookEntryJson may not fetch data"));
    }

    @Override
    public Single<SmlZip> getLogbookSmlZip() {
        return Single.error(new UnsupportedOperationException("LogbookEntryJson may not fetch sml zip"));
    }
}
