package com.suunto.connectivity.weather

import android.content.SharedPreferences
import android.location.Location
import android.location.LocationManager
import android.os.Build
import android.os.Parcel
import android.util.Base64
import androidx.core.content.edit
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.weather.WeatherProtocol
import com.stt.android.domain.weather.WeatherProtocol.Companion.toContract
import com.stt.android.domain.weatherV2.GetWeatherForecastConditionsV2UseCase
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.utils.CoordinateUtils
import com.stt.android.utils.toV2
import com.suunto.connectivity.ServiceCoroutineScope
import com.suunto.connectivity.WeatherPreferences
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.location.GeoCoordinate
import com.suunto.connectivity.location.GnssDate
import com.suunto.connectivity.location.GnssTime
import com.suunto.connectivity.location.GpsLocationProvider
import com.suunto.connectivity.location.LastKnownLocationRequest
import com.suunto.connectivity.location.LocationServiceLevel
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import com.suunto.connectivity.watch.SpartanBt
import com.suunto.connectivity.watch.WatchBt
import dagger.hilt.android.scopes.ServiceScoped
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.timeout
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.internal.UTC
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext
import kotlin.math.pow
import kotlin.system.measureTimeMillis
import kotlin.time.Duration.Companion.seconds

/**
 * To Check if weather need to update and then send to bt device.
 */
@ServiceScoped
class WeatherUpdateModel @Inject constructor(
    @WeatherPreferences private val sharedPreferences: SharedPreferences,
    private val weatherForecastUseCase: GetWeatherForecastConditionsV2UseCase,
    @ServiceCoroutineScope private val serviceScope: CoroutineScope,
    private val gpsLocationProvider: GpsLocationProvider
) {

    companion object {
        // If request interval is less than 15 minutes, use cache data.
        private const val MIN_REQUEST_INTERVAL = 1000 * 60 * 15

        // If distance is within 1KM, use cache data.
        private const val MIN_REQUEST_DISTANCE = 1000

        // Weather prefs key data.
        private const val KEY_WEATHER_DATA = "KEY_DATA"

        private const val KEY_LOCATION_DATA = "KEY_LOCATION"

        private const val ONE_HOUR_IN_MILLISECONDS = 1000 * 60 * 60
    }

    /**
     * Observing connection state change.
     */
    private var stateChangeJob: Job? = null

    /**
     * Observing weather event callback.
     */
    private var weatherCallbackJob: Job? = null

    /**
     * Updating weather data to device.
     */
    private var weatherUpdateJob: Job? = null

    /**
     * Json adapter for converting JSON to WeatherProtocol.
     */
    private val adapter: JsonAdapter<WeatherProtocol> by lazy {
        Moshi.Builder().build().adapter(WeatherProtocol::class.java)
    }

    /**
     * Catch coroutines exception.
     */
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.w(exception, "Update weather to device failed.")
    }

    /**
     * override LifecycleCoroutineScope's CoroutineContext
     */
    private val weatherUpdateContext: CoroutineContext = Dispatchers.IO + exceptionHandler

    /**d
     * Observe connection state change.
     */
    fun startObservingDevice(watchBt: WatchBt) {
        Timber.v("startObservingDevice")

        stateChangeJob?.cancel()
        stateChangeJob = serviceScope.launch(weatherUpdateContext) {
            watchBt.stateChangeObservable
                .toV2()
                .filter {
                    SuuntoDeviceCapabilityInfoProvider[watchBt.suuntoBtDevice.deviceType]
                        .supportsWeather(it.deviceInfo?.capabilities)
                }
                .map { it.isConnected }
                .distinctUntilChanged()
                .asFlow()
                .collect { connected ->
                    Timber.v("watch is connected: $connected")
                    if (connected) {
                        startObservingWeather(watchBt)
                    } else {
                        stopObservingWeather()
                    }
                }
        }
    }

    /**
     * To observe if weather need to update.
     * Ideally it'll callback every 60 minutes.
     */
    private fun startObservingWeather(watchBt: WatchBt) {
        Timber.v("startObservingWeather.")
        weatherCallbackJob?.cancel()
        weatherCallbackJob = serviceScope.launch(weatherUpdateContext) {
            watchBt.subscribeOpenWeatherObservable()
                .toV2()
                // Temp solution to avoid ESW bug(it'll call back twice when subscribed).
                .debounce(1, TimeUnit.SECONDS)
                .filter { it.toBoolean() }
                .asFlow()
                .collect {
                    runSuspendCatching {
                        updateWeatherToDevice(watchBt)
                    }.onFailure { e ->
                        Timber.w(e, "Update weather to device failed.")
                    }
                }
        }
    }

    /**
     * Get weather from backend or cache, then transfer to bt devices.
     */
    suspend fun updateWeatherToDevice(watchBt: WatchBt) = coroutineScope {
        Timber.v("updateWeatherToDevice.")

        weatherUpdateJob?.cancel()
        weatherUpdateJob = launch {
            syncWeatherToWatch(watchBt)
        }
    }

    private suspend fun syncWeatherToWatch(watchBt: WatchBt) {
        val watchWeatherVersion = watchBt.watchSupportedWeatherVersion()
        if (watchWeatherVersion == null) {
            Timber.d("The weather version supported by watch was not found.")
            return
        }

        // It is important to throw an exception if the location retrieval failed to mark the sync operation as Skipped to watch.
        val (location, skipCache) = getWeatherLocation()?.let {
            Timber.d("WeatherLocation succeeded with provider: ${it.provider}")
            it to false
        } ?: getLocationFromPrefs()?.let {
            Timber.d("Using fallback location from prefs, provider: ${it.provider}")
            it to true
        } ?: run {
            Timber.w("Fallback to prefs failed. Throwing NoSuchElementException.")
            throw NoSuchElementException("Location not retrieved, not possible to get weather data")
        }

        val (weather, fromCache) = getWeatherData(location, watchWeatherVersion, skipCache)
        runSuspendCatching {
            val syncDuration = measureTimeMillis {
                if (shouldSyncWeather(watchBt.serial, fromCache)) {
                    // Step 1. Sync weather info.
                    watchBt.putWeatherInfo(toContract(weather.weatherInfo)).await()

                    val iterator = weather.weatherList.iterator()
                    // Step 2. Sync current weather.
                    if (iterator.hasNext()) {
                        val weatherData = iterator.next()
                        watchBt.putCurrentWeather(toContract(weatherData)).await()
                    }
                    // Step 3. Sync forecast hourly weather, max size [WeatherConditionsRepositoryV2.WEATHER_DATA_SIZE].
                    var index = 0
                    while (iterator.hasNext()) {
                        val weatherData = iterator.next()
                        watchBt.putForecastWeather(toContract(weatherData), index).await()
                        index++
                    }
                    // Step 4. Sync daily weather.
                    if (watchWeatherVersion.isNewerThan(WatchWeatherVersion.V1)) {
                        syncDailyWeatherToWatch(watchBt, weather)
                    }
                    // Step 5. Sync daily uvi and hourly Aqi
                    if (watchWeatherVersion.isNewerThan(WatchWeatherVersion.V2)) {
                        syncDailyUviAndHourlyAqi(watchBt, weather)
                    }
                    // Store data sync timestamp for this device
                    sharedPreferences.edit {
                        putLong(watchBt.serial, System.currentTimeMillis())
                    }
                } else {
                    Timber.d("Weather data not synced to watch.")
                }
                // Step 4. Sync time, convert milliseconds to seconds.
                watchBt.putSyncTime(toContract(System.currentTimeMillis() / 1000))
                    .await()

                Timber.v("updateWeatherInfo success.")

                // step 5. Sync last known location
                if (supportedSyncLastKnownLocation(watchBt) && watchBt is SpartanBt) {
                    Timber.v("start getLastKnownLocation.")
                    val instant = Instant.ofEpochMilli(location.time)
                    val utcDateTime = LocalDateTime.ofInstant(instant, UTC.toZoneId())
                    val lastKnownLocationRequest = LastKnownLocationRequest(
                        GeoCoordinate(
                            latitude = (location.latitude * 10.0.pow(7)).toInt(),
                            longitude = (location.longitude * 10.0.pow(7)).toInt(),
                            altitude = location.altitude.toFloat(),
                            utcTime = GnssTime(
                                utcDateTime.hour.toByte(),
                                utcDateTime.minute.toByte(),
                                (utcDateTime.second * 1000).toShort()
                            ),
                            utcDate = GnssDate(
                                utcDateTime.year.toShort(),
                                utcDateTime.month.value.toByte(),
                                utcDateTime.dayOfMonth.toByte()
                            )
                        )
                    )
                    watchBt.putSyncLastKnownLocation(lastKnownLocationRequest).await()
                    Timber.v("Sync last known location success.")
                }
            }
            Timber.v("updateWeatherInfo syncDuration: $syncDuration")
            trackWeatherDataSyncToWatchEvent(watchBt, syncDuration, null)
        }.onFailure { e ->
            Timber.w(e, "failed to sync weather to watch")
            trackWeatherDataSyncToWatchEvent(watchBt, null, e)
            throw e
        }
    }

    private suspend fun syncDailyUviAndHourlyAqi(watchBt: WatchBt, weather: WeatherProtocol) {
        weather.weatherDailyUvi?.forEachIndexed { index, dailyUvi ->
            val contract = toContract(dailyUvi)
            watchBt.putDailyUvi(contract, index).await()
        }
        weather.weatherHourlyAqi?.forEachIndexed { index, hourlyAqi ->
            val contract = toContract(hourlyAqi)
            watchBt.putHourlyAqi(contract, index).await()
        }
    }

    private fun supportedSyncLastKnownLocation(watchBt: WatchBt): Boolean {
        val deviceInfo = watchBt.currentState.deviceInfo
        deviceInfo?.let {
            return SuuntoDeviceCapabilityInfoProvider[watchBt.suuntoBtDevice.deviceType]
                .supportsSyncLastKnownLocation(it.capabilities)
        } ?: return false
    }

    private suspend fun syncDailyWeatherToWatch(watchBt: WatchBt, weather: WeatherProtocol) {
        weather.weatherDaily?.forEachIndexed { index, weatherDaily ->
            val contract = toContract(weatherDaily)
            watchBt.putDailyWeather(contract, index).await()
        }
    }

    private fun shouldSyncWeather(
        serial: String,
        isDataFromCache: Boolean,
    ): Boolean {
        // We used to always send the cached data to the watch, but realised it still took
        // several seconds (around 4.5s when testing on Pixel 7 Pro and Suunto Race).

        // Send the data if it is not from the cache or the time since last sync to this specific
        // device is over MIN_REQUEST_INTERVAL
        if (!isDataFromCache) return true
        val currentTimeMillis = System.currentTimeMillis()
        // Get latest sync timestamp for this device or default to 0 to return true
        val lastSyncTimeMillis = sharedPreferences.getLong(serial, 0)
        return currentTimeMillis - lastSyncTimeMillis >= MIN_REQUEST_INTERVAL
    }

    private suspend fun getWeatherLocation(): Location? = withTimeoutOrNull(30_000) {
        getRecentLastKnownLocation()
            ?: getLocationByProvider(LocationManager.NETWORK_PROVIDER)
            ?: if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                getLocationByProvider(LocationManager.FUSED_PROVIDER)
            } else {
                getLocationByProvider(LocationManager.GPS_PROVIDER)
            }
    }

    private suspend fun getLocationByProvider(provider: String): Location? = runSuspendCatching {
        if (gpsLocationProvider.isProviderEnabled(provider)) {
            gpsLocationProvider.locationUpdates(LocationServiceLevel.BEST, provider)
                .timeout(10.seconds)
                .first()
        } else {
            null
        }
    }.getOrElse { e ->
        Timber.w(e, "Failed to get location by $provider")
        null
    }

    private fun getRecentLastKnownLocation(): Location? = try {
        val now = System.currentTimeMillis()
        val knownLocations = listOfNotNull(
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                gpsLocationProvider.lastLocation(LocationManager.FUSED_PROVIDER)
            } else {
                gpsLocationProvider.lastLocation(LocationManager.GPS_PROVIDER)
            },
            gpsLocationProvider.lastLocation(LocationManager.NETWORK_PROVIDER)
        )

        val recent = knownLocations.filter { now - it.time <= TimeUnit.HOURS.toMillis(1) }
        val mostRecent = recent.maxByOrNull { it.time }

        if (mostRecent == null) {
            knownLocations.forEach { loc ->
                val ageMinutes = TimeUnit.MILLISECONDS.toMinutes(now - loc.time)
                Timber.w("Last known location stale, provider:${loc.provider} age:$ageMinutes minutes")
            }
            if (knownLocations.isEmpty()) {
                Timber.w("Known locations empty. App is likely in the background without background location (Allow all the time) permission.")
            }
        }

        mostRecent
    } catch (e: Exception) {
        Timber.w(e, "Error while getting recent last known location when syncing weather")
        null
    }

    /**
     * Observable for get weather data.
     *
     * @return Weather data and if the data is from the cache
     */
    private suspend fun getWeatherData(
        location: Location,
        watchWeatherVersion: WatchWeatherVersion,
        skipCache: Boolean = false,
    ): Pair<WeatherProtocol, Boolean> = runSuspendCatching {
        val cache = if (!skipCache) getWeatherFromCacheOrNull(location) else null
        if (cache != null) {
            cache to true
        } else {
            weatherForecastUseCase.run(
                GetWeatherForecastConditionsV2UseCase.Params(
                    location.latitude,
                    location.longitude,
                    watchWeatherVersion.toDomain()
                )
            ).also { data ->
                Timber.v("Weather data from backend: $data")
                storeDataToLocal(data)
                storeLocationToPrefs(location)
                trackWeatherDataFromApiEvent(null)
            } to false
        }
    }.getOrElse { e ->
        Timber.w(e, "Error while getting weather info")
        trackWeatherDataFromApiEvent(e)
        throw e
    }

    /**
     * To check if cached weather data is valid.
     */
    private fun getWeatherFromCacheOrNull(currentLocation: Location): WeatherProtocol? {
        val cachedData = loadWeatherDataFromCache() ?: return null
        val cachedLatitude = cachedData.weatherInfo.latitude
        val cachedLongitude = cachedData.weatherInfo.longitude

        if (cachedLatitude != null && cachedLongitude != null) {
            val distance = CoordinateUtils.distanceBetween(
                currentLocation.latitude,
                currentLocation.longitude,
                cachedLatitude,
                cachedLongitude
            ).toInt()
            val isWithinDistance = distance < MIN_REQUEST_DISTANCE

            val requestTime = cachedData.weatherInfo.requestTimeMills
            val currentTime = System.currentTimeMillis()
            val isSameHour =
                (requestTime / ONE_HOUR_IN_MILLISECONDS) == (currentTime / ONE_HOUR_IN_MILLISECONDS)
            val isInInterval = (currentTime - requestTime) < MIN_REQUEST_INTERVAL

            if (isSameHour && isInInterval && isWithinDistance) {
                Timber.v("Weather data from local cache.")
                return cachedData
            }
        }
        return null
    }

    /**
     * Cache data into sharedPreferences.
     */
    private fun storeDataToLocal(
        weatherProtocol: WeatherProtocol
    ) {
        val jsonEntity = adapter.toJson(weatherProtocol)
        sharedPreferences.edit {
            putString(KEY_WEATHER_DATA, jsonEntity)
        }
    }

    /**
     * Load weather data from cache.
     */
    private fun loadWeatherDataFromCache(): WeatherProtocol? {
        return try {
            sharedPreferences.getString(KEY_WEATHER_DATA, null)?.let { jsonContent ->
                adapter.fromJson(jsonContent)
            }
        } catch (e: Exception) {
            Timber.w(e, "loadWeatherDataFromCache failed.")
            null
        }
    }

    private fun storeLocationToPrefs(location: Location) {
        val parcel = Parcel.obtain()
        return try {
            location.writeToParcel(parcel, 0)
            val bytes = parcel.marshall()
            val encoded = Base64.encodeToString(bytes, Base64.DEFAULT)

            if (encoded.isNullOrEmpty()) {
                Timber.w("Failed to encode location to Base64, skipping shared prefs write.")
                return
            }

            sharedPreferences.edit {
                putString(KEY_LOCATION_DATA, encoded)
            }
            Timber.d("Stored location to shared prefs successfully.")
        } catch (e: Exception) {
            Timber.w(e, "Failed to marshall and store location to shared prefs.")
        } finally {
            parcel.recycle()
        }
    }

    private fun getLocationFromPrefs(): Location? {
        return try {
            val encoded = sharedPreferences.getString(KEY_LOCATION_DATA, null)
            if (encoded.isNullOrEmpty()) {
                Timber.w("No location data found in shared prefs.")
                return null
            }

            val bytes = Base64.decode(encoded, Base64.DEFAULT)
            val parcel = Parcel.obtain().apply {
                unmarshall(bytes, 0, bytes.size)
                setDataPosition(0)
            }

            val location = Location.CREATOR.createFromParcel(parcel)
            parcel.recycle()

            if (location.latitude == 0.0 && location.longitude == 0.0) {
                Timber.w("Decoded location appears invalid (0,0), skipping.")
                return null
            }

            val ageMinutes =
                TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis() - location.time)
            Timber.w("Falling back to an old weather location from shared prefs, provider:${location.provider} age:$ageMinutes minutes")

            location
        } catch (e: Exception) {
            Timber.w(e, "getLocationFromPrefs failed during decode or unmarshall.")
            null
        }
    }

    /**
     * Stop observing weather.
     */
    private fun stopObservingWeather() {
        weatherCallbackJob?.cancel()
        weatherCallbackJob = null

        weatherUpdateJob?.cancel()
        weatherUpdateJob = null
    }

    /**
     * Track amplitude analytics events while fetching weather data from api.
     */
    private fun trackWeatherDataFromApiEvent(exception: Throwable? = null) {
        val analyticsProperties = AnalyticsProperties().apply {
            val result = if (exception == null) {
                AnalyticsPropertyValue.ExportResult.OK
            } else {
                AnalyticsPropertyValue.ExportResult.ERROR
            }

            put(AnalyticsEventProperty.FORECAST_RESULT, result)
            put(AnalyticsEventProperty.UV_RESULT, result)
            put(AnalyticsEventProperty.AIR_QUALITY_RESULT, result)
            put(AnalyticsEventProperty.OVERALL_RESULT, result)
            put(
                AnalyticsEventProperty.REAL_TIME_WEATHER_RESULT,
                AnalyticsPropertyValue.ExportResult.NOT_INCLUDED
            )
            if (exception is AskoError) {
                put(AnalyticsEventProperty.ERROR_CODE, exception.code)
            }
        }

        AnalyticsUtils.sendAnyEvent(
            AnalyticsEvent.WATCH_WEATHER_DATA_FETCH_FROM_API,
            analyticsProperties
        )
    }

    /**
     * Track amplitude analytics events while sync weather data to watch.
     */
    private fun trackWeatherDataSyncToWatchEvent(
        watchBt: WatchBt,
        syncDuration: Long? = null,
        exception: Throwable? = null,
    ) {
        try {
            val analyticsProperties = AnalyticsProperties().apply {
                val deviceInfo = watchBt.currentState.deviceInfo
                if (deviceInfo != null) {
                    val watchModel =
                        AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(deviceInfo.variant)
                    put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
                    put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, deviceInfo.swVersion)
                }
                if (syncDuration != null) {
                    put(AnalyticsEventProperty.SUUNTO_SYNC_DURATION, syncDuration / 1000)
                }
                var result = AnalyticsPropertyValue.ExportResult.OK
                if (exception != null) {
                    put(AnalyticsEventProperty.ERROR_CODE, exception.message)
                    result = AnalyticsPropertyValue.ExportResult.ERROR
                }
                put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, watchBt.serial)
                put(AnalyticsEventProperty.RESULT, result)
            }

            AnalyticsUtils.sendAnyEvent(
                AnalyticsEvent.WATCH_WEATHER_DATA_SYNC_TO_WATCH,
                analyticsProperties
            )
        } catch (e: Exception) {
            Timber.w(e, "Error while sending weather sync event")
        }
    }
}
