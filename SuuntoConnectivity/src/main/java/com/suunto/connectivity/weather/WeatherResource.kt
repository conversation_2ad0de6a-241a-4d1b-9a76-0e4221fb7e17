package com.suunto.connectivity.weather

import com.stt.android.utils.toV1
import com.stt.android.utils.traceCompletableV2
import com.suunto.connectivity.repository.SyncResult
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.SyncStepResult
import com.suunto.connectivity.watch.WatchBt
import kotlinx.coroutines.rx2.rxCompletable
import rx.Completable
import timber.log.Timber
import javax.inject.Inject

class WeatherResource
@Inject constructor(
    private val weatherUpdateModel: WeatherUpdateModel
) {

    fun sync(
        watchBt: WatchBt,
        builder: SpartanSyncResult.Builder
    ): Completable {
        var syncStartTimestamp = 0L
        return rxCompletable {
            syncStartTimestamp = System.currentTimeMillis()
            weatherUpdateModel.updateWeatherToDevice(watchBt)
            builder.weatherResult(
                SyncStepResult(
                    syncResult = SyncResult.success(),
                    syncDuration = System.currentTimeMillis() - syncStartTimestamp
                )
            )
        }.onErrorComplete { e ->
            Timber.w(e, "Weather sync failed")
            builder.weatherResult(
                SyncStepResult(
                    syncResult = SyncResult.failed(e),
                    syncDuration = System.currentTimeMillis() - syncStartTimestamp
                )
            )
            return@onErrorComplete true
        }.traceCompletableV2("SyncWeatherResource")
            .toV1()
    }

    fun getSyncState(): SyncState = SyncState(SyncState.SYNCING_WEATHER)
}
