package com.stt.android.di

import com.stt.android.chart.api.ChartNavigator
import com.stt.android.chart.impl.ChartNavigatorImpl
import com.stt.android.diary.DiaryNavigator
import com.stt.android.diary.DiaryNavigatorImpl
import com.stt.android.social.userprofile.BaseUserProfileActivity
import com.stt.android.social.userprofile.UserProfileNavigator
import com.stt.android.tutorial.api.TutorialNavigator
import com.stt.android.tutorial.impl.TutorialNavigatorImpl
import dagger.Binds
import dagger.Module

@Module
abstract class NavigatorModule {
    @Binds
    abstract fun bindUserProfileNavigator(navigator: BaseUserProfileActivity.Navigator): UserProfileNavigator

    @Binds
    abstract fun bindChartNavigator(navigator: ChartNavigatorImpl): ChartNavigator

    @Binds
    abstract fun bindTutorialNavigator(navigator: TutorialNavigatorImpl): TutorialNavigator

    @Binds
    abstract fun bindDiaryNavigator(navigator: DiaryNavigatorImpl): DiaryNavigator
}
