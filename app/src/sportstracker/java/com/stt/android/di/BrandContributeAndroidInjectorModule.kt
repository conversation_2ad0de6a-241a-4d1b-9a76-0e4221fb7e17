package com.stt.android.di

import com.stt.android.newemail.EmailCheckModule
import com.stt.android.social.personalrecord.PersonalRecordModule
import com.stt.android.systemwidget.PremiumRequiredSystemWidgetModule
import dagger.Module

@Module(
    includes = [
        PremiumRequiredSystemWidgetModule::class,
        EmailCheckModule::class,
        PersonalRecordModule::class
    ]
)
abstract class BrandContributeAndroidInjectorModule : ContributeAndroidInjectorModule()
