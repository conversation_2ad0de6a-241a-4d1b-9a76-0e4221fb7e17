package com.stt.android.maps.amap.delegate

import android.graphics.Point
import com.amap.api.maps.Projection
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.VisibleRegion
import com.stt.android.maps.amap.toAMap
import com.stt.android.maps.amap.toGoogle
import com.stt.android.maps.delegate.ProjectionDelegate

class AMapProjectionDelegate(private val projection: Projection) : ProjectionDelegate {

    override fun fromScreenLocation(point: Point): LatLng = projection.fromScreenLocation(point).toGoogle()

    override fun getVisibleRegion(): VisibleRegion = projection.visibleRegion.toGoogle()

    override fun toScreenLocation(location: LatLng): Point = projection.toScreenLocation(location.toAMap())
}
