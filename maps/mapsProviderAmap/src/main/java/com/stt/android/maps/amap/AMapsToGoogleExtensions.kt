package com.stt.android.maps.amap

import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.LatLngBounds
import com.amap.api.maps.model.VisibleRegion

fun LatLng.toGoogle(): com.google.android.gms.maps.model.LatLng {
    return CoordinateTransformUtils.gcj02ToWgs84(this.latitude, this.longitude)
}

fun VisibleRegion.toGoogle(): com.google.android.gms.maps.model.VisibleRegion {
    return com.google.android.gms.maps.model.VisibleRegion(
        this.nearLeft.toGoogle(),
        this.nearRight.toGoogle(),
        this.farLeft.toGoogle(),
        this.farRight.toGoogle(),
        this.latLngBounds.toGoogle()
    )
}

fun LatLngBounds.toGoogle(): com.google.android.gms.maps.model.LatLngBounds {
    return com.google.android.gms.maps.model.LatLngBounds(
        this.southwest.toGoogle(), this.northeast.toGoogle()
    )
}
