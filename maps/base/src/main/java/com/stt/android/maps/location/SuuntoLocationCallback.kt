package com.stt.android.maps.location

import android.location.Location

/**
 * Interface for delivering the result of a single location query.
 */
interface SuuntoLocationCallback {

    /**
     * Called when [location] is retrieved successfully.
     */
    fun onSuccess(location: Location)

    /**
     * Called when getting location fails for some reason.
     */
    fun onFailure(exception: Exception)
}
