package com.stt.android.maps.extensions

import com.google.android.gms.maps.GoogleMap
import com.stt.android.coroutines.throttleLatest
import com.stt.android.maps.SuuntoMap
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * Gets a flow that emits whenever the map camera is moved.
 */
fun SuuntoMap.getOnCameraMoveFlow() = callbackFlow {
    val cameraMoveListener = GoogleMap.OnCameraMoveListener { trySend(Unit) }
    addOnCameraMoveListener(cameraMoveListener)
    awaitClose { removeOnCameraMoveListener(cameraMoveListener) }
}

/**
 * Adds a camera move [listener] that is invoked at most with a frequency defined by [intervalMs].
 * The listener is removed automatically when the given [scope] is cancelled. Do not try to remove
 * the listener manually with [SuuntoMap.removeOnCameraMoveListener], because it will not work.
 */
fun SuuntoMap.addScopedOnCameraMoveListener(
    scope: CoroutineScope,
    intervalMs: Long,
    listener: GoogleMap.OnCameraMoveListener
): Job = getOnCameraMoveFlow()
    .throttleLatest(intervalMs)
    .onEach { listener.onCameraMove() }
    .launchIn(scope)
