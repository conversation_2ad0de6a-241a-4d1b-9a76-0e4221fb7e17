package com.stt.android.maps

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.utils.tileXToLon
import com.stt.android.utils.tileYToLat

/**
 * Gets latitude and longitude bounds of a tile with [x], [y] and [zoom].
 */
fun tileToLatLngBounds(x: Int, y: Int, zoom: Int): LatLngBounds {
    return LatLngBounds(
        LatLng(tileYToLat(y + 1, zoom), tileXToLon(x, zoom)),
        LatLng(tileYToLat(y, zoom), tileXToLon(x + 1, zoom))
    )
}
