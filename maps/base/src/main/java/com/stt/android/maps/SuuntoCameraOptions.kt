package com.stt.android.maps

import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import kotlinx.parcelize.Parcelize

/**
 * Various options for describing the viewpoint of a map. All fields are optional.
 * When camera is moved using these options, the camera keeps the current value of the properties
 * that are null.
 */
@Parcelize
data class SuuntoCameraOptions(
    val target: LatLng? = null,
    val zoom: Float? = null,
    val bearing: Float? = null,
    val tilt: Float? = null,
) : Parcelable {

    // The builder exists to help usage from Java.
    // To be removed when all code is converted to Kotlin.
    class Builder {
        var target: LatLng? = null
        var zoom: Float? = null
        var bearing: Float? = null
        var tilt: Float? = null

        fun target(target: LatLng?) = apply { this.target = target }
        fun zoom(zoom: Float?) = apply { this.zoom = zoom }
        fun bearing(bearing: Float?) = apply { this.bearing = bearing }
        fun tilt(tilt: Float?) = apply { this.tilt = tilt }

        fun build() = SuuntoCameraOptions(target, zoom, bearing, tilt)
    }

    companion object {

        @JvmStatic
        fun fromLatLngZoom(latLng: LatLng, zoom: Float) =
            SuuntoCameraOptions(target = latLng, zoom = zoom)

        @JvmStatic
        fun fromCameraPosition(cameraPosition: SuuntoCameraPosition) =
            with(cameraPosition) {
                SuuntoCameraOptions(
                    target = target,
                    zoom = zoom,
                    bearing = bearing,
                    tilt = tilt
                )
            }
    }
}
