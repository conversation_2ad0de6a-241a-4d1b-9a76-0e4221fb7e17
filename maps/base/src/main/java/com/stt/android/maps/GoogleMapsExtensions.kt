package com.stt.android.maps

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.utils.latToMercatorY
import com.stt.android.utils.lonToMercatorX
import com.stt.android.utils.mercatorXtoLon
import com.stt.android.utils.mercatorYToLat

fun LatLngBounds.withPadding(padding: Int, width: Int, height: Int): LatLngBounds {
    val paddingPixels = minOf(padding, width / 2 - 1, height / 2 - 1)

    var topY = latToMercatorY(northeast.latitude)
    var leftX = lonToMercatorX(southwest.longitude)
    var bottomY = latToMercatorY(southwest.latitude)
    var rightX = lonToMercatorX(northeast.longitude)

    val xRange = rightX - leftX
    val yRange = bottomY - topY

    val paddingX = paddingPixels.toDouble() / (width - paddingPixels * 2) * xRange
    val paddingY = paddingPixels.toDouble() / (height - paddingPixels * 2) * yRange

    topY = (topY - paddingY).coerceAtLeast(0.0)
    leftX = (leftX - paddingX).coerceAtLeast(0.0)
    bottomY = (bottomY + paddingY).coerceAtMost(1.0)
    rightX = (rightX + paddingX).coerceAtMost(1.0)

    return LatLngBounds(
        LatLng(mercatorYToLat(bottomY), mercatorXtoLon(leftX)),
        LatLng(mercatorYToLat(topY), mercatorXtoLon(rightX))
    )
}
