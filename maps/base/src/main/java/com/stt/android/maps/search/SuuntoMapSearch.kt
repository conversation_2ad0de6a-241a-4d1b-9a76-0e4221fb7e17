package com.stt.android.maps.search

interface SuuntoMapSearch {

    fun startSearch(searchQuery: SuuntoSearchQuery, callback: SuuntoSearchCallback)

    fun stop()
}

data class SuuntoSearchQuery(
    val query: String,
    val city: String? = "",
    val searchLimit: Int,
    val latitude: Double,
    val longitude: Double,
    val gpsType: LocationGpsType
)

enum class LocationGpsType {
    WG84,
    GCJ02,
}
