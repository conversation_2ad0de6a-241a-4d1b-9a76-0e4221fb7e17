package com.stt.android.maps.mapbox.delegate

import com.mapbox.maps.Style
import com.mapbox.maps.extension.style.expressions.dsl.generated.neq
import com.mapbox.maps.extension.style.expressions.dsl.generated.not
import com.mapbox.maps.extension.style.expressions.dsl.generated.step
import com.mapbox.maps.extension.style.expressions.generated.Expression
import com.mapbox.maps.extension.style.expressions.generated.Expression.Companion.interpolate
import com.mapbox.maps.extension.style.expressions.generated.Expression.Companion.match
import com.mapbox.maps.extension.style.layers.Layer
import com.mapbox.maps.extension.style.layers.addLayer
import com.mapbox.maps.extension.style.layers.addLayerAbove
import com.mapbox.maps.extension.style.layers.addLayerBelow
import com.mapbox.maps.extension.style.layers.generated.CircleLayer
import com.mapbox.maps.extension.style.layers.generated.FillLayer
import com.mapbox.maps.extension.style.layers.generated.LineLayer
import com.mapbox.maps.extension.style.layers.generated.RasterLayer
import com.mapbox.maps.extension.style.layers.generated.SymbolLayer
import com.mapbox.maps.extension.style.layers.getLayer
import com.mapbox.maps.extension.style.layers.properties.generated.LineCap
import com.mapbox.maps.extension.style.layers.properties.generated.LineJoin
import com.mapbox.maps.extension.style.layers.properties.generated.SymbolPlacement
import com.mapbox.maps.extension.style.layers.properties.generated.TextPitchAlignment
import com.mapbox.maps.extension.style.layers.properties.generated.TextRotationAlignment
import com.mapbox.maps.extension.style.layers.properties.generated.Visibility
import com.mapbox.maps.extension.style.sources.Source
import com.mapbox.maps.extension.style.sources.TileSet
import com.mapbox.maps.extension.style.sources.addSource
import com.mapbox.maps.extension.style.sources.generated.GeoJsonSource
import com.mapbox.maps.extension.style.sources.generated.RasterSource
import com.mapbox.maps.extension.style.sources.generated.VectorSource
import com.mapbox.maps.extension.style.sources.getSource
import com.stt.android.maps.StartingPointFeature
import com.stt.android.maps.SuuntoHeatmapLayerOptions
import com.stt.android.maps.SuuntoLayerType
import com.stt.android.maps.SuuntoLayerTypeOptions
import com.stt.android.maps.SuuntoOfflineRegionTileOverlayOptions
import com.stt.android.maps.SuuntoRasterTileLayerOptions
import com.stt.android.maps.SuuntoRoadSurfaceLayerOptions
import com.stt.android.maps.SuuntoSelectedTopRoutesLayerOptions
import com.stt.android.maps.SuuntoStartingPointsLayerOptions
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRoutesLayerOptions
import com.stt.android.maps.delegate.TileOverlayDelegate
import com.stt.android.maps.mapbox.toMapBox
import java.util.UUID

class MapboxTileOverlayDelegate(
    override val options: SuuntoTileOverlayOptions,
    private val mapDelegate: MapboxMapDelegate
) : TileOverlayDelegate {

    private var style: Style? = null
    private var source: Source? = null
    private var layers = mutableListOf<Layer>()

    /**
     * true if [remove] has not been called on this delegate and this delegate is safe to use.
     * Once removed delegate should not be reused, instead a new one should be created to replace it.
     */
    private var isValid = true

    fun createSourceAndLayers(style: Style) {
        if (!isValid) {
            // Some async OnMapStyleLoaded callback is trying to attach this delegate to the style
            // after call to remove. Adding once removed delegate to a Style isn't supported.
            return
        }

        this.style = style

        val existingLayer = addExistingLayer(options)
        if (existingLayer != null) {
            layers.add(existingLayer)
        } else {
            val source = addSource(options)
            this.source = source
            layers.addAll(addLayers(options, source))
        }
    }

    override fun remove() {
        removeSourceAndLayers()
        style = null
        isValid = false
        mapDelegate.onTileOverlayRemoved(this)
    }

    internal fun invalidateSourceAndLayer() {
        style = null
        source = null
        layers.clear()
    }

    private fun removeSourceAndLayers() {
        source?.let {
            style?.removeStyleSource(it.sourceId)
            source = null

            for (layer in layers) {
                style?.removeStyleLayer(layer.layerId)
            }
            layers.clear()
        } ?: run {
            // Overlay without a source is using a layer that is included in the style.
            // Just hide the layer instead of removing it.
            for (layer in layers) {
                layer.setVisible(false)
            }
            layers.clear()
        }
    }

    override fun setOpacity(opacity: Float) {
        // Update options in case the overlay needs to be recreated when map style changes
        options.opacity = opacity
        opacity.toDouble().let {
            for (layer in layers) {
                when (layer) {
                    is RasterLayer -> layer.rasterOpacity(it)
                    is LineLayer -> layer.lineOpacity(it)
                    is CircleLayer -> layer.circleOpacity(it)
                    is FillLayer -> layer.fillOpacity(it)
                    is SymbolLayer -> {
                        layer.iconOpacity(it)
                        layer.textOpacity(it)
                    }
                }
            }
        }
    }

    override fun setVisible(visible: Boolean) {
        options.visible = visible
        for (layer in layers) {
            layer.setVisible(visible)
        }
    }

    override fun setLayerColor(color: Int) {
        layers.forEach {
            if (it is FillLayer) {
                it.fillColor(color)
            }
            if (it is LineLayer) {
                it.lineColor(color)
            }
        }
    }

    override fun changeLayerZIndex(zIndex: Float) {
        val style = style
        layers.forEach {
            if (style != null) {
                style.removeStyleLayer(it.layerId)
                addLayerToStyle(it, zIndex, style)
            }
        }
    }

    private fun addSource(options: SuuntoTileOverlayOptions): Source {
        options.tileSource?.let {
            val sourceId = UUID.randomUUID().toString()
            val tileSetBuilder = TileSet.Builder(
                MapboxMapDelegate.TILE_JSON_VERSION,
                it.tileEndpoints
            )
                .scheme(it.scheme.toMapBox())
                .minZoom(it.minZoom)
                .maxZoom(it.maxZoom)
            it.bounds?.let { sourceBounds ->
                tileSetBuilder.bounds(
                    listOf(
                        sourceBounds.southwest.longitude,
                        sourceBounds.southwest.latitude,
                        sourceBounds.northeast.longitude,
                        sourceBounds.northeast.latitude
                    )
                )
            }

            val source = if (options.layerTypeOptions.isRaster) {
                RasterSource.Builder(sourceId)
                    .tileSet(tileSetBuilder.build())
                    .tileSize(it.tileSize.toLong())
                    .build()
            } else if (options.layerTypeOptions.isGeoJson) {
                // use options sourceId, when click geoJson layer, only can get sourceId, so you can find the layer that is clicked by sourceId
                GeoJsonSource.Builder(options.sourceId)
                    .url(it.tileEndpoints.first())
                    .build()
            } else {
                VectorSource.Builder(UUID.randomUUID().toString())
                    .tileSet(tileSetBuilder.build())
                    .build()
            }

            style?.addSource(source)

            return source
        } ?: throw IllegalArgumentException("tileSource missing")
    }

    private fun addExistingLayer(options: SuuntoTileOverlayOptions): Layer? {
        val layerId = options.layerTypeOptions.getLayerId()
        return if (style?.styleLayerExists(layerId) == true) {
            style?.getLayer(layerId)?.apply {
                fixBlurryHeatmaps(options.layerTypeOptions)
                setVisible(options.visible ?: true)
            }
        } else {
            null
        }
    }

    private fun addLayers(options: SuuntoTileOverlayOptions, source: Source): List<Layer> {
        val layers = createLayers(options, source)

        val style = style
        if (style != null) {
            for (layer in layers) {
                addLayerToStyle(layer, options.zIndex, style)
            }
        }

        return layers
    }

    private fun addLayerToStyle(layer: Layer, zIndex: Float, style: Style) {
        val layerType = SuuntoLayerType.fromLayerId(layer.layerId) ?: return

        // Find an existing layer that should be just below the layer we are adding.
        val layerIdBelow = getLayerIdBelow(layerType, zIndex, style)
        if (layerIdBelow != null) {
            style.addLayerAbove(layer, layerIdBelow)
            return
        }

        // Find an existing layer that should be just above the layer we are adding.
        val layerIdAbove = getLayerIdAbove(layerType, style)
        if (layerIdAbove != null) {
            style.addLayerBelow(layer, layerIdAbove)
            return
        }

        // Fallback to adding the layer as the topmost layer.
        style.addLayer(layer)
    }

    private fun createLayers(options: SuuntoTileOverlayOptions, source: Source): List<Layer> {
        val layers = when (val layerTypeOptions = options.layerTypeOptions) {
            is SuuntoRasterTileLayerOptions,
            is SuuntoHeatmapLayerOptions ->
                listOf(createRasterLayer(options, layerTypeOptions, source))

            is SuuntoRoadSurfaceLayerOptions ->
                createRoadSurfaceLayers(
                    options,
                    layerTypeOptions,
                    source
                )

            is SuuntoTopRoutesLayerOptions ->
                listOf(
                    createTopRoutesLayer(
                        options,
                        layerTypeOptions.getLayerId(),
                        layerTypeOptions.lineColor,
                        false,
                        source
                    )
                )

            is SuuntoSelectedTopRoutesLayerOptions ->
                listOf(
                    createTopRoutesLayer(
                        options,
                        layerTypeOptions.getLayerId(),
                        layerTypeOptions.lineColor,
                        true,
                        source
                    )
                )

            is SuuntoStartingPointsLayerOptions ->
                listOf(
                    createStartingPointsLayer(
                        options,
                        layerTypeOptions,
                        source
                    )
                )

            is SuuntoOfflineRegionTileOverlayOptions ->
                listOf(
                    if (layerTypeOptions.isMask) {
                        createGeoJsonFillLayer(
                            options,
                            layerTypeOptions.getLayerId(),
                            layerTypeOptions.color,
                            source,
                        )
                    } else {
                        createGeoJsonLineLayer(
                            options,
                            layerTypeOptions.getLayerId(),
                            layerTypeOptions.color,
                            source,
                        )
                    }
                )
        }

        for (layer in layers) {
            options.visible?.let { layer.setVisible(it) }
        }

        return layers
    }

    private fun createRasterLayer(
        options: SuuntoTileOverlayOptions,
        layerTypeOptions: SuuntoLayerTypeOptions,
        source: Source
    ) = RasterLayer(
        layerTypeOptions.getLayerId(),
        source.sourceId
    ).apply {
        fixBlurryHeatmaps(layerTypeOptions)

        options.opacity?.let {
            rasterOpacity(it.toDouble())
        }
    }

    private fun createRoadSurfaceLayers(
        options: SuuntoTileOverlayOptions,
        layerTypeOptions: SuuntoRoadSurfaceLayerOptions,
        source: Source
    ): List<Layer> {
        return listOf(
            createRoadSurfaceStrokeLayer(options, layerTypeOptions, source),
            createRoadSurfaceFillLayer(options, layerTypeOptions, source),
            createRoadSurfaceLabelLayer(layerTypeOptions, source),
        )
    }

    private fun createRoadSurfaceStrokeLayer(
        options: SuuntoTileOverlayOptions,
        layerTypeOptions: SuuntoRoadSurfaceLayerOptions,
        source: Source
    ): Layer = LineLayer(
        layerTypeOptions.getLayerId("stroke"),
        source.sourceId
    ).apply {
        sourceLayer(layerTypeOptions.name)
        lineColor(layerTypeOptions.strokeColor)
        lineWidth(
            // Interpolate line width linearly based on zoom level.
            // See https://docs.mapbox.com/mapbox-gl-js/style-spec/expressions/#ramps-scales-curves
            interpolate {
                linear()
                zoom()
                literal(6)
                literal(3)
                literal(11)
                literal(6)
                literal(14)
                literal(7.5)
                literal(16)
                literal(9)
                literal(22)
                literal(14)
            }
        )
        lineCap(LineCap.ROUND)
        lineJoin(LineJoin.ROUND)

        createRoadSurfaceFilterExpression(layerTypeOptions)?.let {
            filter(it)
        }

        lineOpacity(
            // Fade in between zoom levels minZoom and minZoom + 1.
            // See https://cubic-bezier.com/#.4,1,1,1 for the curve.
            interpolate {
                cubicBezier {
                    literal(0.4)
                    literal(1)
                    literal(1)
                    literal(1)
                }
                zoom()
                literal(options.tileSource?.minZoom?.toDouble() ?: 0.0)
                literal(0)
                literal((options.tileSource?.minZoom?.toDouble() ?: 0.0) + 1)
                literal(1)
            }
        )
    }

    private fun createRoadSurfaceFillLayer(
        options: SuuntoTileOverlayOptions,
        layerTypeOptions: SuuntoRoadSurfaceLayerOptions,
        source: Source
    ): Layer = LineLayer(
        layerTypeOptions.getLayerId("fill"),
        source.sourceId
    ).apply {
        sourceLayer(layerTypeOptions.name)
        lineColor(layerTypeOptions.fillColor)
        lineWidth(
            interpolate {
                linear()
                zoom()
                literal(6)
                literal(2)
                literal(11)
                literal(4)
                literal(14)
                literal(5)
                literal(16)
                literal(6)
                literal(22)
                literal(8)
            }
        )
        lineCap(LineCap.ROUND)
        lineJoin(LineJoin.ROUND)

        createRoadSurfaceFilterExpression(layerTypeOptions)?.let {
            filter(it)
        }

        lineOpacity(
            interpolate {
                cubicBezier {
                    literal(0.4)
                    literal(1)
                    literal(1)
                    literal(1)
                }
                zoom()
                literal(options.tileSource?.minZoom?.toDouble() ?: 0.0)
                literal(0)
                literal((options.tileSource?.minZoom?.toDouble() ?: 0.0) + 1)
                literal(1)
            }
        )
    }

    private fun createRoadSurfaceLabelLayer(
        layerTypeOptions: SuuntoRoadSurfaceLayerOptions,
        source: Source
    ): Layer = SymbolLayer(
        // Override layer type for the label layer so that the layer gets inserted in the correct
        // position compared to other layers.
        SuuntoLayerType.ROAD_SURFACE_LABEL.getLayerId(layerTypeOptions.name),
        source.sourceId
    ).apply {
        sourceLayer(layerTypeOptions.name)
        minZoom(11.0)
        textField(
            // Get the value of surface property and match it with translations in the
            // surfaceTypeTranslations map.
            match {
                get("surface")
                // Each surface type and its translation creates an entry
                // for the match expression.
                for (surfaceType in layerTypeOptions.surfaceTypeTranslations) {
                    literal(surfaceType.key)
                    literal(surfaceType.value)
                }
                // The raw value of the surface property is used as a fallback for the text field
                // content if no translation is found.
                get("surface")
            }
        )
        textSize(
            interpolate {
                linear()
                zoom()
                literal(9)
                match {
                    get("highway")
                    any {
                        literal("motorway")
                        literal("trunk")
                        literal("primary")
                        literal("secondary")
                        literal("tertiary")
                    }
                    literal(10)
                    literal(9)
                }
                literal(20)
                match {
                    get("highway")
                    any {
                        literal("motorway")
                        literal("trunk")
                        literal("primary")
                        literal("secondary")
                        literal("tertiary")
                    }
                    literal(15)
                    literal(14)
                }
            }
        )
        textMaxAngle(30.0)
        textFont(listOf("Open Sans Bold", "Arial Unicode MS Regular"))
        symbolPlacement(SymbolPlacement.LINE)
        symbolSpacing(250.0)
        textPadding(1.0)
        textRotationAlignment(TextRotationAlignment.MAP)
        textPitchAlignment(TextPitchAlignment.VIEWPORT)
        textLetterSpacing(0.01)
        textColor(layerTypeOptions.labelColor)
        textHaloColor("#ffffff")
        textHaloWidth(2.0)
        textOpacity(
            step {
                zoom()
                match {
                    get("highway")
                    array {
                        literal("path")
                        literal("aerialway")
                    }
                    literal(0)
                    literal(1)
                }
                stop {
                    literal(14)
                    literal(1)
                }
            }
        )

        createRoadSurfaceFilterExpression(layerTypeOptions)?.let {
            filter(it)
        }
    }

    private fun createRoadSurfaceFilterExpression(
        layerTypeOptions: SuuntoRoadSurfaceLayerOptions
    ): Expression? {
        return if (layerTypeOptions.hideCyclingForbiddenRoads) {
            neq {
                get("bicycle")
                literal("no")
            }
        } else {
            null
        }
    }

    private fun createGeoJsonLineLayer(
        options: SuuntoTileOverlayOptions,
        layerId: String,
        lineColor: Int,
        source: Source,
    ): Layer = LineLayer(
        layerId,
        source.sourceId
    ).apply {
        lineColor(lineColor)
        lineWidth(
            interpolate {
                linear()
                zoom()
                literal(6)
                literal(2)
                literal(11)
                literal(4)
                literal(14)
                literal(5)
                literal(16)
                literal(6)
                literal(22)
                literal(8)
            }
        )
        lineCap(LineCap.ROUND)
        lineJoin(LineJoin.ROUND)

        options.opacity?.let { opacity ->
            lineOpacity(opacity.toDouble())
        }
    }

    private fun createGeoJsonFillLayer(
        options: SuuntoTileOverlayOptions,
        layerId: String,
        fillColor: Int,
        source: Source,
    ): Layer = FillLayer(
        layerId,
        source.sourceId
    ).apply {
        fillColor(fillColor)
        options.opacity?.let { opacity ->
            fillOpacity(opacity.toDouble())
        }
    }

    private fun createTopRoutesLayer(
        options: SuuntoTileOverlayOptions,
        layerId: String,
        lineColor: Int,
        selectedRoutes: Boolean,
        source: Source
    ): Layer =
        LineLayer(
            layerId,
            source.sourceId
        ).apply {
            if (selectedRoutes) {
                filter(not(true)) // Show nothing when initialized
            }
            lineWidth(
                interpolate {
                    linear()
                    zoom()
                    literal(9)
                    literal(1)
                    literal(12)
                    literal(2)
                    literal(16)
                    literal(8)
                }
            )

            lineColor(lineColor)
            options.opacity?.let { opacity ->
                lineOpacity(opacity.toDouble())
            }

            sourceLayer("routes")
        }

    private fun createStartingPointsLayer(
        options: SuuntoTileOverlayOptions,
        layerTypeOptions: SuuntoStartingPointsLayerOptions,
        source: Source
    ): Layer = CircleLayer(
        layerTypeOptions.getLayerId(),
        source.sourceId
    ).apply {
        val popularityPropertyKey = StartingPointFeature.PROPERTY_KEY_POPULARITY
        val localMaxPropertyKey = StartingPointFeature.PROPERTY_KEY_LOCAL_MAX
        circleRadius(
            interpolate {
                linear()
                zoom()
                stop {
                    literal(9)
                    step {
                        division {
                            get { literal(popularityPropertyKey) }
                            get { literal(localMaxPropertyKey) }
                        }
                        literal(2.0)
                        stop {
                            literal(0.0)
                            literal(2.0)
                        }
                        stop {
                            literal(0.25)
                            literal(3.0)
                        }
                        stop {
                            literal(0.5)
                            literal(4.0)
                        }
                    }
                }
                stop {
                    literal(12.0)
                    step {
                        division {
                            get { literal(popularityPropertyKey) }
                            get { literal(localMaxPropertyKey) }
                        }
                        literal(3.0)
                        stop {
                            literal(0.0)
                            literal(3.0)
                        }
                        stop {
                            literal(0.25)
                            literal(5.0)
                        }
                        stop {
                            literal(0.5)
                            literal(7.0)
                        }
                    }
                }
                stop {
                    literal(16.0)
                    step {
                        division {
                            get { literal(popularityPropertyKey) }
                            get { literal(localMaxPropertyKey) }
                        }
                        literal(10.0)
                        stop {
                            literal(0.0)
                            literal(10.0)
                        }
                        stop {
                            literal(0.25)
                            literal(20.0)
                        }
                        stop {
                            literal(0.5)
                            literal(30.0)
                        }
                    }
                }
            }
        )
        circleColor(layerTypeOptions.circleColor)
        circleStrokeWidth(1.0)
        circleStrokeColor(layerTypeOptions.circleStroke)

        options.opacity?.let { opacity ->
            circleOpacity(opacity.toDouble())
        }

        sourceLayer(layerTypeOptions.name)
    }

    fun Layer.setVisible(visible: Boolean) {
        visibility(if (visible) Visibility.VISIBLE else Visibility.NONE)
    }

    /**
     * This prevents mapbox from using stretched heatmap tiles from zoom levels far too low while
     * loading the tiles.
     */
    private fun Layer.fixBlurryHeatmaps(layerTypeOptions: SuuntoLayerTypeOptions) {
        if (layerTypeOptions.layerType == SuuntoLayerType.HEATMAP) {
            (this as? RasterLayer)?.run {
                (style?.getSource(sourceId) as? RasterSource)?.run {
                    this.maxOverscaleFactorForParentTiles(3)
                    this.prefetchZoomDelta(1)
                }
            }
        }
    }

    private fun getLayerIdBelow(layerType: SuuntoLayerType, zIndex: Float, style: Style): String? {
        var layerIdBelow: String? = null
        if (layerType == SuuntoLayerType.TILE_OVERLAY) {
            layerIdBelow = getExistingRasterTileLayers()
                .lastOrNull { it.options.zIndex <= zIndex }
                ?.layers
                ?.last()
                ?.layerId
        }

        if (layerIdBelow == null) {
            for (i in layerType.ordinal downTo 0) {
                layerIdBelow = style.styleLayers.lastOrNull {
                    matchLayerId(it.id, SuuntoLayerType.entries[i]) &&
                        getZIndexByLayerId(it.id) <= zIndex
                }?.id
                if (layerIdBelow != null) break
            }
        }

        return layerIdBelow
    }

    private fun getLayerIdAbove(layerType: SuuntoLayerType, style: Style): String? {
        var layerIdAbove: String? = null
        if (layerType == SuuntoLayerType.TILE_OVERLAY) {
            layerIdAbove = getExistingRasterTileLayers()
                .firstOrNull()
                ?.layers
                ?.first()
                ?.layerId
        }

        if (layerIdAbove == null) {
            for (i in layerType.ordinal + 1 until SuuntoLayerType.entries.size) {
                layerIdAbove = style.styleLayers.firstOrNull {
                    matchLayerId(it.id, SuuntoLayerType.entries[i])
                }?.id
                if (layerIdAbove != null) break
            }
        }

        return layerIdAbove
    }

    private fun matchLayerId(layerId: String, layerType: SuuntoLayerType): Boolean =
        if (layerType == SuuntoLayerType.TEXT) {
            val layer = style?.getLayer(layerId)
            layer is SymbolLayer && layer.textFieldAsExpression != null
        } else {
            layerId.startsWith(layerType.layerIdPrefix) && layerId.endsWith(layerType.layerIdSuffix)
        }

    private fun getExistingRasterTileLayers(): List<MapboxTileOverlayDelegate> {
        if (!isValid) return emptyList()

        return mapDelegate.overlays
            .filter {
                it.layers.isNotEmpty() &&
                    it.options.layerTypeOptions.layerType == SuuntoLayerType.TILE_OVERLAY
            }
            .sortedBy { it.options.zIndex }
    }

    private fun getTileOverlayDelegateByLayerId(layerId: String): MapboxTileOverlayDelegate? {
        if (!isValid) return null

        return mapDelegate.overlays.find { tileOverlay ->
            tileOverlay.layers.find { layer ->
                layer.layerId == layerId
            } != null
        }
    }

    private fun getZIndexByLayerId(layerId: String): Float =
        getTileOverlayDelegateByLayerId(layerId)?.options?.zIndex ?: 0f
}
