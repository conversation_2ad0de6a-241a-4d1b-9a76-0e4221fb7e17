package com.stt.android.maps.mapbox.delegate.manager

import android.animation.ValueAnimator
import com.google.android.gms.maps.model.LatLng
import com.mapbox.common.Cancelable
import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.ScreenCoordinate
import com.mapbox.maps.StyleImageMissing
import com.mapbox.maps.StyleImageRemoveUnused
import com.mapbox.maps.extension.style.layers.properties.generated.IconAnchor
import com.mapbox.maps.extension.style.layers.properties.generated.IconPitchAlignment
import com.mapbox.maps.extension.style.layers.properties.generated.IconRotationAlignment
import com.mapbox.maps.plugin.annotation.Annotation
import com.mapbox.maps.plugin.annotation.AnnotationConfig
import com.mapbox.maps.plugin.annotation.annotations
import com.mapbox.maps.plugin.annotation.generated.OnPointAnnotationClickListener
import com.mapbox.maps.plugin.annotation.generated.OnPointAnnotationDragListener
import com.mapbox.maps.plugin.annotation.generated.PointAnnotation
import com.mapbox.maps.plugin.annotation.generated.PointAnnotationManager
import com.mapbox.maps.plugin.annotation.generated.PointAnnotationOptions
import com.mapbox.maps.plugin.annotation.generated.createPointAnnotationManager
import com.stt.android.maps.SuuntoBitmapDescriptor
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.mapbox.delegate.MapboxMarkerDelegate
import com.stt.android.maps.mapbox.toMapbox
import timber.log.Timber
import kotlin.math.cos
import kotlin.math.sin

class MarkerManager(
    map: MapboxMap,
    mapView: MapView
) : BaseAnnotationManager<
    SuuntoMarker,
    SuuntoMarkerOptions,
    PointAnnotation,
    PointAnnotationOptions,
    OnPointAnnotationDragListener,
    OnPointAnnotationClickListener,
    PointAnnotationManager
    >(
    MANAGER_NAME,
    map,
    mapView
) {
    private class ImageMapItem(
        val id: String
    ) {
        var refCount = 1
    }

    override val annotationFlavors = listOf(TYPE_FLAT, TYPE_NORMAL)
    private val imageMap = mutableMapOf<SuuntoBitmapDescriptor, ImageMapItem>()
    private var styleImageMissingCancelable : Cancelable? = map.subscribeStyleImageMissing(::addImageToStyle)
    private var styleImageRemoveUnusedCancelable : Cancelable? = map.subscribeStyleImageRemoveUnused(::removeImageFromStyle)

    override fun addAnnotation(options: SuuntoMarkerOptions): SuuntoMarker {
        val markerDelegate = MapboxMarkerDelegate(options, this)

        return SuuntoMarker(
            markerDelegate,
            options,
        ).also {
            markerDelegate.attach(it)
        }
    }

    override fun clear() {
        super.clear()
        imageMap.clear()
    }

    override fun onDestroy() {
        super.onDestroy()
        styleImageMissingCancelable?.cancel()
        styleImageMissingCancelable = null
        styleImageRemoveUnusedCancelable?.cancel()
        styleImageRemoveUnusedCancelable = null
    }

    fun registerDescriptor(descriptor: SuuntoBitmapDescriptor): String {
        return imageMap[descriptor]?.run {
            refCount++
            id
        } ?: run {
            descriptor.name.also {
                imageMap[descriptor] = ImageMapItem(it)
            }
        }
    }

    fun unregisterDescriptor(descriptor: SuuntoBitmapDescriptor) {
        imageMap[descriptor]?.run {
            if (--refCount <= 0) {
                imageMap.remove(descriptor)
            }
        }
    }

    override fun getAnnotationFlavor(options: SuuntoMarkerOptions): AnnotationFlavor {
        return if (options.flat) TYPE_FLAT else TYPE_NORMAL
    }

    override fun createMapboxAnnotationManager(
        flavor: AnnotationFlavor,
        layerId: String,
        belowLayerId: String
    ): PointAnnotationManager =
        mapView.annotations.createPointAnnotationManager(
            AnnotationConfig(
                layerId = layerId,
                belowLayerId = belowLayerId
            )
        ).apply {
            iconAllowOverlap = true
            iconIgnorePlacement = true
            iconOptional = false

            if (flavor == TYPE_FLAT) {
                iconRotationAlignment = IconRotationAlignment.MAP
                iconPitchAlignment = IconPitchAlignment.MAP
            } else {
                iconRotationAlignment = IconRotationAlignment.VIEWPORT
                iconPitchAlignment = IconPitchAlignment.VIEWPORT
            }
        }

    override fun optionsToMapbox(options: SuuntoMarkerOptions) = with(options) {
        PointAnnotationOptions().apply {
            latLng?.let { withPoint(it.toMapbox()) }
            iconDescriptor?.let { imageMap[it]?.id }
                ?.let(::withIconImage)
            withIconAnchor(anchorToMapbox(anchor))
            withIconOpacity(alpha.toDouble())
            withDraggable(draggable)
            withIconSize(iconScale.toDouble())
            withIconRotate(rotation.toDouble())
            withSymbolSortKey(zPriority.zIndex.toDouble())
        }
    }

    override fun attach(annotation: SuuntoMarker, options: SuuntoMarkerOptions) {
        options.iconDescriptor?.let { registerDescriptor(it) }
        super.attach(annotation, options)
    }

    override fun detach(annotation: SuuntoMarker, options: SuuntoMarkerOptions) {
        super.detach(annotation, options)
        options.iconDescriptor?.let { unregisterDescriptor(it) }
    }

    private fun addImageToStyle(styleImageMissing: StyleImageMissing) {
        val id = styleImageMissing.imageId
        map.style?.let { style ->
            imageMap.entries.firstOrNull { it.value.id == id }?.key?.run {
                asBitmap()?.let {
                    Timber.d("Adding image $id")
                    style.addImage(id, it)
                } ?: run {
                    Timber.w("Bitmap not found, unable to add image $id")
                }
            } ?: run {
                Timber.w("Descriptor not found, unable to add image $id")
            }
        } ?: run {
            Timber.w("Style not available, unable to add image $id")
        }
    }

    private fun removeImageFromStyle(styleImageRemoveUnused: StyleImageRemoveUnused) {
        val id = styleImageRemoveUnused.imageId
        map.style?.let { style ->
            Timber.d("Removing image $id")
            style.removeStyleImage(id)
        } ?: run {
            Timber.w("Style not available, unable to remove image $id")
        }
    }

    private fun anchorToMapbox(anchor: Pair<Float, Float>): IconAnchor {
        val (x, y) = anchor
        return when {
            x == 0f && y == 0f -> IconAnchor.TOP_LEFT
            x == 0f && y == 0.5f -> IconAnchor.LEFT
            x == 0f && y == 1f -> IconAnchor.BOTTOM_LEFT
            x == 0.5f && y == 0f -> IconAnchor.TOP
            x == 0.5f && y == 0.5f -> IconAnchor.CENTER
            x == 0.5f && y == 1f -> IconAnchor.BOTTOM
            x == 1f && y == 0f -> IconAnchor.TOP_RIGHT
            x == 1f && y == 0.5f -> IconAnchor.RIGHT
            x == 1f && y == 1f -> IconAnchor.BOTTOM_RIGHT
            else -> IconAnchor.CENTER
        }
    }

    override fun createDragListener(flavor: AnnotationFlavor): OnPointAnnotationDragListener =
        DelegatingPointAnnotationDragListener(flavor)

    override fun createClickListener(flavor: AnnotationFlavor): OnPointAnnotationClickListener =
        DelegatingPointAnnotationClickListener(flavor)

    private inner class DelegatingPointAnnotationDragListener(flavor: AnnotationFlavor) :
        OnPointAnnotationDragListener, DelegatingAnnotationDragListener(flavor) {

        private var offsetAnimator: ValueAnimator? = null

        override fun onAnnotationDrag(annotation: Annotation<*>) {
            applyDragOffset(annotation)
            super.onAnnotationDrag(annotation)
        }

        override fun onAnnotationDragFinished(annotation: Annotation<*>) {
            removeDragOffset(annotation)
            super.onAnnotationDragFinished(annotation)
        }

        private fun applyDragOffset(annotation: Annotation<*>) {
            if (annotation is PointAnnotation && offsetAnimator == null) {
                val angle = annotation.iconRotate ?: 0.0
                offsetAnimator = ValueAnimator
                    .ofFloat(0f, VERTICAL_ANNOTATION_DRAG_OFFSET_IN_DP).apply {
                        duration = 200
                        start()
                        addUpdateListener { animation ->
                            val x = 0.0
                            val y = (animation.animatedValue as Float).toDouble()
                            // Take possible rotation into account
                            val xOffset = rotateX(x, y, angle)
                            val yOffset = rotateY(x, y, angle)
                            annotation.iconOffset = listOf(xOffset, yOffset)
                        }
                    }
            }
        }

        private fun removeDragOffset(annotation: Annotation<*>) {
            offsetAnimator?.cancel()
            offsetAnimator = null
            if (annotation is PointAnnotation) {
                val xIconOffset = annotation.iconOffset?.firstOrNull() ?: 0.0
                val yIconOffset = annotation.iconOffset?.lastOrNull() ?: 0.0
                // Take possible rotation into account
                val angle = -(annotation.iconRotate ?: 0.0) // Counterclockwise. Negate the angle.
                val xOffset = rotateX(xIconOffset, yIconOffset, angle)
                val yOffset = rotateY(xIconOffset, yIconOffset, angle)
                // Convert dp to pixels
                val iconSize = annotation.iconSize ?: 1.0
                val density = mapView.context.resources.displayMetrics.density
                val xOffsetPx = density * xOffset * iconSize
                val yOffsetPx = density * yOffset * iconSize
                val position = with(map.pixelForCoordinate(annotation.point)) {
                    val point =
                        map.coordinateForPixel(ScreenCoordinate(x + xOffsetPx, y + yOffsetPx))
                    annotation.iconOffset = listOf(0.0, 0.0)
                    annotation.point = point
                    LatLng(point.latitude(), point.longitude())
                }
                getAnnotation(annotation.id, flavor)?.setPosition(position)
            }
        }

        private fun rotateX(x: Double, y: Double, angle: Double) =
            x * cos(Math.toRadians(angle)) + y * sin(Math.toRadians(angle))

        private fun rotateY(x: Double, y: Double, angle: Double) =
            -x * sin(Math.toRadians(angle)) + y * cos(Math.toRadians(angle))
    }

    private inner class DelegatingPointAnnotationClickListener(flavor: AnnotationFlavor) :
        OnPointAnnotationClickListener, DelegatingAnnotationClickListener(flavor)

    companion object {
        const val MANAGER_NAME = "marker"

        // Marker that rotates with map.
        const val TYPE_FLAT: AnnotationFlavor = 1

        // Normal marker, drawn above flat markers.
        const val TYPE_NORMAL: AnnotationFlavor = 2

        // Vertical annotation offset in dp during drag to allow the user to see the dragged
        // marker.
        private const val VERTICAL_ANNOTATION_DRAG_OFFSET_IN_DP = -40.0f
    }
}
