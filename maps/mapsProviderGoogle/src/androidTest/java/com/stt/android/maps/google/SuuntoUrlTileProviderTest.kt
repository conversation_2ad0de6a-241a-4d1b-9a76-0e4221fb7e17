package com.stt.android.maps.google

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.common.truth.Truth.assertThat
import com.stt.android.maps.SuuntoTileSource
import org.junit.Test
import java.net.URL

class SuuntoUrlTileProviderTest {

    @Test
    fun shouldRotateEndpoints() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource(
                listOf(
                    "http://tiles1.foo.com/{z}/{x}/{y}.png",
                    "http://tiles2.foo.com/{z}/{x}/{y}.png"
                )
            )
        )

        assertThat(tileProvider.getTileUrl(1, 2, 3))
            .isEqualTo(URL("http://tiles1.foo.com/3/1/2.png"))
        assertThat(tileProvider.getTileUrl(1, 2, 3))
            .isEqualTo(URL("http://tiles2.foo.com/3/1/2.png"))
        assertThat(tileProvider.getTileUrl(1, 2, 3))
            .isEqualTo(URL("http://tiles1.foo.com/3/1/2.png"))
    }

    @Test
    fun shouldInvertYForTms() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource(
                "http://tiles.foo.com/{z}/{x}/{y}.png",
                scheme = SuuntoTileSource.Scheme.TMS
            )
        )

        // yMax = 2^zoom - 1 = 2^3 - 1 = 8 - 1 = 7
        // yInverted = yMax - y = 7 - 2 = 5
        assertThat(tileProvider.getTileUrl(1, 2, 3))
            .isEqualTo(URL("http://tiles.foo.com/3/1/5.png"))
    }

    @Test
    fun shouldVerifyZoomRange() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource("http://tiles.foo.com/{z}/{x}/{y}.png").apply {
                minZoom = 5
                maxZoom = 14
            }
        )

        assertThat(tileProvider.getTileUrl(1, 2, 4)).isNull()
        assertThat(tileProvider.getTileUrl(1, 2, 5)).isNotNull()
        assertThat(tileProvider.getTileUrl(1, 2, 14)).isNotNull()
        assertThat(tileProvider.getTileUrl(1, 2, 15)).isNull()
    }

    @Test
    fun shouldHandleTilesCompletelyInsideBoundingBox() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource("http://tiles.foo.com/{z}/{x}/{y}.png").apply {
                bounds = LatLngBounds(LatLng(61.2, 23.2), LatLng(61.7, 24.0))
            }
        )

        assertThat(tileProvider.getTileUrl(289, 144, 9)).isNotNull()
    }

    @Test
    fun shouldHandleTilesPartiallyInsideBoundingBox() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource("http://tiles.foo.com/{z}/{x}/{y}.png").apply {
                bounds = LatLngBounds(LatLng(61.2, 23.2), LatLng(61.7, 24.0))
            }
        )

        assertThat(tileProvider.getTileUrl(288, 144, 9)).isNotNull()
        assertThat(tileProvider.getTileUrl(290, 144, 9)).isNotNull()
        assertThat(tileProvider.getTileUrl(289, 143, 9)).isNotNull()
        assertThat(tileProvider.getTileUrl(289, 145, 9)).isNotNull()
    }

    @Test
    fun shouldIgnoreTilesCompletelyOutsideBoundingBox() {
        val tileProvider = SuuntoUrlTileProvider(
            SuuntoTileSource("http://tiles.foo.com/{z}/{x}/{y}.png").apply {
                bounds = LatLngBounds(LatLng(61.2, 23.2), LatLng(61.7, 24.0))
            }
        )

        // Tile completely outside bounds
        assertThat(tileProvider.getTileUrl(287, 144, 9)).isNull()
        assertThat(tileProvider.getTileUrl(291, 144, 9)).isNull()
        assertThat(tileProvider.getTileUrl(289, 142, 9)).isNull()
        assertThat(tileProvider.getTileUrl(289, 146, 9)).isNull()
    }
}
