package com.stt.android.data.marketingconsent

import com.stt.android.remote.marketingconsent.MarketingConsentRemoteApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any

@RunWith(MockitoJUnitRunner::class)
class MarketingConsentRemoteDataSourceTest {

    @Mock
    private lateinit var marketingConsentRemoteApi: MarketingConsentRemoteApi

    private lateinit var marketingConsentRemoteDataSource: MarketingConsentRemoteDataSource

    @Before
    fun setup() {
        marketingConsentRemoteDataSource = MarketingConsentRemoteDataSource(
            marketingConsentRemoteApi,
            "suuntoapp"
        )
    }

    @Test
    fun `should access marketingConsent remote api when accepting marketingConsent`() =
        runTest {
            // Prepare
            `when`(marketingConsentRemoteApi.acceptMarketingConsent(any()))
                .thenReturn(Unit)

            // Verify
            marketingConsentRemoteDataSource.acceptMarketingConsent(anyBoolean(), "")

            verify(marketingConsentRemoteApi).acceptMarketingConsent(any())
        }
}
