package com.stt.android.data.recovery

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.VisibleForTesting
import androidx.core.content.edit
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.Local
import com.stt.android.data.timeline.TimelineStreamParser
import com.stt.android.data.toEpochMilli
import com.stt.android.data.utils.asWorkReportData
import com.stt.android.domain.activitydata.dailyvalues.asStressState
import com.stt.android.domain.activitydata.dailyvalues.intValue
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.exceptions.AggregateException
import com.stt.android.exceptions.remote.ServerError
import com.stt.android.remote.askotimeline.AskoTimelineEntry
import com.stt.android.remote.askotimeline.AskoTimelineRecovery
import com.stt.android.remote.askotimeline.AskoTimelineRestApi
import com.stt.android.worker.ifNotAlreadyScheduled
import okhttp3.ResponseBody
import timber.log.Timber
import java.time.Duration
import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class RecoveryRemoteSyncJob(
    @Local private val recoveryDataLocalDataSource: RecoveryDataLocalDataSource,
    private val askoTimelineRestApi: AskoTimelineRestApi,
    private val pageSize: Int,
    private val timelineStreamParser: TimelineStreamParser,
    private val sharedPreferences: SharedPreferences,
    context: Context,
    params: WorkerParameters,
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result = runSuspendCatching {
        fetchFromRemote()
        pushToRemote()
        Result.success()
    }.getOrElse { e ->
        Timber.w(e, "Error syncing recovery data with backend")
        when (e) {
            is ServerError.ServiceUnavailable -> Result.retry()
            else -> Result.failure(e.asWorkReportData())
        }
    }

    /**
     * This sync is meant to be running once only after login to fetch all recovery data from backend.
     * Once it is cached locally, there is no need to fetch it again (the new values will come from
     * the watches only).
     * It's possible this Job runs more than once in case it has not fetched all of the data the first
     * time.
     */
    private suspend fun fetchFromRemote() {
        val lastFetchMillis = sharedPreferences.getLong(RECOVERY_LAST_FETCH_TIMESTAMP, 0L)
        if (System.currentTimeMillis() - lastFetchMillis <= Duration.ofMinutes(10).toMillis()) {
            Timber.d("Skipping fetch, last one was too recent")
            return
        }
        Timber.d("Fetching from date: ${getLatestSyncedTimestamp()}")
        timelineStreamParser.parseTimelineStream(
            timelineDataStream = ::fetchRecoveryFile,
            mapToLocal = ::toRecoveryData,
            saveTimelineToLocal = ::saveRecoveryToLocal,
            pageSize = pageSize,
            tag = TAG
        )
        sharedPreferences.edit {
            putLong(RECOVERY_LAST_FETCH_TIMESTAMP, System.currentTimeMillis())
        }
    }

    private suspend fun pushToRemote() {
        val recoveryToSync = recoveryDataLocalDataSource.fetchRecoveryDataForBackendSync()
        if (recoveryToSync.isEmpty()) {
            Timber.d("Nothing to push to remote")
            return
        }
        // let's sync one page at a time to reduce chances of timeouts
        val recoveryListChunks = recoveryToSync.chunked(pageSize)
        val exceptions = mutableListOf<Throwable>()
        for (recoveryList in recoveryListChunks) {
            runSuspendCatching {
                askoTimelineRestApi.saveRecovery(recoveryList.map(::toRemoteRecovery))
                // this will mark the recovery data as synced to backend
                recoveryDataLocalDataSource.saveRecoveryData(
                    recoveryDataList = recoveryList,
                    replaceConflicts = true,
                    requireBackendSync = false
                )
            }.onFailure { e ->
                exceptions.add(e)
            }
        }
        if (exceptions.isNotEmpty()) {
            throw AggregateException(exceptions)
        }
    }

    private suspend fun fetchRecoveryFile(): ResponseBody? =
        askoTimelineRestApi.streamRecoveryData(getLatestSyncedTimestamp()?.toEpochMilli()).body()

    private suspend fun getLatestSyncedTimestamp(): ZonedDateTime? =
        recoveryDataLocalDataSource.getLatestSyncedTimestamp()

    private suspend fun saveRecoveryToLocal(list: List<RecoveryData>) {
        recoveryDataLocalDataSource.saveRecoveryData(
            recoveryDataList = list,
            replaceConflicts = true,
            requireBackendSync = false
        )
    }

    @VisibleForTesting
    internal fun toRecoveryData(entry: AskoTimelineEntry<AskoTimelineRecovery>): RecoveryData =
        RecoveryData(
            serial = "", // serial is not saved in backend
            timestamp = entry.timestamp.toEpochMilli(),
            balance = entry.entryData.balance,
            stressState = entry.entryData.stressState.asStressState(),
            timeISO8601 = entry.timestamp
        )

    internal fun toRemoteRecovery(recovery: RecoveryData): AskoTimelineEntry<AskoTimelineRecovery> =
        AskoTimelineEntry(
            timestamp = recovery.timeISO8601,
            entryData = AskoTimelineRecovery(
                balance = recovery.balance,
                stressState = recovery.stressState.intValue
            )
        )

    class Factory
    @Inject constructor(
        @Local private val recoveryDataLocalDataSource: RecoveryDataLocalDataSource,
        private val askoTimelineRestApi: AskoTimelineRestApi,
        private val timelineStreamParser: TimelineStreamParser,
        private val sharedPreferences: SharedPreferences,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return RecoveryRemoteSyncJob(
                recoveryDataLocalDataSource,
                askoTimelineRestApi,
                PAGE_SIZE,
                timelineStreamParser,
                sharedPreferences,
                context,
                params,
            )
        }

        companion object {
            private const val PAGE_SIZE = 10_000
        }
    }

    companion object {
        private const val TAG = "RecoveryRemoteSyncJob"

        internal const val RECOVERY_LAST_FETCH_TIMESTAMP = "RECOVERY_LAST_FETCH_TIMESTAMP"

        @JvmStatic
        fun enqueue(workManager: WorkManager) {
            workManager.ifNotAlreadyScheduled(TAG) {
                Timber.d("Enqueuing RecoveryRemoteSyncJob")
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequestBuilder<RecoveryRemoteSyncJob>()
                        .setConstraints(
                            Constraints.Builder()
                                .setRequiredNetworkType(NetworkType.CONNECTED)
                                .build()
                        )
                        .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
                        .build()
                )
            }
        }

        fun cancelAndClearFlags(workManager: WorkManager, sharedPreferences: SharedPreferences) {
            workManager.cancelUniqueWork(TAG)
            sharedPreferences.edit {
                remove(RECOVERY_LAST_FETCH_TIMESTAMP)
            }
        }
    }
}
