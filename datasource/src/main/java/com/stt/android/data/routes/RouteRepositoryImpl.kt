package com.stt.android.data.routes

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.work.WorkManager
import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.RouteRepository
import com.stt.android.domain.routes.RouteSyncWithWatchJobScheduler
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class RouteRepositoryModule {

    @Binds
    abstract fun bindRouteRepositoryImpl(
        repositoryImpl: RouteRepositoryImpl
    ): RouteRepository

    @Binds
    abstract fun bindRouteSyncWithWatchJobSchedulerImpl(
        routeSyncWithWatchJobSchedulerImpl: RouteSyncWithWatchJobSchedulerImpl
    ): RouteSyncWithWatchJobScheduler
}

class RouteRepositoryImpl
@Inject constructor(
    @Local private val routeLocalDataSource: RouteDataSource,
    @Remote private val routeRemoteDataSource: RouteDataSource,
    private val sharedPreferences: SharedPreferences,
    private val workManager: WorkManager
) : RouteRepository {
    override suspend fun getRouteCount(): Int =
        routeLocalDataSource.getRouteCount()

    override suspend fun getWatchEnabledRouteCount(): Int =
        routeLocalDataSource.getWatchEnabledRouteCount()

    /**
     * Fetch [Route] by ID if exists, or null otherwise.
     */
    override suspend fun fetchRouteById(routeId: String): Route? =
        routeLocalDataSource.fetchRouteById(routeId)

    /**
     * Fetch routes from local database. Trigger remote sync if requested.
     * @param syncWithRemote true if remote route sync should be scheduled
     * @param includeSegments true to include route segments and points in the result
     */
    override fun fetchRoutes(
        syncWithRemote: Boolean,
        includeSegments: Boolean
    ): Flow<List<Route>> {
        if (syncWithRemote) {
            RouteRemoteSyncJob.schedule(
                workManager = workManager,
                onlyWatchEnabledInPartialFetch = false
            )
        }

        return routeLocalDataSource.fetchRoutes(includeSegments = includeSegments)
    }

    /**
     * Share route with it's backend id. Fetch the share url always from remote.
     * @param key the route's key on the backend
     */
    override suspend fun shareRouteByKey(key: String): String =
        routeRemoteDataSource.shareRoute(key)

    override suspend fun updateAverageSpeed(routeId: String, metersPerSecond: Double) {
        routeLocalDataSource.updateAverageSpeed(routeId, metersPerSecond)
    }

    override suspend fun updateOrCreate(route: Route) {
        routeLocalDataSource.updateOrCreate(route)
        if (!route.isInProgress) {
            routeRemoteDataSource.updateOrCreate(route)
        }
    }

    override suspend fun delete(route: Route) {
        routeLocalDataSource.delete(route)
        if (!route.isInProgress) {
            routeRemoteDataSource.delete(route)
        }
    }

    override suspend fun deleteRoutesInProgress() {
        routeLocalDataSource.deleteRoutesInProgress()
    }

    override fun getDefaultActivities(defaultList: List<Int>): List<Int> {
        return sharedPreferences.getString(DEFAULT_ACTIVITIES_KEY, null)
            ?.split(",")?.mapNotNull { it.toIntOrNull() } ?: defaultList
    }

    override fun saveDefaultActivities(activities: List<Int>) {
        sharedPreferences.edit {
            putString(DEFAULT_ACTIVITIES_KEY, activities.joinToString(","))
        }
    }

    companion object {
        private const val DEFAULT_ACTIVITIES_KEY =
            "com.stt.android.data.routes.RouteRepository.DEFAULT_ACTIVITIES"
    }
}
