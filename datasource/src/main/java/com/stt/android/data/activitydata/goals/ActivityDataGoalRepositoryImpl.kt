package com.stt.android.data.activitydata.goals

import com.stt.android.TestOpen
import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.joules
import com.suunto.algorithms.data.Energy.Companion.kcal
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@Module
@InstallIn(SingletonComponent::class)
abstract class ActivityDataGoalModule {

    @Binds
    abstract fun bindActivityDataDailyRepository(
        repositoryImpl: ActivityDataGoalRepositoryImpl
    ): ActivityDataGoalRepository
}

/**
 * Repository definition to fetch and set activity data goals
 */
@TestOpen
class ActivityDataGoalRepositoryImpl
@Inject constructor(
    @Local private val activityDataGoalLocalDataSource: ActivityDataGoalDataSource,
    @Remote private val activityDataGoalRemoteDataSource: ActivityDataGoalDataSource
) : ActivityDataGoalRepository {

    override fun fetchStepsGoal(): Flow<Int> = merge(
        flow { emit(activityDataGoalLocalDataSource.fetchStepsGoal()) },
        flow {
            emit(activityDataGoalRemoteDataSource.fetchStepsGoal())
        }.onEach { stepsGoal ->
            activityDataGoalLocalDataSource.setStepsGoal(stepsGoal)
        }.catch { e ->
            Timber.w(e, "Error while fetching steps goal from watch")
            emit(activityDataGoalLocalDataSource.fetchStepsGoal())
        }
    ).map { goal ->
        goal.takeIf { it >= 0 } ?: ActivityDataType.Steps().goal
    }.distinctUntilChanged()

    override fun fetchEnergyGoal(): Flow<Energy> = merge(
        flow { emit(activityDataGoalLocalDataSource.fetchEnergyGoal().joules) },
        flow {
            emit(activityDataGoalRemoteDataSource.fetchEnergyGoal().joules)
        }.onEach { energyGoal ->
            activityDataGoalLocalDataSource.setEnergyGoal(energyGoal.inJoules.roundToInt())
        }.catch { e ->
            Timber.w(e, "Error while fetching energy goal from watch")
            emit(activityDataGoalLocalDataSource.fetchEnergyGoal().joules)
        }
    ).map { goal ->
        goal.takeIf { it.inCal >= 0.0 } ?: ActivityDataType.Energy().goal.kcal
    }.distinctUntilChanged()

    override fun fetchSleepGoal(): Flow<Duration> = merge(
        flow { emit(activityDataGoalLocalDataSource.fetchSleepGoal().seconds) },
        flow {
            emit(activityDataGoalRemoteDataSource.fetchSleepGoal().seconds)
        }.onEach { sleepGoal ->
            activityDataGoalLocalDataSource.setSleepGoal(sleepGoal.inWholeSeconds.toInt())
        }.catch { e ->
            Timber.w(e, "Error while fetching sleep goal from watch")
            emit(activityDataGoalLocalDataSource.fetchSleepGoal().seconds)
        }
    ).map { goal ->
        goal.takeIf { it >= 0.seconds } ?: ActivityDataType.SleepDuration().goal.seconds
    }.distinctUntilChanged()

    override fun fetchBedtimeStart(): Flow<Int> = merge(
        flow { emit(activityDataGoalLocalDataSource.fetchBedtimeStart()) },
        flow {
            emit(activityDataGoalRemoteDataSource.fetchBedtimeStart())
        }.onEach { bedtimeStart ->
            activityDataGoalLocalDataSource.setBedtimeStart(bedtimeStart)
        }.catch { e ->
            Timber.w(e, "Error while fetching start bedtime from watch")
            emit(activityDataGoalLocalDataSource.fetchBedtimeStart())
        }
    ).distinctUntilChanged()

    override fun fetchBedtimeEnd(): Flow<Int> = merge(
        flow { emit(activityDataGoalLocalDataSource.fetchBedtimeEnd()) },
        flow {
            emit(activityDataGoalRemoteDataSource.fetchBedtimeEnd())
        }.onEach { bedtimeEnd ->
            activityDataGoalLocalDataSource.setBedtimeEnd(bedtimeEnd)
        }.catch { e ->
            Timber.w(e, "Error while fetching end bedtime from watch")
            emit(activityDataGoalLocalDataSource.fetchBedtimeEnd())
        }
    ).distinctUntilChanged()

    override suspend fun setStepsGoal(goal: Int) {
        activityDataGoalRemoteDataSource.setStepsGoal(goal)
        activityDataGoalLocalDataSource.setStepsGoal(goal)
    }

    override suspend fun setEnergyGoal(goal: Energy) {
        val joules = goal.inJoules.roundToInt()
        activityDataGoalRemoteDataSource.setEnergyGoal(joules)
        activityDataGoalLocalDataSource.setEnergyGoal(joules)
    }

    override suspend fun setSleepGoal(goal: Duration) {
        activityDataGoalRemoteDataSource.setSleepGoal(goal.inWholeSeconds.toInt())
        activityDataGoalLocalDataSource.setSleepGoal(goal.inWholeSeconds.toInt())
    }

    override suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int) {
        activityDataGoalRemoteDataSource.setBedtimes(bedtimeStart, bedtimeEnd)
        activityDataGoalLocalDataSource.setBedtimes(bedtimeStart, bedtimeEnd)
    }

    override suspend fun fetchLocalStepsGoal(): Int {
        return activityDataGoalLocalDataSource.fetchStepsGoal()
    }

    override suspend fun fetchLocalEnergyGoal(): Energy {
        return activityDataGoalLocalDataSource.fetchEnergyGoal().joules
    }

    override suspend fun fetchLocalSleepGoal(): Duration {
        return activityDataGoalLocalDataSource.fetchSleepGoal().seconds
    }
}
