package com.stt.android.data.trenddata

import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.data.source.local.trenddata.LocalWeChatTrendData
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.WeChatTrendData
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * A mapper class for mapping local data [LocalTrendData] entities to domain [TrendData] entity
 */
class WeChatTrendDataLocalMapper
@Inject constructor() {

    fun toDomainEntity(): (LocalWeChatTrendData) -> WeChatTrendData {
        return { dbTrendData ->
            val timestampMillis = TimeUnit.SECONDS.toMillis(dbTrendData.timestampSeconds)
            WeChatTrendData(
                serial = dbTrendData.serial,
                timestamp = timestampMillis,
                energy = dbTrendData.energy,
                steps = dbTrendData.steps,
                hr = dbTrendData.heartrate,
                hrMin = dbTrendData.hrMin,
                hrMax = dbTrendData.hrMax,
                timeISO8601 = dbTrendData.timeISO8601,
                spo2 = dbTrendData.spo2,
                altitude = dbTrendData.altitude,
                variantName = dbTrendData.deviceName
            )
        }
    }

    fun toDomainEntity(localWeChatTrendData: LocalWeChatTrendData): WeChatTrendData {
        val timestampMillis = TimeUnit.SECONDS.toMillis(localWeChatTrendData.timestampSeconds)
        return WeChatTrendData(
            serial = localWeChatTrendData.serial,
            timestamp = timestampMillis,
            energy = localWeChatTrendData.energy,
            steps = localWeChatTrendData.steps,
            hr = localWeChatTrendData.heartrate,
            hrMin = localWeChatTrendData.hrMin,
            hrMax = localWeChatTrendData.hrMax,
            timeISO8601 = localWeChatTrendData.timeISO8601,
            spo2 = localWeChatTrendData.spo2,
            altitude = localWeChatTrendData.altitude,
            variantName = localWeChatTrendData.deviceName
        )
    }

    fun toDataEntity(syncedStatus: Int): (WeChatTrendData) -> LocalWeChatTrendData {
        return { trendData ->
            val timestampSeconds = TimeUnit.MILLISECONDS.toSeconds(trendData.timestamp)
            LocalWeChatTrendData(
                serial = trendData.serial,
                timestampSeconds = timestampSeconds,
                energy = trendData.energy,
                steps = trendData.steps,
                heartrate = trendData.hr,
                syncedStatus = syncedStatus,
                timeISO8601 = trendData.timeISO8601,
                hrMin = trendData.hrMin,
                hrMax = trendData.hrMax,
                spo2 = trendData.spo2,
                altitude = trendData.altitude,
                deviceName = trendData.variantName
            )
        }
    }

    fun toNewLocalTrendData(trendData: WeChatTrendData, syncedStatus: Int): LocalWeChatTrendData {
        val timestampSeconds = TimeUnit.MILLISECONDS.toSeconds(trendData.timestamp)
        return LocalWeChatTrendData(
            serial = trendData.serial,
            timestampSeconds = timestampSeconds,
            energy = trendData.energy,
            steps = trendData.steps,
            heartrate = trendData.hr,
            syncedStatus = syncedStatus,
            timeISO8601 = trendData.timeISO8601,
            hrMin = trendData.hrMin,
            hrMax = trendData.hrMax,
            spo2 = trendData.spo2,
            altitude = trendData.altitude,
            deviceName = trendData.variantName
        )
    }

    fun toDataEntityList(syncedStatus: Int): (List<WeChatTrendData>) -> List<LocalWeChatTrendData> =
        { list ->
            list.map { toDataEntity(syncedStatus)(it) }
        }

    fun toDomainEntityList(): (List<LocalWeChatTrendData>) -> List<WeChatTrendData> = { list ->
        list.map { toDomainEntity()(it) }
    }
}
