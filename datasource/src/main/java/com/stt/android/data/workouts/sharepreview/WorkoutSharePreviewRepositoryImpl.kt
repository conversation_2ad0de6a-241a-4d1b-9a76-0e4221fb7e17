package com.stt.android.data.workouts.sharepreview

import com.stt.android.domain.workouts.sharepreview.WorkoutSharePreviewMetadata
import com.stt.android.domain.workouts.sharepreview.WorkoutSharePreviewRepository
import com.stt.android.remote.BrandForAsko
import com.stt.android.remote.workouts.sharepreview.WorkoutSharePreviewRestApi
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class WorkoutSharePreviewRepositoryModule {

    @Binds
    abstract fun bindWatchInfoRepositoryImpl(
        repositoryImpl: WorkoutSharePreviewRepositoryImpl
    ): WorkoutSharePreviewRepository
}

class WorkoutSharePreviewRepositoryImpl
@Inject constructor(
    private val workoutSharePreviewRestApi: WorkoutSharePreviewRestApi,
    @BrandForAsko private val brand: String
) : WorkoutSharePreviewRepository {
    override suspend fun fetchWorkoutSharePreviewMetadata(
        username: String,
        workoutKey: String
    ): WorkoutSharePreviewMetadata =
        workoutSharePreviewRestApi.fetchWorkoutSharePreviewMetadata(
            username,
            workoutKey,
            brand
        ).run {
            WorkoutSharePreviewMetadata(title, description, imageUrl)
        }
}
