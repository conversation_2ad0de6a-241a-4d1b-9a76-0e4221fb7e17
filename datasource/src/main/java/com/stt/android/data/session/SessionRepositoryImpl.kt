package com.stt.android.data.session

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.domain.session.GdprExportStatus
import com.stt.android.domain.session.SessionRepository
import com.stt.android.domain.session.SessionStatus
import com.stt.android.remote.session.SessionStatusRemoteApi
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class SessionRepositoryModule {

    @Binds
    abstract fun bindSessionRepositoryImpl(
        repositoryImpl: SessionRepositoryImpl
    ): SessionRepository
}

/**
 * Possible future container of most of the logic in SessionController
 */
class SessionRepositoryImpl
@Inject constructor(
    private val sessionStatusRemoteApi: SessionStatusRemoteApi,
    private val sessionStatusRemoteMapper: SessionStatusRemoteMapper,
    private val gdprExportStatusRemoteMapper: GdprExportStatusRemoteMapper,
    @Named("SessionStatusPrefKey") private val sessionStatusPrefKey: String,
    @Named("SessionStatusWarningPrefKey") private val sessionStatusWarningPrefKey: String,
    private val sharedPreferences: SharedPreferences
) : SessionRepository {

    override var lastSessionStatus: SessionStatus
        get() {
            // valid status by default
            val statusName = sharedPreferences.getString(
                sessionStatusPrefKey,
                SessionStatus.VALID.statusName
            )!!
            return SessionStatus.fromStatusName(statusName)
        }
        set(value) {
            sharedPreferences.edit {
                putString(sessionStatusPrefKey, value.statusName)
            }
        }

    override var lastSessionStatusWarning: Boolean
        get() = sharedPreferences.getBoolean(sessionStatusWarningPrefKey, false)
        set(value) {
            sharedPreferences.edit {
                putBoolean(sessionStatusWarningPrefKey, value)
            }
        }

    override suspend fun fetchSessionStatus(): SessionStatus =
        sessionStatusRemoteMapper.toDomainEntity()(
            sessionStatusRemoteApi.fetchSessionStatus()
        )

    override suspend fun sendPasswordResetEmail() {
        sessionStatusRemoteApi.sendPasswordResetEmail()
    }

    override suspend fun sendDeleteAccountRequest() {
        sessionStatusRemoteApi.sendDeleteAccountRequest()
    }

    override suspend fun sendGdprExportRequest(): GdprExportStatus =
        gdprExportStatusRemoteMapper.toDomainEntity()(
            sessionStatusRemoteApi.sendGdprExportRequest()
        )
}
