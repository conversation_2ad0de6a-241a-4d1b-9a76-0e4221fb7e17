package com.stt.android.data.marketing

import com.stt.android.data.EntityMapper
import com.stt.android.domain.marketing.MarketingBannerInfo
import com.stt.android.domain.marketing.MarketingBannerInfo.LinkType
import com.stt.android.domain.marketing.MarketingBannerInfo.Location
import com.stt.android.domain.marketing.MarketingBannerInfo.PreviewUrl
import com.stt.android.remote.marketing.RemoteMarketingBannerInfo
import com.stt.android.remote.marketing.RemoteLinkType
import com.stt.android.remote.marketing.RemoteMarketingBannerInfo.RemoteLocation
import com.stt.android.remote.marketing.RemoteMarketingBannerInfo.RemotePreviewUrl
import javax.inject.Inject

class MarketingBannerRemoteMapper @Inject constructor(
) : EntityMapper<RemoteMarketingBannerInfo, MarketingBannerInfo> {
    override fun toDomainEntity(): (RemoteMarketingBannerInfo) -> MarketingBannerInfo = {
        MarketingBannerInfo(
            bannerId = it.bannerId,
            displayOrder = it.displayOrder,
            name = it.name,
            title = it.title,
            previewUrl = it.previewUrl.toDomain(),
            location = it.location?.toDomain(),
            startTime = it.startTime,
            endTime = it.endTime,
            linkType = it.linkType?.toDomain(),
            link = it.link,
        )
    }

    override fun toDataEntity(): (MarketingBannerInfo) -> RemoteMarketingBannerInfo = {
        RemoteMarketingBannerInfo(
            bannerId = it.bannerId,
            displayOrder = it.displayOrder,
            name = it.name,
            title = it.title,
            previewUrl = it.previewUrl.toRemote(),
            location = it.location?.toRemote(),
            startTime = it.startTime,
            endTime = it.endTime,
            linkType = it.linkType?.toRemote(),
            link = it.link,
        )
    }
}

private fun RemotePreviewUrl.toDomain() = PreviewUrl(
    phone = phone,
)

private fun PreviewUrl.toRemote() = RemotePreviewUrl(
    phone = phone,
)

private fun RemoteLocation.toDomain() = when (this) {
    RemoteLocation.HOME_VIEW -> Location.HOME_VIEW
    RemoteLocation.SUUNTO_PLUS_STORE_VIEW -> Location.SUUNTO_PLUS_STORE_VIEW
}

private fun Location.toRemote() = when (this) {
    Location.HOME_VIEW -> RemoteLocation.HOME_VIEW
    Location.SUUNTO_PLUS_STORE_VIEW -> RemoteLocation.SUUNTO_PLUS_STORE_VIEW
}

private fun RemoteLinkType.toDomain() = when (this) {
    RemoteLinkType.EXTERNAL_URL -> LinkType.EXTERNAL_URL
    RemoteLinkType.DEEPLINK -> LinkType.DEEPLINK
    RemoteLinkType.H5 -> LinkType.H5
}

private fun LinkType.toRemote() = when (this) {
    LinkType.EXTERNAL_URL -> RemoteLinkType.EXTERNAL_URL
    LinkType.DEEPLINK -> RemoteLinkType.DEEPLINK
    LinkType.H5 -> RemoteLinkType.H5
}
