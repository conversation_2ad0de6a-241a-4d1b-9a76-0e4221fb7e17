package com.stt.android.data.sportmodes

interface SportModeComponent {
    fun initialize(variant: String, version: String, locale: String)

    fun getSportModes(): String

    fun getVersion(): String

    fun getMinNumberOfDisplays(watchSportModeSettings: String): Double

    fun getMaxNumberOfDisplays(watchSportModeSettings: String): Double

    fun getCurrentSportModes(watchSportModesJson: String): String

    fun getCurrentDisplays(watchSportModeDisplays: String, watchSportModeSettings: String): String

    fun getCurrentFields(watchSportModeDisplays: String, displayId: Int): String

    fun getFields(activityId: Int, displayId: String, fieldIndex: Int): String

    fun getDisplays(watchSportModeDisplays: String, activityId: Int, displayIndex: Int): String

    fun getSportModeTemplate(activityId: Int, watchSportModesJson: String): String

    fun addDisplay(watchSportModeDisplays: String, watchSportModeSettings: String, displayIndex: Int, displayId: String): String

    fun changeDisplay(watchSportModeDisplays: String, watchSportModeSettings: String, displayIndex: Int, displayId: String): String

    fun changeField(watchSportModeDisplays: String, displayIndex: Int, fieldIndex: Int, fieldId: String): String

    fun deleteDisplay(watchSportModeDisplays: String, watchSportModeSettings: String, displayIndex: Int): String

    fun getSetting(watchSportModeGroup: String, watchSportModeSettings: String, settingId: String): String

    fun isDeviceSupported(): Double

    fun setDisplayValuesAsStrings(watchSportModeDisplays: String): String

    fun setDisplayValuesAsEnums(watchSportModeDisplays: String): String
}
