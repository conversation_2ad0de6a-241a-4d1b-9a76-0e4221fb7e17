package com.stt.android.backgroundwork

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ListenableWorker
import androidx.work.WorkerFactory
import androidx.work.WorkerParameters
import com.stt.android.data.BuildConfig
import timber.log.Timber
import javax.inject.Inject

class CoroutineWorkerFactory
@Inject constructor(
    private val factories: Map<
        Class<out @JvmSuppressWildcards CoroutineWorker>,
        @JvmSuppressWildcards CoroutineWorkerAssistedFactory
        >
) : WorkerFactory() {
    override fun createWorker(
        appContext: Context,
        workerClassName: String,
        workerParameters: WorkerParameters
    ): ListenableWorker? {
        return try {
            val workerClass = Class.forName(workerClassName)
            val foundEntry = factories.entries.find { workerClass.isAssignableFrom(it.key) }
            foundEntry?.value?.create(appContext, workerParameters)
                ?: throw ClassNotFoundException()
        } catch (e: Exception) {
            if (workerClassName == "leakcanary.internal.HeapAnalyzerWorker") {
                // leakcanary uses its own worker, this factory doesn't instantiate it
                return null
            }

            if (BuildConfig.DEBUG && workerClassName.contains("FetchRoutesFromRemoteJob")) {
                // FetchRoutesFromRemoteJob was removed as RouteRemoteSyncJob handles also
                // pulling new routes from backend. However, some scheduled jobs may still be
                // persisted causing issues. In obfuscated builds the name will never match so only
                // prevent debug builds from crashing.
                return null
            }

            // don't throw exception here (WorkerWrapper just ignore it and log a useless message).
            // it will also break WorkerWrapper's fallback logic
            // and make third party libraries (like Glance) not working.
            Timber.w(e, "Could not find a matching factory for $workerClassName. Did you forget Dagger binding?")
            null
        }
    }
}
