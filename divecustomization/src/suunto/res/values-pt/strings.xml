<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="dive_mode_customization_title">PERSONALIZ. MODO MERGULHO</string>

    <!-- TODO: Some of the dive strings(like algos) are not localizable, move them to strings_non_localized file later -->

    <!-- Dive modes -->
    <string name="dive_mode_create">Criar modo mergulho</string>
    <string name="dive_mode_edit_title">Editar modo mergulho</string>
    <string name="dive_modes_in_device">Modos mergulho no disp.</string>
    <string name="dive_modes_edit">Editar</string>
    <string name="dive_modes_name">Nome do seu modo</string>
    <string name="dive_modes_name_max_characters">Máx. 15 carateres</string>
    <string name="dive_modes_name_invalid">Nome do modo inválido</string>
    <string name="dive_modes_sync_to_device">Sinc. com disp.</string>
    <string name="dive_modes_syncing_to_device">A sincr. c/dispos.</string>
    <string name="dive_modes_sync_failure">Falhou sincr. Verifique conexão do dispositivo.</string>
    <string name="dive_modes_sync_failure_mode_name">Imposs.sincr. c/dispos. Modos devem ter nomes exclusiv.</string>
    <string name="dive_modes_sync_to_device_info_text">Depois de sinc. alterações o dispositivo será reiniciado. Esta ação pode demorar alguns minutos.</string>
    <string name="dive_modes_number_selected">%d selecionado</string>
    <string name="dive_modes_select">Selec. modos merg.</string>
    <string name="dive_modes_settings_title">Definições</string>
    <string name="dive_modes_discard_changes_title">Ignorar alterações?</string>
    <string name="dive_modes_discard_changes_message">Se sair sem sincronizar, as alterações não serão guardadas.</string>
    <string name="dive_modes_discard_changes_and_leave">rejeitar e sair</string>
    <string name="dive_modes_discard_changes_continue_editing">Continuar a editar</string>
    <string name="dive_modes_active">Modo ativo</string>
    <string name="dive_modes_set_as_active">Def.como mod.ativo</string>
    <string name="dive_modes_set_as_active_dialog_title">Def.como mod.ativo?</string>
    <string name="dive_modes_set_as_active_dialog_content">O modo selecionado fica ativo quando mergulhar.</string>
    <string name="dive_delete_modes_dialog_title">Eliminar modos selec.?</string>
    <string name="dive_delete_all_modes_error_message">Imposs.eliminar tod. modos merg. Tem de ter, no mín., um modo mergulho no dispos.</string>
    <string name="dive_max_modes_added">Adicionou o número máx. de modos de mergulho. Elimine, pelo menos, um para adicionar um modo novo.</string>
    <string name="dive_device_not_connected">O dispositivo não está ligado. Não é possível guardar as alterações.</string>

    <!-- Dive styles -->
    <string name="dive_modes_style_title">Estilo de mergulho</string>
    <string name="TXT_APP_DIVE_STYLE_FREE">Mergulho livre</string>
    <string name="TXT_APP_DIVE_STYLE_OFF">Desligado</string>
    <string name="TXT_APP_DIVE_STYLE_SCUBA">Mergulho com garrafa</string>

    <!-- Dive modes -->
    <string name="dive_modes_diving_mode_title">Modo de mergulho</string>
    <string name="TXT_APP_DIVE_MODE_CCR">Circuito fechado com rebreather (CCR)</string>
    <string name="TXT_APP_DIVE_MODE_OC">Circuito aberto (OC)</string>
    <string name="TXT_APP_DIVE_MODE_FREE">Livre</string>
    <string name="TXT_APP_DIVE_MODE_GAUGE">Medidor (temporizador inferior)</string>
    <string name="TXT_APP_DIVE_MODE_OFF">Desligado</string>

    <!-- Dive mode flags type -->
    <string name="TXT_APP_DIVEMODE_FLAG_TYPE_OC">OC</string>
    <string name="TXT_APP_DIVEMODE_FLAG_TYPE_CCR">CCR</string>
    <string name="TXT_APP_DIVEMODE_FLAG_TYPE_FREE">Livre</string>
    <string name="TXT_APP_DIVEMODE_FLAG_TYPE_OFF">Desligado</string>

    <!-- Dive settings -->
    <string name="dive_modes_settings_item_title">Definições</string>
    <string name="dive_modes_settings_item_subtitle">Def. modo de mergulho, p. ex., algoritmo e def. pessoais</string>

    <!-- Dive mode Algorithms -->
    <string name="dive_modes_algorithm_title">Algoritmo</string>
    <string name="dive_modes_learn_about_algorithms">Ler mais acerca dos algoritmos de mergulho Suunto</string>
    <string name="TXT_APP_ALGORITHM_SUUNTO_FUSED2_RGBM">Suunto Fused™ RGBM 2</string>
    <string name="TXT_APP_ALGORITHM_BUHLMANN">@string/algorithm_buhlmann</string>
    <string name="TXT_APP_ALGORITHM_NONE">Nenhum(a)</string>
    <string name="dive_item_description_algorithm_buhlmann">Um algoritmo alternativo do computador de mergulho com fatores de gradiente.</string>
    <string name="dive_modes_learn_about_algorithms_buhlmann">Saiba mais sobre o algoritmo de mergulho Bühlmann 16 GF</string>
    <string name="dive_item_description_gradient_factors">Com os fatores de gradiente do algoritmo Bühlmann, pode controlar facilmente a forma como chega a um valor M aceitável de um compartimento de tecido durante um mergulho. Ou seja, os fatores de gradiente permitem aumentar o conservadorismo para que nenhum tecido chegue ao valor máximo.\n\nO valor Baixo determina a primeira paragem profunda (descompressão), enquanto que o valor Alto define o valor M ao chegar à superfície.\n\nAVISO: Não edite os valores do fator de gradiente até entender os seus efeitos.</string>
    <string name="dive_item_description_altitude_settings">Esta definição ajusta automaticamente o cálculo de descompressão de acordo com um dado intervalo de altitude.</string>

    <!-- Dive mode Conservatism -->
    <string name="dive_modes_conservatism_title">Pessoal</string>
    <string name="dive_modes_learn_about_conservatism">Saber como as definições pessoais influenciam o mergulho</string>
    <string name="TXT_APP_MORE_AGGRESSIVE">P-2 Mais agressivo</string>
    <string name="TXT_APP_AGGRESSIVE">P-1 Agressivo</string>
    <string name="TXT_APP_DEFAULT">P0 Predefinido</string>
    <string name="TXT_APP_CONSERVATIVE">P+1 Conservador</string>
    <string name="TXT_APP_MORE_CONSERVATIVE">P+2 Mais conservador</string>

    <!-- Dive mode Altitude -->
    <string name="dive_modes_altitude_title">Altitude</string>

    <!-- Dive mode min GF -->
    <string name="dive_modes_min_gf_title">Fator gradiente baixo</string>

    <!-- Dive mode max GF -->
    <string name="dive_modes_max_gf_title">Fator gradiente alto</string>

    <!-- Dive mode Stops -->
    <string name="dive_modes_stops_title">Paragens</string>
    <string name="dive_modes_safety_stop_title">Paragem de segurança</string>
    <string name="TXT_APP_3min">3 min</string>
    <string name="TXT_APP_4min">4 min</string>
    <string name="TXT_APP_5min">5 min</string>

    <!-- Deep Stop -->
    <string name="dive_modes_deep_stop_title">Paragem profund.</string>

    <!-- LastDecoStopDepth -->
    <string name="dive_modes_last_deco_stop_title">Profundidade da última paragem</string>
    <string name="TXT_APP_LAST_STOP_DEPTH_3M">3m</string>
    <string name="TXT_APP_LAST_STOP_DEPTH_6M">6m</string>
    <string name="TXT_APP_LAST_STOP_DEPTH_10FT">10 ft</string>
    <string name="TXT_APP_LAST_STOP_DEPTH_20FT">20 ft</string>

    <!-- Deco Profile -->
    <string name="dive_modes_deco_profile">Perfil deco</string>
    <string name="TXT_APP_DECO_PROFILE_STEPPED">Com patamares</string>
    <string name="TXT_APP_DECO_PROFILE_CONTINUOUS">Contínuo</string>

    <!-- Dive alarms -->
    <string name="dive_modes_alarms_title">Alarmes</string>
    <string name="dive_modes_alarms_description">Def. alarmes p/ atingir determ. profund., tempo merg. e pressão garrafa</string>
    <string name="dive_modes_depth_alarm">Alarme de profundidade</string>
    <string name="dive_modes_time_alarm">Alarme de tempo</string>
    <string name="dive_modes_tank_pressure_alarm">Alarme press.garrafa</string>
    <string name="dive_modes_gas_time_alarm">Alarme tempo gás</string>
    <string name="dive_alarm_time_hour">%d h</string>
    <string name="dive_alarm_time_minutes">%d min</string>
    <string name="dive_alarm_time_seconds">%d s</string>
    <string name="dive_time_picker_hours">h</string>
    <string name="dive_time_picker_minutes">min</string>
    <string name="dive_time_picker_seconds">s</string>

    <!-- Dive notifications -->
    <string name="dive_modes_notifications_title">Notificações</string>
    <string name="dive_modes_notifications_description">Defina notific. para tempo à superfície alcançado e até cinco profund. diferentes. Notificações podem ser alarme, vibração ou ambos.</string>
    <string name="TXT_APP_Off">Desligado</string>
    <string name="TXT_APP_Sound">Som</string>
    <string name="TXT_APP_Vibra">Vibração</string>
    <string name="TXT_APP_SoundAndVibra">Som e vibração</string>
    <string name="dive_mode_notification_number">Notificação de profundidade %d</string>
    <string name="dive_mode_surface_time_title">Notific. de superfície</string>

    <!-- Dive Gases -->
    <string name="dive_modes_gases_item_title">Gases</string>
    <string name="dive_modes_gases_item_subtitle">Adicione e edite gases no modo mergulho</string>
    <string name="dive_modes_gases_screen_title">Def. de gás</string>
    <string name="dive_modes_gases_helium_title">Hélio</string>
    <string name="dive_modes_gases_helium_subtitle">Permite mergulhar com trimix</string>
    <string name="dive_modes_gases_multiple_title">Vários gases</string>
    <string name="dive_modes_gases_modify_title">Modif. gases durante mergulho</string>
    <string name="dive_modes_gases_modify_subtitle">Permite edição gás durante mergulho</string>
    <string name="dive_modes_gases_general_settings_section">Definições gerais</string>
    <string name="dive_modes_gases_setpoint_item_title">Ponto ajuste</string>
    <string name="dive_modes_gases_fixed_gas_max_po2">Gás fixo máx. pO₂</string>
    <string name="dive_modes_gases_gas_max_po2_manual">TSS (Manual)</string>
    <string name="dive_modes_gases_oxygen">Oxigénio</string>
    <string name="dive_modes_gases_air">Ar</string>
    <string name="dive_modes_gases_heliox">Heliox</string>
    <string name="dive_modes_gases_mixed">Gás misto</string>

    <string name="dive_modes_gas_type_oc">Circuito aberto (OC)</string>
    <string name="dive_modes_gas_type_cc">Circuito fechado (CC)</string>

    <string name="dive_modes_gas_type_title">Tipo de gás</string>

    <string name="dive_modes_gas_po2">pO₂</string>
    <string name="dive_modes_add_gas">Adic. gás</string>
    <string name="dive_modes_gas_mixture">Mistura gases</string>
    <string name="dive_modes_gas_oxygen_molecular">O2</string>
    <string name="dive_modes_gas_helium_molecular">He</string>
    <string name="dive_modes_gas_tank_settings">Def. garrafa</string>
    <string name="dive_modes_gas_tank_fill_pressure">Pressão enchim.</string>
    <string name="dive_modes_gas_tank_size">Tam.</string>
    <string name="dive_modes_gas_tank_air_capacity">Capacidade de ar</string>
    <string name="dive_modes_gas_tank_size_unit_liter">l</string>
    <string name="dive_modes_gas_tank_size_cubic_feet">ft³</string>
    <string name="dive_modes_gas_delete">Eliminar gás?</string>
    <string name="dive_modes_gases_section_oc">Gases de CA</string>
    <string name="dive_modes_gases_section_cc">Gases de CF</string>
    <string name="dive_modes_gases_tank_pod">Tank POD</string>
    <string name="dive_modes_gases_select_tank_pod">Selecionar Tank POD</string>
    <string name="dive_modes_gases_pair_pod_in_device">Emparelhar dispositivo</string>
    <string name="dive_modes_gases_pod_remove">Remover</string>
    <string name="dive_modes_gases_pod_id">Tank POD: %s</string>

    <!-- Dive setpoint -->
    <string name="dive_modes_setpoint_low">Pt. ajuste pO₂ baixo</string>
    <string name="dive_modes_setpoint_high">Pt. ajuste pO₂ alto</string>
    <string name="dive_modes_switch_point_low">Pt. mudança baixo</string>
    <string name="dive_modes_switch_point_high">Pt. mudança alto</string>

    <!-- Dive Displays -->
    <string name="dive_modes_displays_title">Vistas</string>
    <string name="dive_modes_displays_item_subtitle">Personalizar os dados que vê no dispositivo de mergulho</string>
    <string name="dive_modes_displays_add_display">Adicionar vista</string>
    <string name="dive_modes_displays_select_display">Selecionar vista</string>
    <string name="dive_modes_displays_edit_display_data">Editar dados de vista</string>
    <string name="dive_modes_displays_select_data">Selecionar dados</string>
    <string name="dive_modes_displays_fixed_data">Dados fixos</string>
    <string name="dive_modes_displays_switchable_data">Dados alternáveis</string>
    <string name="dive_modes_display_delete">Eliminar vista?</string>
    <string name="dive_modes_display_max_reached">Número máximo de vistas para o dispositivo já adicionado</string>
    <string name="dive_modes_display_change">Alterar</string>
    <string name="dive_modes_display_change_data_hint">O dispositivo mostra os dados numa ordem diferente.</string>
    <string name="dive_modes_display_change_style_dialog_title">Escolher um estilo diferente elimina as vistas adicionadas</string>
    <string name="dive_modes_display_change_style_dialog_content">Se alterar o estilo, todas as vistas adicionadas com um estilo diferente serão eliminadas.</string>

    <!-- Graphical Displays -->
    <string name="dive_modes_displays_graphical">Gráfico</string>
    <string name="TXT_APP_GraphicDive">Predefinido</string>
    <string name="TXT_APP_GraphicCompass">Bússola</string>
    <string name="TXT_APP_GraphicTank">Press. garrafa</string>
    <string name="TXT_APP_GraphicTimer">Temporizador</string>
    <!-- Classic Displays -->
    <string name="dive_modes_displays_classic">Clássico</string>
    <string name="TXT_APP_ClassicDive">Predefinido</string>
    <string name="TXT_APP_ClassicCompass">Bússola</string>
    <!-- Prominent Displays -->
    <string name="dive_modes_displays_prominent">Destacado</string>
    <string name="TXT_APP_ProminentDive">Predefinido</string>
    <string name="TXT_APP_ProminentCompass">Bússola</string>
    <string name="TXT_APP_ProminentTank">Pressão da garrafa</string>
    <string name="TXT_APP_ProminentTimer">Temporizador</string>
    <!-- Round graphical Displays -->
    <string name="dive_modes_displays_round_graphical">Gráfico</string>
    <string name="TXT_APP_RoundGraphicalDaily">Todo o dia</string>
    <string name="TXT_APP_RoundGraphicalDive">Sem desc. (Predef.)</string>
    <string name="TXT_APP_RoundGraphicalCompass">Bússola</string>
    <string name="TXT_APP_RoundGraphicalTank">Press. garrafa</string>
    <string name="TXT_APP_RoundGraphicalTimer">Temporizador</string>
    <!-- Round graphical free displays -->
    <string name="dive_modes_displays_round_graphical_free">Mergulho livre</string>
    <string name="TXT_APP_RoundGraphicalFreeDive">Sem desc. (Predef.)</string>
    <string name="TXT_APP_RoundGraphicalFreeCompass">Bússola</string>
    <string name="TXT_APP_RoundGraphicalFreeDepth">Profundidade</string>
    <string name="TXT_APP_RoundGraphicalFreeTimer">Temporizador</string>

    <!-- Dive Display field values -->
    <string name="TXT_APP_Empty">Vazio</string>
    <string name="TXT_APP_Depth">Profundidade</string>
    <string name="TXT_APP_AvgDepth">Profund.média</string>
    <string name="TXT_APP_MaxDepth">Profund. máx.</string>
    <string name="TXT_APP_NoDecTime">Sem tempo descompressão</string>
    <string name="TXT_APP_TimeToSurface">Tempo para superfície</string>
    <string name="TXT_APP_Ceiling">Teto</string>
    <string name="TXT_APP_Ventilation">Consumo</string>
    <string name="TXT_APP_CurrentGas">Gás atual</string>
    <string name="TXT_APP_DiveTime">Tempo merg.</string>
    <string name="TXT_APP_BatteryTime">Tempo bateria</string>
    <string name="TXT_APP_Temperature">Temperatura</string>
    <string name="TXT_APP_TankPressure">Press. garrafa</string>
    <string name="TXT_APP_Time">Tempo</string>
    <string name="TXT_APP_Timer">Temporizador</string>
    <string name="TXT_APP_PO2">pO₂</string>
    <string name="TXT_APP_CNS">CNS</string>
    <string name="TXT_APP_OTU">OTU</string>
    <string name="TXT_APP_DiveTimer">Temporizador</string>
    <string name="TXT_APP_Gas">Gás</string>
    <string name="TXT_APP_GasTime">Tempo gás</string>
    <string name="TXT_APP_DiveType">Tipo de mergulho</string>
    <string name="TXT_APP_StopTime">Tempo de paragem</string>
    <string name="TXT_APP_CCRO2TankPressure">Pressão garrafa de O₂ do CCR</string>
    <string name="TXT_APP_Heading">Direção</string>
    <string name="TXT_APP_MaxAscentSpeed">Veloc. máx. subida</string>
    <string name="TXT_APP_MaxAscendSpeed">Veloc. máx. subida</string> <!-- Have to add it here due to a typo on data side, remove when fixed -->
    <string name="TXT_APP_MaxDescentSpeed">Veloc. máx. descida</string>
    <string name="TXT_APP_SurfaceInterval">Intervalo superfície</string>
    <string name="TXT_APP_DualTime">Hr dupla</string>
    <string name="TXT_APP_SetPoint">Ponto ajuste</string>
    <string name="TXT_APP_SStopTime">Tempo da paragem de segurança</string>
    <string name="TXT_APP_DStopTime">Tempo paragem profundidade</string>
    <string name="TXT_APP_Temperature_Time">Temperatura/Tempo</string>
    <string name="TXT_APP_C1_Battery">Bateria</string>
    <string name="TXT_APP_C1_Battery_Time">Bateria/Tempo</string>
    <string name="TXT_APP_C1_Battery_Temperature">Bateria/Temperatura</string>
    <string name="dive_max_fields_reached">Só pode adicionar até %d campos</string>

</resources>
