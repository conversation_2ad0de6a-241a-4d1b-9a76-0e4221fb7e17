package com.stt.android.domain.divecustomization

import com.soy.algorithms.divemodecustomization.DiveDeviceConfigValidator
import com.soy.algorithms.divemodecustomization.entities.CreateDivingGasResponse
import com.soy.algorithms.divemodecustomization.entities.DeviceName
import com.soy.algorithms.divemodecustomization.entities.DiveDeviceConfig
import com.soy.algorithms.divemodecustomization.entities.Mode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class DiveDeviceCreateGasUseCase
@Inject constructor(
    private val diveDeviceConfigValidator: DiveDeviceConfigValidator,
) {
    suspend fun createNewDivingGas(
        deviceConfig: DiveDeviceConfig,
        deviceName: DeviceName,
        state: String,
        mode: Mode
    ): Resource<CreateDivingGasResponse> = withContext(Dispatchers.IO) {
        try {
            Success(
                diveDeviceConfigValidator.createNewDivingGas(
                    deviceConfig,
                    deviceName.value,
                    state,
                    mode
                )
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to create new dive gas with: ${e.message}")
            Failure(Reason("Failed to create new dive gas", e))
        }
    }
}
