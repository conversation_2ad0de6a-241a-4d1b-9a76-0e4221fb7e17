package com.stt.android.domain.divecustomization

import com.soy.algorithms.divemodecustomization.DiveDeviceConfigValidator
import com.soy.algorithms.divemodecustomization.entities.CreateViewResponse
import com.soy.algorithms.divemodecustomization.entities.DeviceName
import com.soy.algorithms.divemodecustomization.entities.DiveDeviceConfig
import com.soy.algorithms.divemodecustomization.entities.Mode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class DiveDeviceCreateViewUseCase @Inject constructor(
    private val diveDeviceConfigValidator: DiveDeviceConfigValidator,
) {
    suspend fun createNewView(
        deviceConfig: DiveDeviceConfig,
        deviceName: DeviceName,
        mode: Mode
    ): Resource<CreateViewResponse> = withContext(Dispatchers.IO) {
        try {
            Success(
                diveDeviceConfigValidator.createNewView(
                    deviceConfig,
                    deviceName.value,
                    mode
                )
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to create new view with: ${e.message}")
            Failure(Reason("Failed to create new view", e))
        }
    }
}
