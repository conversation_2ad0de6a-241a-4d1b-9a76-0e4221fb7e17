package com.stt.android.menstrualcycle.datasource

import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.menstrualcycle.domain.MenstrualCycleRemoteDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.remote.MenstrualCycleRestApi
import com.stt.android.menstrualcycle.remote.RemoteMenstrualCycle
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.inject.Inject

class MenstrualCycleRemoteDataSourceImpl @Inject constructor(
    private val menstrualCycleRestApi: MenstrualCycleRestApi
) : MenstrualCycleRemoteDataSource {

    override suspend fun fetchMenstrualCycles(): List<MenstrualCycle> {
        return menstrualCycleRestApi.fetchMenstrualCycles()
            .payloadOrThrow()
            .map { it.toDomain() }
    }

    override suspend fun saveMenstrualCycles(menstrualCycles: List<MenstrualCycle>): List<MenstrualCycle> {
        return menstrualCycleRestApi.saveMenstrualCycle(menstrualCycles.map { it.toRemote() })
            .payloadOrThrow()
            .map { it.toDomain() }
    }

    override suspend fun deleteMenstrualCycle(remoteKeys: List<String>) {
        menstrualCycleRestApi.deleteMenstrualCycle(remoteKeys).payloadOrThrow()
    }
}

private fun MenstrualCycle.toRemote() = RemoteMenstrualCycle(
    remoteKey = this.remoteKey,
    includedDates = this.includedDates.map { it.toZonedDateTime() },
    startDate = this.startDate.toZonedDateTime(),
    endDate = this.endDate.toZonedDateTime(),
    modifiedTime = this.modifiedTime
)

private fun LocalDate.toZonedDateTime() = this.atStartOfDay(ZoneId.systemDefault())

private fun RemoteMenstrualCycle.toDomain(): MenstrualCycle {
    val startDate = this.startDate.toLocalDateWithZone()
    val endDate = this.endDate.toLocalDateWithZone()
    return MenstrualCycle(
        localId = null,
        remoteKey = this.remoteKey,
        includedDates = this.includedDates?.map { it.toLocalDateWithZone() }?.toSet()?.sorted() ?: emptyList(),
        startDate = startDate,
        endDate = endDate,
        menstrualCycleType = MenstrualCycleType.HISTORICAL,
        modifiedTime = this.modifiedTime
    )
}

private fun ZonedDateTime.toLocalDateWithZone() =
    this.withZoneSameInstant(ZoneId.systemDefault()).toLocalDate()
