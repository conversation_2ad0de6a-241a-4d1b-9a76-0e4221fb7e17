package com.stt.android.menstrualcycle.remote

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import java.time.ZonedDateTime

@JsonClass(generateAdapter = true)
data class RemoteMenstrualCycle(
    @<PERSON><PERSON>(name = "id") val remoteKey: String?,
    @Json(name = "dates") val includedDates: List<ZonedDateTime>?,
    @<PERSON>son(name = "startDate")val startDate: ZonedDateTime,
    @<PERSON>son(name = "endDate") val endDate: ZonedDateTime,
    @<PERSON>son(name = "modified") val modifiedTime: Long?
)
