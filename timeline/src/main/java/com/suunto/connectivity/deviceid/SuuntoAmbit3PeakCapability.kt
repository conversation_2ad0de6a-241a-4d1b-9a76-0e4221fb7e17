package com.suunto.connectivity.deviceid

import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

/**
 * Device capabilities for Ambit3Peak
 *
 *
 * Created by <PERSON><PERSON> on 9/1/2016.
 */
internal class SuuntoAmbit3PeakCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.Ambit3Peak
    override val generation = SuuntoDeviceGeneration.OBI2
    override val isWhiteboard = false
    override val isLegacy = true
    override val isWatch = true
    override val isSensor = false
    override val isAmbit = true
    override val isTraverse = false
    override val isEon = false
    override val isSpartan = false
    override val isTrainer = false

    override fun hasGpsSensor(): Boolean {
        return true
    }

    override fun supportsEpoFiles(): Boolean {
        return true
    }

    override fun supportsBarometricAltitude(): Boolean {
        return true
    }

    override fun supportsBarographMetric(): Boolean {
        return true
    }

    override fun supportsFusedAltitude(): Boolean {
        return true
    }

    override fun supportsStormAlarm(): Boolean {
        return true
    }

    override fun supportsSunriseAlarm(): Boolean {
        return false
    }

    override fun supportsSunsetAlarm(): Boolean {
        return false
    }

    override fun supportsTemperature(): Boolean {
        return true
    }

    override fun supportsAirPressure(): Boolean {
        return true
    }

    override fun supportsMultisportMode(): Boolean {
        return true
    }

    override fun supportsWorkouts(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsHeartRateLimits(): Boolean {
        return true
    }

    override fun canBeUpdatedToSupportWorkouts(): Boolean {
        return true
    }

    override fun supportsBikePod(): Boolean {
        return true
    }

    override fun supportsBikePowerMetrics(): Boolean {
        return true
    }

    override fun supportsCadenceMetric(): Boolean {
        return true
    }

    override fun supportsCadencePod(): Boolean {
        return true
    }

    override fun supportsFootPod(): Boolean {
        return true
    }

    override fun supportsPowerPod(): Boolean {
        return true
    }

    override fun supportsVibration(): Boolean {
        return false
    }

    override fun supportsAutoScroll(): Boolean {
        return true
    }

    override fun supportsIntervalTimer(): Boolean {
        return true
    }

    override fun supportsExtendedCharacterSet(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsBacklightColor(): Boolean {
        return false
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSystemEvents(): Boolean {
        return false
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return false
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        return false
    }

    override fun gpsHoursBest(): Int {
        return 20
    }

    override fun gpsHoursGood(): Int {
        return 30
    }

    override fun gpsHoursOK(): Int {
        return 200
    }

    override fun toString(): String {
        return SuuntoDeviceType.Ambit3Peak.toString()
    }
}
