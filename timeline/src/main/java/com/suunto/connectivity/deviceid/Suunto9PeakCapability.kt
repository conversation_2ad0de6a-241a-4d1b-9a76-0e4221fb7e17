package com.suunto.connectivity.deviceid

import com.stt.android.domain.firmware.Version
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import timber.log.Timber

internal class Suunto9PeakCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.Suunto9Peak
    override val generation = SuuntoDeviceGeneration.WHITEBOARD
    override val isWhiteboard = true
    override val isLegacy = false
    override val isWatch = true
    override val isSensor = false
    override val isAmbit = false
    override val isTraverse = false
    override val isEon = false
    override val isSpartan = true
    override val isTrainer = false

    override fun hasGpsSensor(): Boolean {
        return true
    }

    override fun supportsEpoFiles(): Boolean {
        return true
    }

    override fun supportsBarometricAltitude(): Boolean {
        return true
    }

    override fun supportsBarographMetric(): Boolean {
        return true
    }

    override fun supportsFusedAltitude(): Boolean {
        return true
    }

    override fun supportsStormAlarm(): Boolean {
        return true
    }

    override fun supportsSunriseAlarm(): Boolean {
        return false
    }

    override fun supportsSunsetAlarm(): Boolean {
        return false
    }

    override fun supportsTemperature(): Boolean {
        return true
    }

    override fun supportsAirPressure(): Boolean {
        return true
    }

    override fun supportsMultisportMode(): Boolean {
        return true
    }

    override fun supportsWorkouts(firmwareVersion: String?): Boolean {
        return false
    }

    override fun supportsHeartRateLimits(): Boolean {
        return true
    }

    override fun canBeUpdatedToSupportWorkouts(): Boolean {
        return false
    }

    override fun supportsBikePod(): Boolean {
        return true
    }

    override fun supportsBikePowerMetrics(): Boolean {
        return true
    }

    override fun supportsCadenceMetric(): Boolean {
        return true
    }

    override fun supportsCadencePod(): Boolean {
        return true
    }

    override fun supportsFootPod(): Boolean {
        return true
    }

    override fun supportsPowerPod(): Boolean {
        return true
    }

    override fun supportsVibration(): Boolean {
        return true
    }

    override fun supportsAutoScroll(): Boolean {
        return true
    }

    override fun supportsIntervalTimer(): Boolean {
        return true
    }

    override fun supportsExtendedCharacterSet(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsBacklightColor(): Boolean {
        return false
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsSystemEvents(): Boolean {
        return true
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return true
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        val result = runCatching {
            Version(firmwareVersion) >= Version("2.17.5")
        }
        return result
            .onFailure { Timber.w(it, "Error in supportsMediaAndNotificationControls($firmwareVersion)") }
            .getOrDefault(false)
    }

    override fun gpsHoursBest(): Int {
        return 10
    } // Battery 500mAh

    override fun gpsHoursGood(): Int {
        return 15
    }

    override fun gpsHoursOK(): Int {
        return 70
    }

    override fun toString(): String {
        return SuuntoDeviceType.Suunto9Peak.toString()
    }
}
