package com.stt.android.logbook

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class SuuntoLogbookDiving(
    @<PERSON><PERSON>(name = "Gases") val gases: List<SuuntoLogbookGas>?,
    @<PERSON><PERSON>(name = "Partner") val partner: String?,
    @<PERSON><PERSON>(name = "Master") val master: String?,
    @<PERSON><PERSON>(name = "Algorithm") val algorithm: String?,
    @<PERSON><PERSON>(name = "DiveMode") val diveMode: String?,
    @<PERSON><PERSON>(name = "DiveModeFlags") val diveModeFlags: SuuntoLogbookDiveModeFlags?,
    @<PERSON><PERSON>(name = "Conservatism") val conservatism: Int?,
    @<PERSON><PERSON>(name = "Altitude") val altitude: Float?,
    @<PERSON><PERSON>(name = "AlgorithmTransitionDepth") val algorithmTransitionDepth: Float?,
    @<PERSON><PERSON>(name = "MaxEND") val maxEnd: Float?,
    @<PERSON><PERSON>(name = "DeepStopEnabled") val deepStopEnabled: Boolean?,
    @Json(name = "SafetyStopTime") val safetyStopTime: Float?,
    @<PERSON><PERSON>(name = "LastDecoStopDepth") val lastDecoStopDepth: Float?,
    @Json(name = "FixedPO2") val fixedPo2: SuuntoLogbookFixedPo2?,
    @Json(name = "SurfaceTime") val surfaceTime: Float?,
    @Json(name = "DaysInSeries") val daysInSeries: Int?,
    @Json(name = "PreviousDiveDepth") val previousDiveDepth: Float?,
    @Json(name = "NumberInSeries") val numberInSeries: Int?,
    @Json(name = "SurfacePressure") val surfacePressure: Float?,
    @Json(name = "DesaturationTime") val desaturationTime: Float?,
    @Json(name = "AlgorithmBottomTime") val algorithmBottomTime: Float?,
    @Json(name = "AlgorithmAscentTime") val algorithmAscentTime: Float?,
    @Json(name = "AlgorithmBottomMixture") val algorithmBottomMixture: SuuntoLogbookGas?,
    @Json(name = "LowSetPoint") val lowSetPoint: Float?,
    @Json(name = "HighSetPoint") val highSetPoint: Float?,
    @Json(name = "SwitchHighSetPoint") val switchHighSetPoint: SuuntoLogbookDivingSetpointSwitch?,
    @Json(name = "SwitchLowSetPoint") val switchLowSetPoint: SuuntoLogbookDivingSetpointSwitch?,
    @Json(name = "MinGF") val minGf: Float?,
    @Json(name = "MaxGF") val maxGf: Float?,
    @Json(name = "StartTissue") val startTissue: SuuntoLogbookTissue?,
    @Json(name = "EndTissue") val endTissue: SuuntoLogbookTissue?,
    @Json(name = "TimeFromReset") val timeFromReset: Int?,
    @Json(name = "AscentMode") val ascentMode: String?
)

@JsonClass(generateAdapter = true)
data class SuuntoLogbookGas(
    @Json(name = "State") val state: String?,
    @Json(name = "Oxygen") val oxygen: Float?,
    @Json(name = "Helium") val helium: Float?,
    @Json(name = "PO2") val po2: Float?,
    @Json(name = "TankSize") val tankSize: Float?,
    @Json(name = "TankFillPressure") val tankFillPressure: Float?,
    @Json(name = "TransmitterID") val transmitterID: String?,
    @Json(name = "TransmitterName") val transmitterName: String?,
    @Json(name = "TransmitterStartBatteryCharge") val transmitterStartBatteryCharge: Float?,
    @Json(name = "TransmitterEndBatteryCharge") val transmitterEndBatteryCharge: Float?,
    @Json(name = "StartPressure") val startPressure: Float?,
    @Json(name = "EndPressure") val endPressure: Float?,
    @Json(name = "UsedPressure") val usedPressure: Float?,
    @Json(name = "TotalVolume") val totalVolume: Float?,
    @Json(name = "UsedVolume") val usedVolume: Float?,
    @Json(name = "LeftVolume") val leftVolume: Float?,
    @Json(name = "Consumption") val consumption: Float?,
    @Json(name = "AverageDepth") val averageDepth: Float?,
    @Json(name = "TimeOfUse") val timeOfUse: Float?,
    @Json(name = "InsertTime") val insertTime: Float?,
    @Json(name = "RemoveTime") val removeTime: Float?
) {
    val startPressureKPa
        get() = startPressure?.div(1000f)

    val endPressureKPa
        get() = endPressure?.div(1000f)

    val usedPressureKPa
        get() = usedPressure?.div(1000f)
}

@JsonClass(generateAdapter = true)
data class SuuntoLogbookDiveModeFlags(
    @Json(name = "Type") val type: String?,
    @Json(name = "MultiGas") val multiGas: Boolean?,
    @Json(name = "Helium") val helium: Boolean?,
    @Json(name = "GasEdit") val gasEdit: Boolean?
)

@JsonClass(generateAdapter = true)
data class SuuntoLogbookFixedPo2(
    @Json(name = "Enabled") val enabled: Boolean?,
    @Json(name = "Value") val value: Float?
)

@JsonClass(generateAdapter = true)
data class SuuntoLogbookDivingSetpointSwitch(
    @Json(name = "Enabled") val enabled: Boolean?,
    @Json(name = "Depth") val depth: Float?
)

@JsonClass(generateAdapter = true)
data class SuuntoLogbookTissue(
    @Json(name = "CNS") val cns: Float?,
    @Json(name = "OTU") val otu: Float?,
    @Json(name = "OLF") val olf: Float?,
    @Json(name = "Nitrogen") val nitrogen: List<Int>?,
    @Json(name = "Helium") val helium: List<Int>?,
    @Json(name = "RgbmNitrogen") val rgbmNitrogen: Float?,
    @Json(name = "RgbmHelium") val rgbmHelium: Float?
)
