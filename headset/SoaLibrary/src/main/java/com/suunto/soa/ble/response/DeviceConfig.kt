package com.suunto.soa.ble.response

import com.suunto.soa.ble.control.attr.DeviceConfigAttr
import com.suunto.soa.ble.control.attr.SoaSportType
import com.suunto.soa.data.SaoDistanceUnit
import com.suunto.soa.data.SoaPoolDistance
import com.suunto.soa.toUInt

class DeviceConfig(data: ByteArray) {

    private var customizationButtons = mutableListOf<CustomizationButton>()
    private var soaSportType: SoaSportType = SoaSportType.OPEN_WATER_SWIMMING
    private var poolDistance: SoaPoolDistance? = null

    init {
        parseData(data)
    }

    private fun parseData(dataArray: ByteArray) {
        when (toUInt(dataArray[1], dataArray[2])) {
            toUInt(DeviceConfigAttr.CUSTOMIZATION_BUTTON.configType) ->
                parseCustomsButtons(dataArray)

            toUInt(DeviceConfigAttr.SPORT_TYPE_SETTING.configType) ->
                parseSportType(dataArray)

            toUInt(DeviceConfigAttr.POOL_DISTANCE_SETTING.configType) ->
                parsePoolDistance(dataArray)

            else -> {
                throw RuntimeException("parseDeviceConfigError, configType is not define")
            }
        }
    }

    private fun parseCustomsButtons(configData: ByteArray) {
        val configLength = configData[0].toInt()
        val data = ByteArray(configLength - 2)
        System.arraycopy(configData, 3, data, 0, data.size)
        if (data.size % 2 != 0) throw RuntimeException("parseCustomsButtons error")
        val buttons = mutableListOf<CustomizationButton>()
        for (i in data.indices step 2) {
            val shortcutKey = getButtonShortKey(data[i].toUInt())
            val buttonOperate = getButtonFunction(data[i + 1].toUInt())
            buttons.add(CustomizationButton(shortcutKey, buttonOperate))
        }
        customizationButtons.addAll(buttons)
    }

    private fun parseSportType(configData: ByteArray) {
        val type = configData[3].toUInt()
        SoaSportType.entries.find { it.type == type }?.let {
            soaSportType = it
        }
    }

    private fun parsePoolDistance(configData: ByteArray) {
        if (configData.size >= 8) {
            poolDistance = SoaPoolDistance(
                distanceInMetric = toUInt(configData[3], configData[4]),
                distanceInImperial = toUInt(configData[5], configData[6]),
                distanceUnit = SaoDistanceUnit.entries.find { it.value == configData[7].toUInt() }
                    ?: SaoDistanceUnit.METRIC
            )
        }
    }

    fun getCustomizationButtons() = customizationButtons

    fun getSportType(): SoaSportType = soaSportType

    fun getPoolDistance(): SoaPoolDistance? = poolDistance
}
