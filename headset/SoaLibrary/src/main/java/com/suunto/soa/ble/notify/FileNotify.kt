package com.suunto.soa.ble.notify

import com.suunto.soa.ble.exception.ParseException
import com.suunto.soa.data.FilePartData
import com.suunto.soa.toUInt
import com.suunto.soa.toULong

class FileNotify(bytes: ByteArray) : Notify<FilePartData>(bytes) {

    companion object {
        const val FILE_NOTIFY = 0xE8.toByte()
    }

    override fun buildData(dataBytes: ByteArray): FilePartData {
        return getFilePartData(dataBytes) ?: throw ParseException("file part data parse error.")
    }

    private fun getFilePartData(byteArray: ByteArray): FilePartData? {
        return if (byteArray.size <= 8) {
            null
        } else {
            val fileDataSn = toUInt(byteArray.copyOfRange(0, 2))
            val fileDataSize = toUInt(byteArray.copyOfRange(2, 4))
            val crc32 = toULong(byteArray.copyOfRange(4, 8))
            val fileDataArray = byteArray.copyOfRange(8, byteArray.size)
            FilePartData(fileDataSn, fileDataSize, crc32, fileDataArray)
        }
    }
}
