package com.suunto.soa.ble.workqueue

import android.os.Handler
import android.os.Looper
import com.suunto.soa.ble.exception.CancelException
import com.suunto.soa.command.Request
import io.reactivex.SingleEmitter
import io.reactivex.SingleOnSubscribe
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeoutException
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference

abstract class QueueOperation<T : Any> : Runnable, SingleOnSubscribe<T> {

    companion object {
        // Operation not run yet
        const val STATE_WAITING = 0

        // Operation is running, run method is executing
        const val STATE_RUNNING_BUSY = 1

        // Operation is still in progress, but run method has completed
        const val STATE_RUNNING = 2

        // Operation has finished but onStop has not been called yet
        const val STATE_STOP_PENDING = 3

        // Operation has finished and there are no pending actions
        const val STATE_FINISHED = 4
    }

    private val state = AtomicInteger(STATE_WAITING)

    private val blockingSemaphore = AtomicReference<Semaphore>()

    private var emitter: SingleEmitter<T>? = null

    private val timeoutHandler = Handler(Looper.getMainLooper())

    var tag: Any? = null

    open fun getTimeoutMills(): Long = Request.TIME_OUT

    override fun run() {
        onReady()

        if (!state.compareAndSet(STATE_WAITING, STATE_RUNNING_BUSY)) {
            releaseQueue()
            return
        }

        startTimeoutHandler()

        try {
            protectedRun()
        } catch (tr: Throwable) {
            onError(tr)
        }

        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_RUNNING)) {
            // No action
        } else if (state.compareAndSet(STATE_STOP_PENDING, STATE_FINISHED)) {
            onStop()
        }
    }

    @Throws(Throwable::class)
    abstract fun protectedRun()

    open fun getState() = state.get()

    protected open fun onCompleted(value: T?) {
        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_STOP_PENDING)) {
            return
        }
        if (state.compareAndSet(STATE_RUNNING, STATE_FINISHED)) {
            value?.let { tryEmitSuccess(it) }
            onStop()
            return
        }
        if (state.compareAndSet(STATE_WAITING, STATE_FINISHED)) {
            return
        }
    }

    protected open fun onCompleted() = onCompleted(null)

    protected open fun onError(tr: Throwable) {
        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_STOP_PENDING)) {
            return
        }
        if (state.compareAndSet(STATE_RUNNING, STATE_FINISHED)) {
            tryEmitError(tr)
            onStop()
            return
        }
        if (state.compareAndSet(STATE_WAITING, STATE_FINISHED)) {
            return
        }
    }

    open fun cancel(thr: Throwable? = null) {
        if (state.get() != STATE_FINISHED) {
            onError(CancelException("Queue operation[${this.javaClass.simpleName}] cancelled", thr))
        }
    }

    override fun subscribe(emitter: SingleEmitter<T>) {
        this.emitter = emitter
    }

    private fun tryEmitSuccess(t: T) {
        emitter?.let {
            if (!it.isDisposed) it.onSuccess(t)
        }
    }

    private fun tryEmitError(tr: Throwable) {
        emitter?.tryOnError(tr)
    }

    open fun setQueueBlockingSemaphore(semaphore: Semaphore) {
        blockingSemaphore.set(semaphore)
    }

    private fun onStop() {
        cancelTimeoutHandler()
        releaseQueue()
        onRelease()
    }

    private fun releaseQueue() {
        blockingSemaphore.getAndSet(null)?.release()
    }

    open fun startTimeoutHandler() {
        val timeoutMillis = getTimeoutMills()
        timeoutHandler.postDelayed({
            cancel(TimeoutException("Queue operation timed out."))
        }, timeoutMillis)
    }

    open fun cancelTimeoutHandler() {
        timeoutHandler.removeCallbacksAndMessages(null)
    }

    protected open fun onReady() {}

    protected open fun onRelease() {}
}
