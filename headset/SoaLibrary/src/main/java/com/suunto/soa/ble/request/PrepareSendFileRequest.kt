package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request
import com.suunto.soa.to2Bytes
import com.suunto.soa.to4Bytes
import java.io.File
import java.nio.ByteBuffer

class PrepareSendFileRequest(type: Int, file: File) : Request(
    CMD_REQUEST_RESPONSE, PREPARE_SEND_FILE_REQUEST, OpCodeSn.nextOpCodeSn(), getRequestData(type, file)
) {
    companion object {

        const val PREPARE_SEND_FILE_REQUEST = 0x34.toByte()
        private const val FILE_TYPE_SIZE = 1
        private const val FILE_LENGTH_SIZE = 4
        private const val FILE_STATUS_SIZE = 4
        private const val FILE_NAME_SIZE = 2
        private val FILE_STATUS_DATA = ByteArray(4)

        fun getRequestData(type: Int, file: File): ByteArray {
            val fileNameBytes = file.name.toByteArray()
            val fileLength = file.length().toInt()
            val dataLength =
                FILE_TYPE_SIZE + FILE_LENGTH_SIZE + FILE_STATUS_SIZE + FILE_NAME_SIZE + fileNameBytes.size
            val byteBuffer = ByteBuffer.allocate(dataLength)
            byteBuffer.put(type.toByte())
            byteBuffer.put(fileLength.to4Bytes())
            byteBuffer.put(FILE_STATUS_DATA)
            byteBuffer.put(fileNameBytes.size.to2Bytes())
            byteBuffer.put(fileNameBytes)
            return byteBuffer.array()
        }
    }
}
