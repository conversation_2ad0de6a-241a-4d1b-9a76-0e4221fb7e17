package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.SoaSportType
import com.suunto.soa.ble.response.CustomizationButton
import com.suunto.soa.data.SoaDeviceCapability

interface DeviceConfigFeatures {

    suspend fun setAppLanguage(language: String): Boolean

    suspend fun getCustomizationButtons(): List<CustomizationButton>

    suspend fun setCustomizationButtons(buttons: List<CustomizationButton>): Boolean

    suspend fun updateSportType(soaSportType: SoaSportType): Boolean

    suspend fun getSportType(): SoaSportType

    suspend fun getDeviceCapability(): SoaDeviceCapability
}
