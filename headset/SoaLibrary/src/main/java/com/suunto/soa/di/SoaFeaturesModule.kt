package com.suunto.soa.di

import com.suunto.soa.ble.client.ConnectFeatures
import com.suunto.soa.ble.client.ConnectStates
import com.suunto.soa.ble.client.DeviceConfigFeatures
import com.suunto.soa.ble.client.DeviceInfoFeatures
import com.suunto.soa.ble.client.DeviceStatusFeatures
import com.suunto.soa.ble.client.OfflineMusicFeatures
import com.suunto.soa.ble.client.JumpSettingFeatures
import com.suunto.soa.ble.client.NeckSettingFeatures
import com.suunto.soa.ble.client.RopeJumpSettingFeature
import com.suunto.soa.ble.client.RunningInfoFeatures
import com.suunto.soa.ble.client.SoaConnectFeatures
import com.suunto.soa.ble.client.SoaConnectStates
import com.suunto.soa.ble.client.SoaDeviceConfigFeatures
import com.suunto.soa.ble.client.SoaDeviceInfoFeatures
import com.suunto.soa.ble.client.SoaDeviceStatusFeatures
import com.suunto.soa.ble.client.SoaOfflineMusicFeatures
import com.suunto.soa.ble.client.SoaJumpSettingFeatures
import com.suunto.soa.ble.client.SoaNeckSettingFeatures
import com.suunto.soa.ble.client.SoaRopeJumpSettingFeature
import com.suunto.soa.ble.client.SoaRunningInfoFeatures
import com.suunto.soa.ble.client.SoaSwimSettingFeatures
import com.suunto.soa.ble.client.SwimSettingFeatures
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@InstallIn(SingletonComponent::class)
@Module
abstract class SoaFeaturesModule {

    @Binds
    abstract fun bindConnectFeatures(connectFeatures: SoaConnectFeatures): ConnectFeatures

    @Binds
    abstract fun bindConnectStates(connectStates: SoaConnectStates): ConnectStates

    @Binds
    abstract fun bindDeviceInfoFeatures(deviceInfoFeatures: SoaDeviceInfoFeatures): DeviceInfoFeatures

    @Binds
    abstract fun bindRunningInfoFeatures(runningInfoFeatures: SoaRunningInfoFeatures): RunningInfoFeatures

    @Binds
    abstract fun bindDeviceConfigFeatures(deviceConfigFeatures: SoaDeviceConfigFeatures): DeviceConfigFeatures

    @Binds
    abstract fun bindDeviceStatusFeatures(deviceStatusFeatures: SoaDeviceStatusFeatures): DeviceStatusFeatures

    @Binds
    abstract fun bindOfflineMusicFeatures(offlineMusicFeatures: SoaOfflineMusicFeatures): OfflineMusicFeatures

    @Binds
    abstract fun bindNeckSettingFeatures(neckSettingFeatures: SoaNeckSettingFeatures): NeckSettingFeatures

    @Binds
    abstract fun bindJumpSettingFeatures(jumpSettingFeatures: SoaJumpSettingFeatures): JumpSettingFeatures

    @Binds
    abstract fun bindSwimSettingFeatures(swimSettingFeatures: SoaSwimSettingFeatures): SwimSettingFeatures

    @Binds
    abstract fun bindRopeJumpSettingFeatures(ropeJumpSettingFeatures: SoaRopeJumpSettingFeature): RopeJumpSettingFeature
}
