package com.suunto.soa.command

import com.suunto.soa.DataConstants.CMD_SIZE
import com.suunto.soa.DataConstants.CONTENT_SIZE_SIZE
import com.suunto.soa.DataConstants.END_MASK
import com.suunto.soa.DataConstants.OPCODE_SIZE
import com.suunto.soa.DataConstants.OPCODE_SN_SIZE
import com.suunto.soa.DataConstants.START_MASK
import com.suunto.soa.DataConstants.STATUS_SIZE
import com.suunto.soa.ble.response.AnyResponse
import com.suunto.soa.to2Bytes
import java.nio.ByteBuffer

internal class ResponseParamsData(
    val cmd: Byte,
    val opCode: Byte,
    val contentSize: Int,
    val opCodeSn: Byte,
    val status: Byte,
    val data: ByteArray
) {

    constructor(cmd: Byte, opCode: Byte, opCodeSn: Byte, status: Byte) : this(
        cmd,
        opCode,
        OPCODE_SN_SIZE + STATUS_SIZE,
        opCodeSn,
        status,
        byteArrayOf()
    )

    fun toByteArray(): ByteArray =
        ByteBuffer.allocate(START_MASK.size + CMD_SIZE + OPCODE_SIZE + CONTENT_SIZE_SIZE + OPCODE_SN_SIZE + STATUS_SIZE + data.size + END_MASK.size)
            .apply {
                put(START_MASK)
                put(cmd)
                put(opCode)
                put(contentSize.to2Bytes())
                put(opCodeSn)
                put(status)
                put(data)
                put(END_MASK)
            }.array()

    fun toAnyResponse(): AnyResponse {
        return AnyResponse(this.toByteArray())
    }
}
