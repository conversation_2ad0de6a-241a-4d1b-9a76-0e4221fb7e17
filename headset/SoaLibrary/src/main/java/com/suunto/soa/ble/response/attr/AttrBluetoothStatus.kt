package com.suunto.soa.ble.response.attr

import com.suunto.soa.toUInt
import com.suunto.soa.utils.TransformUtils

class AttrBluetoothStatus(bytes: ByteArray) {

    val connectedDevices: List<ConnectedDevice>

    init {
        connectedDevices = mutableListOf()
        val connectedSize = bytes[0].toUInt()
        var macStartIndex = 1
        repeat(connectedSize) {
            val macEndIndex = macStartIndex + 6
            val mac = TransformUtils.formatMac(bytes.copyOfRange(macStartIndex, macEndIndex))
            val nameLength = bytes[macEndIndex].toUInt()
            val nameStartIndex = macEndIndex + 1
            val nameEndIndex = nameStartIndex + nameLength
            val name = if (nameLength == 0) {
                null
            } else {
                TransformUtils.formatString(bytes.copyOfRange(nameStartIndex, nameEndIndex))
            }
            connectedDevices.add(ConnectedDevice(mac, name))
            macStartIndex = nameEndIndex
        }
    }

    data class ConnectedDevice(val mac: String, val name: String?)
}
