package com.suunto.soa.ble.client

import com.suunto.soa.data.NeckCheckoutResult

interface NeckSettingFeatures {

    suspend fun startNeckInitCheckout(): Boolean

    suspend fun stopNeckInitCheckout(): Boolean

    suspend fun startLeftRotationCheckout(): Boolean

    suspend fun stopLeftRotationCheckout(): Boolean

    suspend fun startRightRotationCheckout(): Boolean

    suspend fun stopRightRotationCheckout(): Boolean

    suspend fun startLateralBendingLeftCheckout(): Boolean

    suspend fun stopLateralBendingLeftCheckout(): Boolean

    suspend fun startLateralBendingRightCheckout(): Boolean

    suspend fun stopLateralBendingRightCheckout(): Boolean

    suspend fun startForwardTiltCheckout(): Boolean

    suspend fun stopForwardTiltCheckout(): Boolean

    suspend fun startBackwardTiltCheckout(): Boolean

    suspend fun stopBackwardTiltCheckout(): Boolean

    suspend fun syncNeckCheckoutResult(): NeckCheckoutResult
}
