package com.suunto.soa.ble.scanner

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import com.suunto.soa.toUInt
import com.suunto.soa.utils.TransformUtils
import timber.log.Timber

data class Device(
    val name: String?,
    val address: String,
    val serial: String,
    val advertising: Advertising?,
    val bonded: Boolean,
    val connected: Boolean,
    val bluetoothDevice: BluetoothDevice
) {
    override fun equals(other: Any?): <PERSON>olean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Device

        if (address != other.address) return false
        return serial == other.serial
    }

    override fun hashCode(): Int {
        var result = address.hashCode()
        result = 31 * result + serial.hashCode()
        return result
    }
}

class Advertising(ad: ByteArray) {

    companion object {
        private const val AD_LENGTH_SIZE = 1
        private const val AD_FLAG_SIZE = 1

        const val LOCAL_NAME = 0x09
        const val MANUFACTURER = 0xFF
    }

    private val packets = mutableMapOf<Int, Packet>()

    init {
        runCatching {
            var index = 0
            while (index < ad.size) {
                val length = ad[index].toUInt()
                index += AD_LENGTH_SIZE
                val flag = ad[index].toUInt()
                index += AD_FLAG_SIZE
                val content = ad.copyOfRange(index, index + (length - AD_FLAG_SIZE))
                parse(flag, content)?.let {
                    packets.put(flag, it)
                }
                index += content.size
            }
        }.onFailure {
            Timber.w(it, "parse device advertising fail.")
        }
    }

    fun getLocalName() = packets[LOCAL_NAME] as? LocalName

    fun getManufacturer() = packets[MANUFACTURER] as? Manufacturer

    private fun parse(flag: Int, content: ByteArray): Packet? {
        return when (flag) {
            LOCAL_NAME -> LocalName(content)

            MANUFACTURER -> Manufacturer(
                content
            )

            else -> null
        }
    }
}

abstract class Packet

class LocalName(val content: ByteArray) : Packet() {

    val adName = TransformUtils.formatString(content)
}

class Manufacturer(val content: ByteArray) : Packet() {

    companion object {
        val UNKNOWN_CRC = byteArrayOf(0xFF.toByte(), 0xFF.toByte())
    }

    val pid = content[4]
    val firstPhoneCrc = content.copyOfRange(5, 7)
    val secondPhoneCrc = content.copyOfRange(7, 9)
}

@SuppressLint("MissingPermission")
fun BluetoothDevice.validName() = this.name ?: ""
