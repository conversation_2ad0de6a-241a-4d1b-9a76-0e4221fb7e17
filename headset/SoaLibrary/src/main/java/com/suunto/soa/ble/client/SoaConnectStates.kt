package com.suunto.soa.ble.client

import com.suunto.soa.ble.service.BleState
import com.suunto.soa.ble.service.SoaConnectivity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.rx2.await
import javax.inject.Inject

class SoaConnectStates @Inject constructor(
    private val connectivity: SoaConnectivity
) : ConnectStates {

    override suspend fun observableState(): Flow<BleState> {
        return connectivity.observableConnectState().asFlow()
    }

    override suspend fun getState(): BleState {
        return connectivity.getConnectState().await()
    }

    override suspend fun waitForState(state: BleState) {
        connectivity.waitForBleState(state).await()
    }

    override suspend fun waitForReady() {
        connectivity.waitForBleReady().await()
    }
}
