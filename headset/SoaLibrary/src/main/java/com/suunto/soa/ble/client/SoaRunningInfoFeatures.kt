package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.BodySenseOperation
import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.DevicesConnectSupport
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.RunningInfoAttr
import com.suunto.soa.ble.control.attr.SettingAttr
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.control.attr.getDeviceUtcAttr
import com.suunto.soa.ble.control.attr.getNeckReminderIntervalAttr
import com.suunto.soa.ble.control.attr.getUserInfoAttr
import com.suunto.soa.ble.control.attr.isRoping
import com.suunto.soa.ble.control.attr.isRunning
import com.suunto.soa.ble.control.attr.isSwimming
import com.suunto.soa.ble.exception.CmdException
import com.suunto.soa.ble.response.RunningInfoResponse
import com.suunto.soa.ble.response.attr.AttrBluetoothStatus
import com.suunto.soa.ble.response.attr.OtaStatus
import com.suunto.soa.ble.service.SoaConnectivity
import com.suunto.soa.data.NeckIntervalData
import com.suunto.soa.data.SoaSwimType
import com.suunto.soa.data.SoaUserInfo
import com.suunto.soa.data.SportStatus
import kotlinx.coroutines.rx2.await
import java.nio.ByteBuffer
import javax.inject.Inject

class SoaRunningInfoFeatures @Inject constructor(
    private val connectivity: SoaConnectivity,
) : RunningInfoFeatures {

    private suspend fun putSetting(settingsData: ByteArray): Boolean {
        return connectivity.waitForController().flatMap {
            it.pushSetting(settingsData)
        }.map {
            it.isSuccess()
        }.await()
    }

    override suspend fun pushSetting(vararg attrs: SettingAttr): Boolean {
        val requestBytes = getSettingAttrRequestBytes(*attrs)
        return putSetting(requestBytes)
    }

    override suspend fun getRunningInfo(vararg attrs: RunningInfoAttr): RunningInfoResponse {
        return connectivity.waitForController().flatMap {
            it.queryRunningInfo(*attrs)
        }.await()
    }

    override suspend fun setBodySense(bodySenseOperation: BodySenseOperation): Boolean {
        val operation = if (bodySenseOperation == BodySenseOperation.OFF) {
            SettingAttr.ATTR_DEVICE_BODY_SENSE_OPERATION_SWITCH_OFF
        } else {
            SettingAttr.ATTR_DEVICE_BODY_SENSE_OPERATION_SWITCH_ON
        }
        return pushSetting(operation)
    }

    override suspend fun getBodySense(): BodySenseOperation {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_BODY_SENSE_OPERATION_SWITCH)
        if (response.isSuccess() && response.data.getBodySenseOperation() != null) {
            return response.data.getBodySenseOperation()!!
        } else {
            throw CmdException("get BodySense failed.")
        }
    }

    override suspend fun setEqMode(eqMode: EqMode): Boolean {
        val operation = when (eqMode) {
            EqMode.OUTDOOR -> SettingAttr.ATTR_DEVICE_EQ_MODE_OUTDOOR
            EqMode.UNDERWATER -> SettingAttr.ATTR_DEVICE_EQ_MODE_UNDERWATER
            else -> SettingAttr.ATTR_DEVICE_EQ_MODE_NORMAL
        }
        return pushSetting(operation)
    }

    override suspend fun getEqMode(): EqMode {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_EQ_MODE)
        if (response.isSuccess() && response.data.getEqMode() != null) {
            return response.data.getEqMode()!!
        } else {
            throw CmdException("get EqMode failed.")
        }
    }

    override suspend fun setSportsMode(
        sportsMode: SportsMode
    ): Boolean {
        val operation = when (sportsMode) {
            SportsMode.TEAM -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_TEAM
            SportsMode.LEADER -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_LEADER
            SportsMode.SOS -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_SOS
            SportsMode.RUNNING -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_RUNNING
            SportsMode.CYCLING -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_CYCLING
            else -> SettingAttr.ATTR_SPORTS_MODE_SWITCH_CLOSE
        }
        return pushSetting(operation)
    }

    override suspend fun getSportsMode(): SportsMode {
        val response = getRunningInfo(RunningInfoAttr.ATTR_SPORTS_MODE_SWITCH)
        if (response.isSuccess() && response.data.getSportsModeInd() != null) {
            return response.data.getSportsModeInd()!!
        } else {
            throw CmdException("get SportsMode failed.")
        }
    }

    override suspend fun setSupportDevicesConnect(support: DevicesConnectSupport): Boolean {
        val operation = when (support) {
            DevicesConnectSupport.SUPPORT -> SettingAttr.ATTR_DEVICE_2DEVICES_CONNECT_SUPPORT
            else -> SettingAttr.ATTR_DEVICE_2DEVICES_CONNECT_NOT_SUPPORT
        }
        return pushSetting(operation)
    }

    override suspend fun getSupportDevicesConnect(): DevicesConnectSupport {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_2DEVICES_CONNECT)
        if (response.isSuccess() && response.data.getDevicesConnectSupport() != null) {
            return response.data.getDevicesConnectSupport()!!
        } else {
            throw CmdException("get SupportDevicesConnect failed.")
        }
    }

    override suspend fun getConnectedDevices(): List<AttrBluetoothStatus.ConnectedDevice> {
        val response = getRunningInfo(RunningInfoAttr.ATTR_CLASSIC_BLUETOOTH_STATUS)
        if (response.isSuccess() && response.data.getBluetoothStatus()?.connectedDevices != null) {
            return response.data.getBluetoothStatus()?.connectedDevices ?: listOf()
        } else {
            throw CmdException("get SupportDevicesConnect failed.")
        }
    }

    override suspend fun setLowLatencyMode(lowLatencyMode: LowLatencyMode): Boolean {
        val operation = when (lowLatencyMode) {
            LowLatencyMode.ON -> SettingAttr.ATTR_DEVICE_LOW_LATENCY_MODE_ON
            else -> SettingAttr.ATTR_DEVICE_LOW_LATENCY_MODE_OFF
        }
        return pushSetting(operation)
    }

    override suspend fun getLowLatencyMode(): LowLatencyMode {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_LOW_LATENCY_MODE)
        if (response.isSuccess() && response.data.getLowLatencyMode() != null) {
            return response.data.getLowLatencyMode()!!
        } else {
            throw CmdException("get LowLatencyMode failed.")
        }
    }

    override suspend fun setNeckReminderInterval(interval: Int): Boolean {
        val neckReminderSetting = getNeckReminderIntervalAttr(interval)
        return putSetting(neckReminderSetting)
    }

    override suspend fun getNeckReminderInterval(): NeckIntervalData {
        val response = getRunningInfo(RunningInfoAttr.ATTR_NECK_REMINDER_INTERVAL)
        if (response.isSuccess() && response.data.getNeckReminderInterval() != null) {
            return response.data.getNeckReminderInterval()!!
        } else {
            throw CmdException("get neckReminderInterval failed.")
        }
    }

    private fun getSettingAttrRequestBytes(vararg attrs: SettingAttr): ByteArray {
        var countSize = 0
        attrs.forEach {
            countSize += it.mask.size + 1
        }
        val buffer = ByteBuffer.allocate(countSize)
        attrs.forEach {
            buffer.put((it.mask.size and 0xFF).toByte())
            buffer.put(it.mask)
        }
        return buffer.array()
    }

    override suspend fun getDeviceStatusBeforeOTA(): OtaStatus? {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_OTA_STATUS)
        if (response.isSuccess() && response.data.getOtaStatus() != null) {
            return response.data.getOtaStatus()
        } else {
            throw CmdException("get getDeviceOtaAvailable failed.")
        }
    }

    override suspend fun setDeviceMusicMode(musicMode: MusicMode): Boolean {
        val operation = when (musicMode) {
            MusicMode.ONLINE -> SettingAttr.ATTR_DEVICE_MUSIC_MODE_ONLINE
            MusicMode.OFFLINE -> SettingAttr.ATTR_DEVICE_MUSIC_MODE_OFFLINE
        }
        return pushSetting(operation)
    }

    override suspend fun getDeviceMusicMode(): MusicMode? {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_MUSIC_MODE)
        if (response.isSuccess() && response.data.getMusicMode() != null) {
            return response.data.getMusicMode()
        } else {
            throw CmdException("getDeviceMusicMode failed.")
        }
    }

    override suspend fun getCallingStatus(): CallStatus? {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_CALL_STATUS)
        if (response.isSuccess() && response.data.getCallingStatus() != null) {
            return response.data.getCallingStatus()
        } else {
            throw CmdException("getCallingStatus failed.")
        }
    }

    override suspend fun getSportStatus(
        isSwimSupported: Boolean,
        isJumpRopeSupported: Boolean,
        isRunningSupported: Boolean
    ): SportStatus {
        val attrs = mutableListOf<RunningInfoAttr>()
        if (isSwimSupported) attrs.add(RunningInfoAttr.ATTR_DEVICE_SWIM_STATUS)
        if (isJumpRopeSupported) attrs.add(RunningInfoAttr.ATTR_DEVICE_ROPE_STATUS)
        if (isRunningSupported) attrs.add(RunningInfoAttr.ATTR_DEVICE_RUNNING_STATUS)
        val response = getRunningInfo(*attrs.toTypedArray())
        if (response.isSuccess()) {
            return response.data.run {
                val swimmingType = getSwimmingType()
                val swimStatus = getSwimStatus()
                SportStatus(
                    isOpenWaterSwimming = (swimmingType == null || swimmingType == SoaSwimType.OPEN_WATER) && swimStatus?.isSwimming() == true,
                    isPoolSwimming = (swimmingType == SoaSwimType.POOL_SWIM) && swimStatus?.isSwimming() == true,
                    isJumpRoping = this.getJumpRopeStatus()?.isRoping() ?: false,
                    isRunning = this.getRunningStatus()?.isRunning() ?: false
                )
            }
        } else {
            throw CmdException("getSportStatus failed.")
        }
    }

    override suspend fun setDeviceUTC(timestamp: Long): Boolean {
        val userInfoSetting = getDeviceUtcAttr(timestamp)
        return putSetting(userInfoSetting)
    }

    override suspend fun setUserInfo(userInfo: SoaUserInfo): Boolean {
        val userInfoSetting = getUserInfoAttr(userInfo)
        return putSetting(userInfoSetting)
    }

    override suspend fun setDeviceLedStatus(isOpen: Boolean): Boolean {
        val operation = if (isOpen) {
            SettingAttr.ATTR_DEVICE_LED_OPEN
        } else {
            SettingAttr.ATTR_DEVICE_LED_CLOSE
        }
        return pushSetting(operation)
    }

    override suspend fun getDeviceLedStatus(): LedStatus? {
        val response = getRunningInfo(RunningInfoAttr.ATTR_DEVICE_LED_STATUS)
        if (response.isSuccess() && response.data.getLedStatus() != null) {
            return response.data.getLedStatus()!!
        } else {
            throw CmdException("get deviceLedStatus failed.")
        }
    }
}
