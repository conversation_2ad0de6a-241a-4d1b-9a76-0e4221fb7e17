<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/routeSelection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/Body.Medium.Bold"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:layout_marginStart="@dimen/size_spacing_medium"
        tools:text="@string/previous_on_all_route_capital"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/trend_recyclerview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/size_spacing_xlarge"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:background="?suuntoBackground"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/routeSelection" />

</androidx.constraintlayout.widget.ConstraintLayout>
