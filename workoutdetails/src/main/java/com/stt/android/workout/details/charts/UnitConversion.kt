package com.stt.android.workout.details.charts

import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryGraph

internal fun SummaryGraph.valueUnitConverter(activityType: ActivityType): MeasurementUnit.(Double) -> Double {
    return when (this) {
        SummaryGraph.SPEED -> MeasurementUnit::toSpeedUnit
        SummaryGraph.SPEEDKNOTS -> MeasurementUnit::toKnots
        SummaryGraph.ALTITUDE -> MeasurementUnit::toAltitudeUnit
        SummaryGraph.PACE -> {
            if (activityType.isSwimming) {
                MeasurementUnit::toSwimPaceUnit
            } else {
                MeasurementUnit::toPaceUnit
            }
        }
        SummaryGraph.VERTICALSPEED -> MeasurementUnit::toVerticalSpeedUnit
        SummaryGraph.TEMPERATURE -> MeasurementUnit::toTemperatureUnit
        SummaryGraph.AVGSKIPSRATE,
        SummaryGraph.CADENCE -> MeasurementUnit::fromHzToRpm
        SummaryGraph.POWER,
        SummaryGraph.SWIMSTROKERATE,
        SummaryGraph.SWOLF -> { value -> value }

        SummaryGraph.GASCONSUMPTION -> MeasurementUnit::toGasConsumptionUnit
        SummaryGraph.TANKPRESSURE -> MeasurementUnit::toPressureUnit
        SummaryGraph.DEPTH -> MeasurementUnit::toAltitudeUnit

        SummaryGraph.AEROBICZONE -> { value -> value }
        SummaryGraph.AEROBICHRTHRESHOLDS -> MeasurementUnit::fromHzToRpm
        SummaryGraph.AEROBICPOWERTHRESHOLDS -> MeasurementUnit::fromHzToRpm
        SummaryGraph.GROUNDCONTACTTIME -> MeasurementUnit::fromSecondsToMilliseconds
        SummaryGraph.VERTICALOSCILLATION -> MeasurementUnit::fromMeterToCentimeter
        SummaryGraph.DURATION,
        SummaryGraph.AVGFREESTYLEBREATHANGLE,
        SummaryGraph.BREATHINGRATE,
        SummaryGraph.FREESTYLEPITCHANGLE,
        SummaryGraph.BREASTSTROKEHEADANGLE,
        SummaryGraph.BREASTSTROKEGLIDETIME,
        SummaryGraph.AVGBREASTSTROKEBREATHANGLE,
        SummaryGraph.AVGSKIPSPERROUND-> { value -> value }
        else -> throw IllegalStateException("Not implemented: $this")
    }
}

internal fun SummaryGraph.reverseValueUnitConverter(activityType: ActivityType): MeasurementUnit.(Double) -> Double {
    return when (this) {
        SummaryGraph.SPEED -> MeasurementUnit::fromSpeedUnit
        SummaryGraph.SPEEDKNOTS -> MeasurementUnit::fromKnots
        SummaryGraph.ALTITUDE -> MeasurementUnit::fromAltitudeUnit
        SummaryGraph.PACE -> {
            if (activityType.isSwimming) {
                MeasurementUnit::fromSwimPaceUnit
            } else {
                MeasurementUnit::fromPaceUnit
            }
        }
        SummaryGraph.VERTICALSPEED -> MeasurementUnit::fromVerticalSpeedUnit
        SummaryGraph.TEMPERATURE -> MeasurementUnit::fromTemperatureUnit
        SummaryGraph.AVGSKIPSRATE,
        SummaryGraph.CADENCE -> MeasurementUnit::fromRpmToHz
        SummaryGraph.POWER,
        SummaryGraph.SWIMSTROKERATE,
        SummaryGraph.SWOLF -> { value -> value }

        SummaryGraph.GASCONSUMPTION -> MeasurementUnit::fromGasConsumptionUnit
        SummaryGraph.TANKPRESSURE -> MeasurementUnit::fromPressureUnit
        SummaryGraph.DEPTH -> MeasurementUnit::fromAltitudeUnit

        SummaryGraph.AEROBICZONE -> { value -> value }
        SummaryGraph.AEROBICHRTHRESHOLDS -> MeasurementUnit::fromRpmToHz
        SummaryGraph.AEROBICPOWERTHRESHOLDS -> MeasurementUnit::fromRpmToHz
        SummaryGraph.GROUNDCONTACTTIME -> MeasurementUnit::fromMillisecondsToSeconds
        SummaryGraph.VERTICALOSCILLATION -> MeasurementUnit::fromCentimeterToMeter
        SummaryGraph.DURATION,
        SummaryGraph.AVGFREESTYLEBREATHANGLE,
        SummaryGraph.BREATHINGRATE,
        SummaryGraph.FREESTYLEPITCHANGLE,
        SummaryGraph.BREASTSTROKEHEADANGLE,
        SummaryGraph.BREASTSTROKEGLIDETIME,
        SummaryGraph.AVGBREASTSTROKEBREATHANGLE,
        SummaryGraph.AVGSKIPSPERROUND -> { value -> value }
        else -> throw IllegalStateException("Not implemented: $this")
    }
}
