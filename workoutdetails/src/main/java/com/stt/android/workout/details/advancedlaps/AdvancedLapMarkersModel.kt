package com.stt.android.workout.details.advancedlaps

import android.content.Context
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.google.android.material.switchmaterial.SwitchMaterial
import com.google.android.material.tabs.TabLayout
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.extensions.stringRes
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.R
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableItems

@EpoxyModelClass
abstract class AdvancedLapMarkersModel : EpoxyModelWithHolder<AdvancedLapMarkersViewHolder>() {

    @EpoxyAttribute
    var stId: Int? = null

    @EpoxyAttribute
    lateinit var lapTables: List<AdvancedLapsTableItems>

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var selectLapsTableType: LapsTableType? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onLapsTableTypeSelected: ((LapsTableType) -> Unit)? = null

    @EpoxyAttribute
    var lapMarkerEnabled: Boolean = true

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onLapMarkerSwitchChanged: ((Boolean) -> Unit)? = null

    override fun getDefaultLayout() = R.layout.model_advanced_lap_markers

    override fun bind(holder: AdvancedLapMarkersViewHolder) {
        stId?.let { _ ->
            holder.tabLayout.removeAllTabs()
            holder.tabLayout.clearOnTabSelectedListeners()
            val context = holder.tabLayout.context
            lapTables.forEach { item ->
                val tab = holder.tabLayout.newTab()
                    .setCustomView(R.layout.tab_item)
                    .setTag(item)
                    .apply {
                        customView?.findViewById<TextView>(R.id.tabName)?.text =
                            item.getTitle(context)
                    }
                holder.tabLayout.addTab(tab, item.lapsTableType == selectLapsTableType)
            }
            holder.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab) {
                    onLapsTableTypeSelected?.invoke((tab.tag as AdvancedLapsTableItems).lapsTableType)
                }

                override fun onTabUnselected(tab: TabLayout.Tab) {
                }

                override fun onTabReselected(tab: TabLayout.Tab) {
                    onLapsTableTypeSelected?.invoke((tab.tag as AdvancedLapsTableItems).lapsTableType)
                }
            })

            holder.lapMarkerSwitch.isChecked = lapMarkerEnabled
            holder.lapMarkerSwitch.setOnCheckedChangeListener { _, isChecked ->
                onLapMarkerSwitchChanged?.invoke(isChecked)
            }
        }
    }

    private fun AdvancedLapsTableItems.getTitle(context: Context): String {
        return when (val type = lapsTableType) {
            LapsTableType.ONE_KM_AUTO_LAP,
            LapsTableType.FIVE_KM_AUTO_LAP,
            LapsTableType.TEN_KM_AUTO_LAP,
            LapsTableType.ONE_MILE_AUTO_LAP,
            LapsTableType.FIVE_MILE_AUTO_LAP,
            LapsTableType.TEN_MILE_AUTO_LAP,
            LapsTableType.DISTANCE_AUTO_LAP ->
                infoModelFormatter.formatDistanceAutoLapTitle(autoLapLength?.toDouble(), type)

            LapsTableType.DURATION_AUTO_LAP ->
                infoModelFormatter.formatDurationAutoLapTitle(autoLapLength?.toDouble(), type)

            LapsTableType.MANUAL,
            LapsTableType.INTERVAL,
            LapsTableType.DOWNHILL,
            LapsTableType.DIVE -> context.getString(type.stringRes())
        }
    }
}

class AdvancedLapMarkersViewHolder : KotlinEpoxyHolder() {
    val tabLayout by bind<TabLayout>(R.id.laps_tab_layout)
    val lapMarkerSwitch by bind<SwitchMaterial>(R.id.lapMarkerSwitch)
}
