package com.stt.android.workout.details.graphanalysis.map

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.getSingleValidLocation
import com.stt.android.domain.workouts.isHuntingOrFishing
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.activityTypeChangeCoordinates
import com.stt.android.extensions.getIndexOfHighlightedRoute
import com.stt.android.extensions.multisportRoutes
import com.stt.android.extensions.traverseEvents
import com.stt.android.routes.toLatLng
import com.stt.android.ski.SlopeSkiUtils
import com.stt.android.ui.components.RouteWithDashLinePoints
import com.stt.android.ui.map.MapHelper
import com.stt.android.workout.details.graphanalysis.playback.PlaybackFullWorkoutWindow
import com.stt.android.workout.details.graphanalysis.playback.PlaybackLapWindow
import com.stt.android.workout.details.graphanalysis.playback.PlaybackTimeWindow

object WorkoutMapRouteDataGenerator {
    fun generateWorkoutMapRouteDataForFullWorkout(
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader,
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml?
    ): WorkoutMapRouteData? {
        val activityType = workoutHeader.activityType
        val singleLocation =
            workoutHeader.getSingleValidLocation()
        val fullRouteLatLngs = getRouteLatLngs(
            geoPoints,
            singleLocation?.toLatLng()
        ).filterNot { it.latitude == 0.0 && it.longitude == 0.0 }
        val bounds = getLatLngBounds(fullRouteLatLngs) ?: return null
        val routeWithDashLinePoints = sml?.let {
            MapHelper.filterGeoPointsByActivity(geoPoints, it.streamData)?.let { result ->
                RouteWithDashLinePoints(result.first, result.second)
            }
        }
        fun generateBasicWorkoutWorkoutMapData(): BasicWorkoutMapRouteData {
            return BasicWorkoutMapRouteData(
                routeWithDashLinePoints = routeWithDashLinePoints,
                highlightedLapLatLngs = fullRouteLatLngs,
                nonHighlightedLatLngs = emptyList(),
                bounds = bounds,
                disableZoomToBounds = workoutHeader.startPosition == workoutHeader.stopPosition,
                startPoint = fullRouteLatLngs.first(),
                endPoint = fullRouteLatLngs.last(),
            )
        }

        return if (activityType.isSlopeSki) {
            val runs = mutableListOf<List<LatLng>>()
            val lifts = mutableListOf<List<LatLng>>()
            SlopeSkiUtils.splitPointsIntoRunsOrLifts(
                SlopeSkiUtils.getRuns(geoPoints),
                geoPoints
            ).forEachIndexed { index, runOrLift ->
                if (index % 2 == 0) {
                    lifts.add(runOrLift)
                } else {
                    runs.add(runOrLift)
                }
            }

            SkiWorkoutMapRouteData(
                routeWithDashLinePoints = routeWithDashLinePoints,
                fullRoute = fullRouteLatLngs,
                bounds = bounds,
                disableZoomToBounds = workoutHeader.startPosition == workoutHeader.stopPosition,
                highlightedRuns = runs,
                nonHighlightedRuns = emptyList(),
                highlightedLifts = lifts,
                nonHighlightedLifts = emptyList()
            )
        } else if (workoutHeader.isMultisport) {
            if (sml != null) {
                val multisportRoutes = sml.multisportRoutes
                val currentMultisportPartRouteIndex = sml.getIndexOfHighlightedRoute(multisportPartActivity)
                val (currentMultisportPartRoute, inactivePartRoutes) = if (currentMultisportPartRouteIndex != -1) {
                    val currentRoute = multisportRoutes[currentMultisportPartRouteIndex]
                    val otherRoutes = multisportRoutes.filterIndexed { idx, _ -> idx != currentMultisportPartRouteIndex }
                    currentRoute to otherRoutes
                } else {
                    fullRouteLatLngs to emptyList()
                }

                val activityTypeChangeLocations = multisportRoutes.activityTypeChangeCoordinates

                MultisportWorkoutMapRouteData(
                    routeWithDashLinePoints = routeWithDashLinePoints,
                    highlightedLapLatLngs = currentMultisportPartRoute,
                    nonHighlightedLatLngs = emptyList(),
                    bounds = bounds,
                    disableZoomToBounds = workoutHeader.startPosition == workoutHeader.stopPosition,
                    inactiveMultisportPartRoutes = inactivePartRoutes,
                    activityTypeChangeLocations = activityTypeChangeLocations,
                    startPoint = currentMultisportPartRoute.first(),
                    endPoint = currentMultisportPartRoute.last(),
                )
            } else {
                generateBasicWorkoutWorkoutMapData()
            }
        } else if (workoutHeader.isHuntingOrFishing && sml != null) {
            val traverseEvents = sml.traverseEvents
            HuntingOrFishingMapRouteData(
                routeWithDashLinePoints = routeWithDashLinePoints,
                highlightedLapLatLngs = fullRouteLatLngs,
                nonHighlightedLatLngs = emptyList(),
                traverseEvents = traverseEvents,
                bounds = bounds,
                disableZoomToBounds = workoutHeader.startPosition == workoutHeader.stopPosition,
                startPoint = fullRouteLatLngs.first(),
                endPoint = fullRouteLatLngs.last()
            )
        } else {
            generateBasicWorkoutWorkoutMapData()
        }
    }

    /**
     * The WorkoutRouteMapData for time windows is based on the full route's data
     * so it can reuse the same LatLngs for less allocations. We still need the time info
     * from the geoPoints those LatLngs are based on, so they're taken as a parameter.
     */
    fun generateWorkoutRouteMapDataForTimeWindow(
        streamData: SmlStreamData?,
        geoPoints: List<WorkoutGeoPoint>,
        fullWorkoutMapRouteData: WorkoutMapRouteData,
        timeWindow: PlaybackTimeWindow
    ): WorkoutMapRouteData {
        return when (timeWindow) {
            is PlaybackFullWorkoutWindow -> fullWorkoutMapRouteData
            is PlaybackLapWindow -> {
                when (fullWorkoutMapRouteData) {
                    is BasicWorkoutMapRouteData -> {
                        val (highlightedLatLng, nonHighlightedLatLngs) = splitHighlightedLap(
                            timeWindow.windowStartMillis,
                            timeWindow.windowEndMillis,
                            fullWorkoutMapRouteData.highlightedLapLatLngs,
                            geoPoints
                        )

                        BasicWorkoutMapRouteData(
                            routeWithDashLinePoints = streamData?.let {
                                MapHelper.filterGeoPointsByActivity(geoPoints, it)?.let { result ->
                                    RouteWithDashLinePoints(result.first, result.second)
                                }
                            },
                            highlightedLapLatLngs = highlightedLatLng,
                            nonHighlightedLatLngs = nonHighlightedLatLngs,
                            bounds = getLatLngBounds(highlightedLatLng) ?: fullWorkoutMapRouteData.bounds,
                            disableZoomToBounds = fullWorkoutMapRouteData.disableZoomToBounds,
                            startPoint = fullWorkoutMapRouteData.startPoint,
                            endPoint = fullWorkoutMapRouteData.endPoint,
                        )
                    }
                    is MultisportWorkoutMapRouteData -> {
                        val (highlightedLatLng, nonHighlightedLatLngs) = splitHighlightedLap(
                            timeWindow.windowStartMillis,
                            timeWindow.windowEndMillis,
                            fullWorkoutMapRouteData.highlightedLapLatLngs,
                            geoPoints
                        )

                        MultisportWorkoutMapRouteData(
                            routeWithDashLinePoints = streamData?.let {
                                MapHelper.filterGeoPointsByActivity(geoPoints, it)?.let { result ->
                                    RouteWithDashLinePoints(result.first, result.second)
                                }
                            },
                            highlightedLapLatLngs = highlightedLatLng,
                            nonHighlightedLatLngs = nonHighlightedLatLngs,
                            bounds = getLatLngBounds(highlightedLatLng) ?: fullWorkoutMapRouteData.bounds,
                            disableZoomToBounds = fullWorkoutMapRouteData.disableZoomToBounds,
                            inactiveMultisportPartRoutes = fullWorkoutMapRouteData.inactiveMultisportPartRoutes,
                            activityTypeChangeLocations = fullWorkoutMapRouteData.activityTypeChangeLocations,
                            startPoint = fullWorkoutMapRouteData.startPoint,
                            endPoint = fullWorkoutMapRouteData.endPoint
                        )
                    }
                    is SkiWorkoutMapRouteData -> {
                        val allRuns = fullWorkoutMapRouteData.highlightedRuns.toMutableList()
                        val allLifts = fullWorkoutMapRouteData.highlightedLifts.toMutableList()

                        // Divide the route to pre-highlight, highlight, and post-highlight
                        // parts without caring about lifts and runs
                        val (highlightedLatLngs, nonHighlightedLatLngs) = splitHighlightedLap(
                            timeWindow.windowStartMillis,
                            timeWindow.windowEndMillis,
                            fullWorkoutMapRouteData.fullRoute,
                            geoPoints
                        )
                        val preHighlightLatLngs = nonHighlightedLatLngs[0]

                        val nonHighlightedRuns = mutableListOf<List<LatLng>>()
                        val nonHighlightedLifts = mutableListOf<List<LatLng>>()
                        val highlightedRuns = mutableListOf<List<LatLng>>()
                        val highlightedLifts = mutableListOf<List<LatLng>>()

                        // Now that we know how many points are before highlight, in the highlight
                        // and after highlight, start dividing lifts and runs to highlights and
                        // non-highlights by first handling the pre-highlighted parts. Pick lifts and
                        // runs alternating until we've reached the amount of points in the
                        // pre-highlight part of full route (subsequent runs and lifts share one latLng,
                        // which is taken into account). If the cutoff point is in the middle
                        // of a lift or run, take the non-highlighted part and return rest to the
                        // non-processed list (keep the last non-highlighted point in the highlighted
                        // part to make sure the route doesn't have gaps in it). Set a flag if the
                        // highlight dividing needs to start from runs instead of lifts. The lift-run
                        // alternating order starts and ends with a lift, so the loop's condition of
                        // cares only about lifts
                        var numLatLngsProcessed = 0
                        var inMiddleOfRun = false
                        while (allLifts.isNotEmpty()) {
                            val lift = allLifts.removeAt(0)
                            if (numLatLngsProcessed + lift.size < preHighlightLatLngs.size) {
                                numLatLngsProcessed += lift.size - 1
                                nonHighlightedLifts.add(lift)
                            } else {
                                val numLatLngsToNotHighlight =
                                    lift.size - (numLatLngsProcessed + lift.size - preHighlightLatLngs.size)
                                nonHighlightedLifts.add(lift.take(numLatLngsToNotHighlight))
                                allLifts.add(0, lift.drop((numLatLngsToNotHighlight - 1).coerceAtLeast(0)))
                                break
                            }

                            if (allRuns.isNotEmpty()) {
                                val run = allRuns.removeAt(0)
                                if (numLatLngsProcessed + run.size < preHighlightLatLngs.size) {
                                    numLatLngsProcessed += run.size - 1
                                    nonHighlightedRuns.add(run)
                                } else {
                                    val numLatLngsToNotHighlight =
                                        run.size - (numLatLngsProcessed + run.size - preHighlightLatLngs.size)
                                    nonHighlightedRuns.add(run.take(numLatLngsToNotHighlight))
                                    allRuns.add(0, run.drop((numLatLngsToNotHighlight - 1).coerceAtLeast(0)))
                                    inMiddleOfRun = true
                                    break
                                }
                            }
                        }

                        // Do the same alternating picking of runs and lifts but with highlights,
                        // start from runs of the flag is set
                        numLatLngsProcessed = 0
                        while (allLifts.isNotEmpty()) {
                            if (!inMiddleOfRun) {
                                val lift = allLifts.removeAt(0)
                                if (numLatLngsProcessed + lift.size < highlightedLatLngs.size) {
                                    numLatLngsProcessed += lift.size - 1
                                    highlightedLifts.add(lift)
                                } else {
                                    val numLatLngsToHighlight =
                                        lift.size - (numLatLngsProcessed + lift.size - highlightedLatLngs.size)
                                    highlightedLifts.add(lift.take(numLatLngsToHighlight))
                                    allLifts.add(0, lift.drop((numLatLngsToHighlight - 1).coerceAtLeast(0)))
                                    break
                                }
                            }

                            if (allRuns.isNotEmpty()) {
                                val run = allRuns.removeAt(0)
                                if (numLatLngsProcessed + run.size < highlightedLatLngs.size) {
                                    numLatLngsProcessed += run.size - 1
                                    highlightedRuns.add(run)
                                    inMiddleOfRun = false
                                } else {
                                    val numLatLngsToHighlight =
                                        run.size - (numLatLngsProcessed + run.size - highlightedLatLngs.size)
                                    highlightedRuns.add(run.take(numLatLngsToHighlight))
                                    allRuns.add(0, run.drop((numLatLngsToHighlight - 1).coerceAtLeast(0)))
                                    break
                                }
                            }
                        }

                        // Rest of latLngs are the post-highlight portion of workout, use as is
                        nonHighlightedRuns.addAll(allRuns)
                        nonHighlightedLifts.addAll(allLifts)

                        SkiWorkoutMapRouteData(
                            routeWithDashLinePoints = null,
                            fullRoute = fullWorkoutMapRouteData.fullRoute,
                            bounds = getLatLngBounds(highlightedLatLngs) ?: fullWorkoutMapRouteData.bounds,
                            disableZoomToBounds = fullWorkoutMapRouteData.disableZoomToBounds,
                            highlightedRuns = highlightedRuns,
                            nonHighlightedRuns = nonHighlightedRuns,
                            highlightedLifts = highlightedLifts,
                            nonHighlightedLifts = nonHighlightedLifts
                        )
                    }
                    is HuntingOrFishingMapRouteData -> {
                        val (highlightedLatLng, nonHighlightedLatLngs) = splitHighlightedLap(
                            timeWindow.windowStartMillis,
                            timeWindow.windowEndMillis,
                            fullWorkoutMapRouteData.highlightedLapLatLngs,
                            geoPoints
                        )

                        HuntingOrFishingMapRouteData(
                            routeWithDashLinePoints = streamData?.let {
                                MapHelper.filterGeoPointsByActivity(geoPoints, it)?.let { result ->
                                    RouteWithDashLinePoints(result.first, result.second)
                                }
                            },
                            highlightedLapLatLngs = highlightedLatLng,
                            nonHighlightedLatLngs = nonHighlightedLatLngs,
                            traverseEvents = fullWorkoutMapRouteData.traverseEvents,
                            bounds = getLatLngBounds(highlightedLatLng) ?: fullWorkoutMapRouteData.bounds,
                            disableZoomToBounds = fullWorkoutMapRouteData.disableZoomToBounds,
                            startPoint = fullWorkoutMapRouteData.startPoint,
                            endPoint = fullWorkoutMapRouteData.endPoint
                        )
                    }
                }
            }
        }
    }
    private fun splitHighlightedLap(
        startTime: Long,
        endTime: Long,
        latLngs: List<LatLng>, // expected to be same as geoPoints but pre-converted to LatLngs
        geoPoints: List<WorkoutGeoPoint>,
    ): Pair<List<LatLng>, List<List<LatLng>>> {
        val startIndex = getGeoPointIndexByWorkoutTime(geoPoints, startTime)
        if (startIndex == latLngs.lastIndex) {
            // Our route data doesn't go this far to the workout, highlight only
            // the last point
            return listOf(latLngs.last()) to listOf(latLngs, emptyList())
        }

        val endIndex = getGeoPointIndexByWorkoutTime(geoPoints, endTime)

        val beforeHighlight = latLngs.subList(0, (startIndex + 1).coerceAtMost(latLngs.size))
        val highlight = latLngs.subList(startIndex, (endIndex + 1).coerceAtMost(latLngs.size))
        val afterHighlight = latLngs.subList(endIndex, latLngs.size)

        return highlight to listOf(beforeHighlight, afterHighlight)
    }

    private fun getRouteLatLngs(
        geoPoints: List<WorkoutGeoPoint>,
        singleLocation: LatLng?
    ): List<LatLng> = when {
        geoPoints.isNotEmpty() -> {
            geoPoints.map { LatLng(it.latitude, it.longitude) }
        }

        singleLocation != null -> {
            listOf(singleLocation)
        }

        else -> emptyList()
    }

    private fun getLatLngBounds(latLngs: List<LatLng>): LatLngBounds? = when {
        latLngs.isNotEmpty() -> {
            with(LatLngBounds.builder()) {
                for (latLng in latLngs) {
                    include(latLng)
                }
                build()
            }
        }
        else -> null
    }

    private fun getGeoPointIndexByWorkoutTime(
        geoPoints: List<WorkoutGeoPoint>,
        millisInWorkout: Long
    ): Int {
        val index = with(geoPoints.indexOfFirst { it.millisecondsInWorkout >= millisInWorkout }) {
            if (this == -1) geoPoints.lastIndex else this
        }
        return index
    }
}
