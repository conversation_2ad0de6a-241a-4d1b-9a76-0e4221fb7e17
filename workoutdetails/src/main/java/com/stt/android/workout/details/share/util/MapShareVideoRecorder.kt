package com.stt.android.workout.details.share.util

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaCodec
import android.media.MediaRecorder
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.view.PixelCopy
import android.view.SurfaceView
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.core.graphics.createBitmap
import androidx.core.view.children
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class MapShareVideoRecorder @Inject constructor(
    @ApplicationContext context: Context,
    private val dispatchers: CoroutinesDispatchers,
) {

    private val mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        MediaRecorder(context)
    } else {
        @Suppress("DEPRECATION")
        MediaRecorder()
    }

    private val surface = MediaCodec.createPersistentInputSurface()

    private val surfaceMutex = Mutex()

    private val screenshotHandlerThread = HandlerThread("screenshot")
    private val screenshotHandler: Handler

    private var recodingJob: Job? = null

    private var isRecording = false

    private val outputFile = File(context.getExternalFilesDir(null), FILE_NAME)

    var errorListener: MediaRecorder.OnErrorListener? = null
        set(value) {
            field = value
            mediaRecorder.setOnErrorListener(value)
        }

    init {
        screenshotHandlerThread.start()
        screenshotHandler = Handler(screenshotHandlerThread.looper)
    }

    fun startRecording(window: Window, coroutineScope: CoroutineScope): Boolean {
        if (isRecording) return false

        val width = window.decorView.width
        val height = window.decorView.height
        if (width == 0 || height == 0) return false
        val surfaceView = findSurfaceView(window.decorView) ?: return false

        isRecording = true
        recodingJob = coroutineScope.launch {
            runSuspendCatching {
                withContext(dispatchers.io) {
                    outputFile.delete()
                }
                val (width, height) = calculateVideoDimensions(width, height)
                mediaRecorder.apply {
                    reset()
                    setInputSurface(<EMAIL>)
                    setVideoSource(MediaRecorder.VideoSource.SURFACE)
                    setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                    setVideoEncoder(MediaRecorder.VideoEncoder.H264)
                    setVideoFrameRate(FRAME_RATE)
                    setVideoEncodingBitRate(BIT_RATE)
                    setOutputFile(outputFile)
                    setVideoSize(width, height)
                    prepare()
                    start()
                }
                while (isRecording) {
                    launch(dispatchers.io) {
                        drawFrame(surfaceView, window, width, height)
                    }
                    delay(FRAME_INTERVAL)
                }
            }
        }
        return true
    }

    fun stopRecording(
        coroutineScope: CoroutineScope,
        release: Boolean = false,
        block: (suspend (File?) -> Unit)? = null,
    ): Boolean {
        if (!isRecording) return false

        isRecording = false
        coroutineScope.launch {
            try {
                delay(FRAME_INTERVAL)
                recodingJob?.cancelAndJoin()
            } finally {
                recodingJob = null
                // https://stackoverflow.com/questions/16221866/mediarecorder-failed-when-i-stop-the-recording
                try {
                    mediaRecorder.stop()
                    block?.invoke(outputFile)
                } catch (_: RuntimeException) {
                    block?.invoke(null)
                }
                if (release) {
                    mediaRecorder.release()
                    surface.release()
                    screenshotHandlerThread.quitSafely()
                }
            }
        }
        return true
    }

    private suspend fun drawFrame(
        surfaceView: SurfaceView,
        window: Window,
        width: Int,
        height: Int,
    ) = coroutineScope {
        val surfaceBitmap = createBitmap(width, height)
        val windowBitmap = createBitmap(width, height)
        try {
            val surfaceTask = async { copySurface(surfaceView, surfaceBitmap) }
            val windowTask = async { copyWindow(window, windowBitmap) }
            if (surfaceTask.await() == true && windowTask.await() == true) {
                surfaceMutex.withLock {
                    val canvas = surface.lockHardwareCanvas()
                    canvas.drawBitmap(surfaceBitmap, 0f, 0f, null)
                    canvas.drawBitmap(windowBitmap, 0f, 0f, null)
                    surface.unlockCanvasAndPost(canvas)
                }
            }
        } finally {
            surfaceBitmap.recycle()
            windowBitmap.recycle()
        }
    }

    private suspend fun copySurface(
        surfaceView: SurfaceView,
        bitmap: Bitmap,
    ) = suspendCoroutine { continuation ->
        runCatching {
            PixelCopy.request(
                surfaceView,
                bitmap,
                {
                    Timber.d("Copy surface: $it")
                    continuation.resume(it == PixelCopy.SUCCESS)
                },
                screenshotHandler,
            )
        }.onFailure {
            Timber.d(it, "Failed to copy surface")
            continuation.resume(false)
        }
    }

    private suspend fun copyWindow(
        window: Window,
        bitmap: Bitmap,
    ) = suspendCoroutine { continuation ->
        runCatching {
            PixelCopy.request(
                window,
                bitmap,
                {
                    Timber.d("Copy window: $it")
                    continuation.resume(it == PixelCopy.SUCCESS)
                },
                screenshotHandler,
            )
        }.onFailure {
            Timber.d(it, "Failed to copy window")
            continuation.resume(false)
        }
    }

    private fun findSurfaceView(view: View): SurfaceView? {
        if (view is SurfaceView) return view

        if (view is ViewGroup) {
            view.children.forEach {
                val surfaceView = findSurfaceView(it)
                if (surfaceView != null) {
                    return surfaceView
                }
            }
        }

        return null
    }

    private fun calculateVideoDimensions(width: Int, height: Int): Pair<Int, Int> {
        return if (width > height) {
            (width * SHORT_DIMEN.toFloat() / height).toInt() / DIMEN_ALIGN * DIMEN_ALIGN to SHORT_DIMEN
        } else {
            SHORT_DIMEN to (height * SHORT_DIMEN.toFloat() / width).toInt() / DIMEN_ALIGN * DIMEN_ALIGN
        }
    }

    companion object {
        private const val FRAME_RATE = 30
        private const val FRAME_INTERVAL = 33L
        private const val BIT_RATE = 2500_000
        private const val SHORT_DIMEN = 720
        private const val DIMEN_ALIGN = 16
        private const val FILE_NAME = "map_share_video.mp4"
    }
}
