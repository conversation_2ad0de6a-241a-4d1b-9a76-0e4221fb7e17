package com.stt.android.workout.details.share

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.media.MediaRecorder
import android.os.Build
import android.os.Bundle
import android.text.style.RelativeSizeSpan
import android.view.View
import android.view.View.MeasureSpec
import android.view.WindowManager
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.content.res.ResourcesCompat
import androidx.core.net.toUri
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.gms.maps.model.LatLng
import com.stt.android.FontRefs
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.GraphType
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMapView
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.newLatLngBounds
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.multimedia.video.ExoPlayerHelper
import com.stt.android.ui.fragments.workout.WorkoutLineChartShare
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.map.RouteMarkerHelper
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CustomFontStyleSpan
import com.stt.android.videoencode.MergeAudioAndVideo
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.ActivityWorkoutMapPlaybackBinding
import com.stt.android.workout.details.graphanalysis.playback.Workout3DPlaybackCameraConfig
import com.stt.android.workout.details.share.util.MapShareVideoRecorder
import com.stt.android.workout.details.share.util.VideoFileUtil
import com.stt.android.workout.details.share.video.VideoShareInfoView
import com.stt.android.workout.details.share.video.toMapType
import com.stt.android.workouts.details.values.isSuuntoRun
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import kotlin.math.max
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@AndroidEntryPoint
class WorkoutMapPlaybackActivity : AppCompatActivity(), SimpleDialogFragment.Callback {

    @Inject
    internal lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var videoRecorder: MapShareVideoRecorder

    @Inject
    lateinit var dispatchers: CoroutinesDispatchers

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    private lateinit var binding: ActivityWorkoutMapPlaybackBinding
    private lateinit var mapView: SuuntoMapView
    private lateinit var graphView: WorkoutLineChartShare
    private lateinit var bitmapDescriptorFactory: SuuntoBitmapDescriptorFactory

    private lateinit var audioPlayer: ExoPlayer

    private val viewModel: WorkoutMapPlaybackViewModel by viewModels()

    private val valueSpanFactory: TextFormatter.SpanFactory by lazy {
        val font = ResourcesCompat.getFont(this, FontRefs.WORKOUT_VALUE_FONT_REF)
        TextFormatter.SpanFactory {
            if (font != null) {
                listOf(CustomFontStyleSpan(font))
            } else {
                emptyList()
            }
        }
    }

    private val textSpanFactory: TextFormatter.SpanFactory by lazy {
        val font = ResourcesCompat.getFont(this, FontRefs.WORKOUT_UNIT_FONT_REF)
        TextFormatter.SpanFactory {
            if (font != null) {
                listOf(CustomFontStyleSpan(font), RelativeSizeSpan(0.8f))
            } else {
                listOf(RelativeSizeSpan(0.8f))
            }
        }
    }

    private val markers = mutableListOf<SuuntoMarker>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
        )
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        videoRecorder.errorListener = MediaRecorder.OnErrorListener { _, what, _ ->
            if (what == MediaRecorder.MEDIA_ERROR_SERVER_DIED || what == MediaRecorder.MEDIA_RECORDER_ERROR_UNKNOWN) {
                viewModel.onRecordError()
            }
        }

        audioPlayer = ExoPlayerHelper.createPlayer(this, "").apply {
            playWhenReady = true
            repeatMode = Player.REPEAT_MODE_ALL
        }

        bitmapDescriptorFactory = SuuntoBitmapDescriptorFactory(this)

        binding = ActivityWorkoutMapPlaybackBinding.inflate(layoutInflater)
        graphView = binding.chartContainer.findViewById(BR.id.graphView)
        setContentView(binding.root)

        binding.composeView.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
            updateBackgroundScrim()
        }
        binding.chartContainer.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
            updateBackgroundScrim()
        }
        val mapOptions = SuuntoMapOptions(
            mapType = viewModel.mapType.toMapType.name,
            map3dMode = true,
            enable3dLocation = true,
        )
        mapView = SuuntoMapView(this, mapOptions)
        // WorkoutMapView's state restoration requires that an ID is set. Having duplicate ID in the
        // hierarchy is allowed and doesn't break getViewById, so just use the container ID
        mapView.id = binding.mapContainer.id
        binding.mapContainer.addView(mapView)
        mapView.onCreate(savedInstanceState)
        mapView.clicksDisabled = true
        mapView.getMapAsync { map ->
            map.setCompassEnabled(false)
            map.removeScaleBar()
        }

        binding.chartContainer.infoModelFormatter = infoModelFormatter
        binding.chartContainer.unit
        graphView.progressive = true
        graphView.ratio = 0f

        binding.composeView.setContentWithM3Theme {
            val infoData by viewModel.infoData.collectAsState()
            val infoRowAnimation by viewModel.infoRowAnimation.collectAsState()
            infoData?.let { data ->
                VideoShareInfoView(
                    data = data,
                    rowDataAnimation = infoRowAnimation,
                    options = viewModel.dataOptions,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { _, insets ->
            val systemBarsInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            updateMapPadding(systemBarsInsets.bottom)
            binding.dataContainer.setPadding(
                systemBarsInsets.left,
                systemBarsInsets.top,
                systemBarsInsets.right,
                systemBarsInsets.bottom + resources.getDimensionPixelSize(BR.dimen.size_spacing_xlarge),
            )
            insets
        }

        binding.playFab.setOnClickListener {
            viewModel.startPlayback(true)
        }

        binding.shareVideoBtn.setOnClickListener {
            viewModel.sharingFile.value?.let { shareVideo(it) }
        }

        launchOnStart {
            viewModel.cameraBounds.filterNotNull().collectLatest {
                mapView.getMapAsync { map ->
                    val padding = resources.getDimensionPixelSize(BR.dimen.size_spacing_xxlarge)
                    map.moveCamera(newLatLngBounds(it, padding))
                }
            }
        }

        launchOnStart {
            viewModel.startEndPoints.filterNotNull().collectLatest { (start, end) ->
                mapView.getMapAsync { map ->
                    val context = applicationContext
                    RouteMarkerHelper.drawStartPoint(context, map, start.latLng, false, true)
                    RouteMarkerHelper.drawEndPoint(context, map, end.latLng, true)
                }
            }
        }

        launchOnStart {
            viewModel.colorTrack.filterNotNull().collectLatest {
                mapView.getMapAsync { map ->
                    val lineWidth =
                        resources.getDimensionPixelSize(R.dimen.map_playback_color_track_line_width)
                    map.addColorTrack(it, lineWidth.toDouble())
                }
            }
        }

        launchOnStart {
            viewModel.markers.collectLatest {
                mapView.getMapAsync { map ->
                    map.updateMarkers(it)
                }
            }
        }

        launchOnStart {
            viewModel.workoutDataToSummaryExtension.filterNotNull().collectLatest { (data, summaryExtension) ->
                val graphType = GraphType.Summary(SummaryGraph.ALTITUDE)
                val measurementUnit = userSettingsController.settings.measurementUnit
                binding.chartContainer.unit = WorkoutAnalysisHelper.getGraphUnitStringRes(
                    infoModelFormatter,
                    graphType,
                    measurementUnit,
                    data.workoutHeader.activityType.isSwimming,
                )?.let(::getString).orEmpty()
                lifecycleScope.launch {
                    graphView.drawGraph(
                        summaryExtension.isSuuntoRun(),
                        graphType,
                        data.geoPoints,
                        data.workoutHeader,
                        data.sml,
                        measurementUnit,
                    ).await()
                }
            }
        }

        launchOnStart {
            viewModel.cameraConfig.filterNotNull().collectLatest { cameraConfig ->
                if (cameraConfig is Workout3DPlaybackCameraConfig) {
                    update3d(cameraConfig)
                }
            }
        }

        fun formatValue(item: SummaryItem, value: Any) = TextFormatter.formatWorkoutValue(
            resources,
            infoModelFormatter.formatValue(item, value).let {
                when (item) {
                    SummaryItem.AVGPACE -> it.copy(label = getString(BR.string.pace))
                    SummaryItem.AVGSPEED -> it.copy(label = getString(CR.string.speed))
                    else -> it
                }
            },
            valueSpanFactory,
            textSpanFactory,
            true,
        )

        /**
         * Avoid view jitter
         */
        fun View.measureAndSetMinimumWidth() {
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED).let { measure(it, it) }
            minimumWidth = max(width, measuredWidth)
        }

        launchOnStart {
            viewModel.chartProgress.collectLatest { (ratio, visible) ->
                binding.bottomGroup.isVisible = visible
                if (visible) {
                    graphView.ratio = ratio
                    viewModel.workoutDataToSummaryExtension.value?.let { (data, _) ->
                        val workoutHeader = data.workoutHeader
                        val targetDistance = workoutHeader.totalDistance * ratio
                        binding.firstData.text = formatValue(SummaryItem.DISTANCE, targetDistance)
                        binding.firstData.measureAndSetMinimumWidth()
                        val targetTime = workoutHeader.totalTime * ratio
                        binding.secondData.text = formatValue(SummaryItem.DURATION, targetTime)
                        binding.secondData.measureAndSetMinimumWidth()
                        val targetMillis = targetTime * 1000
                        data.speedPoints.lastOrNull { it.time <= targetMillis }?.let {
                            binding.thirdData.text =
                                formatValue(viewModel.thirdSummaryItem, it.value)
                            binding.thirdData.measureAndSetMinimumWidth()
                        }
                    }
                }
            }
        }

        launchOnStart {
            viewModel.playbackState.collect { state ->
                when (state?.resumed) {
                    true -> {
                        viewModel.setSharingFile(null)
                        binding.playFab.isVisible = false
                        videoRecorder.startRecording(window, lifecycleScope)
                        audioPlayer.setMediaItem(MediaItem.fromUri("asset:///$AUDIO_FILENAME".toUri()))
                        audioPlayer.prepare()
                    }

                    false -> {
                        binding.playFab.isVisible = true
                        videoRecorder.stopRecording(lifecycleScope) { file ->
                            if (state.pauseReason == null) {
                                if (file != null) {
                                    mergeAudioAndVideo(file).fold({
                                        if (viewModel.sharePopup) {
                                            shareVideo(it)
                                        } else {
                                            viewModel.setSharingFile(it)
                                        }
                                    }, {
                                        Timber.w(it, "Merge audio and video failed")
                                    })
                                } else {
                                    showErrorDialog()
                                }
                            }
                        }
                        audioPlayer.stop()
                    }

                    else -> Unit
                }
            }
        }

        launchOnStart {
            viewModel.autoPlayback.collectLatest {
                if (it) {
                    binding.loading.isVisible = false
                    viewModel.startPlayback(false)
                }
            }
        }

        launchOnStart {
            viewModel.playbackError.filterNotNull().collectLatest {
                showErrorDialog()
            }
        }

        launchOnStart {
            viewModel.sharingFile.collectLatest {
                binding.shareVideoBtn.isVisible = it != null
            }
        }
    }

    private fun updateBackgroundScrim() {
        val height = binding.dataContainer.height.toFloat()
        if (height <= 0f) return

        val drawable = (binding.dataContainer.background as? GradientDrawable) ?: GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(SCRIM_COLOR, SCRIM_COLOR),
        )
        val colors = mutableListOf<Int>()
        val positions = mutableListOf<Float>()
        colors.add(SCRIM_COLOR)
        positions.add(0f)
        val topScrimOffset = binding.composeView.bottom.toFloat() / height * SCRIM_SCALING_FACTOR
        if (0f < topScrimOffset && topScrimOffset < 1f) {
            colors.add(Color.TRANSPARENT)
            positions.add(topScrimOffset)
        }
        if (binding.chartContainer.isVisible) {
            val bottomScrimOffset =
                1f - (1f - binding.chartContainer.top.toFloat() / height) * SCRIM_SCALING_FACTOR
            if (topScrimOffset < bottomScrimOffset && bottomScrimOffset < 1f) {
                colors.add(Color.TRANSPARENT)
                positions.add(bottomScrimOffset)
            }
            colors.add(SCRIM_COLOR)
            positions.add(1f)
        } else {
            colors.add(Color.TRANSPARENT)
            positions.add(1f)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            drawable.setColors(colors.toIntArray(), positions.toFloatArray())
        } else {
            drawable.colors = colors.toIntArray()
        }
        binding.dataContainer.background = drawable
    }

    private fun updateMapPadding(paddingBottom: Int) {
        mapView.getMapAsync { map ->
            MapHelper.updateMapPaddingWithDefaults(
                resources = resources,
                map = map,
                paddingTop = 0,
                paddingBottom = paddingBottom,
                compassTopMargin = 0,
                mapboxLogoLeftMargin = resources.getDimensionPixelSize(BR.dimen.size_spacing_xsmall),
                mapCreditTextView = binding.credit,
                addCreditHeightToBottomPadding = true
            )
        }
    }

    private fun update3d(config: Workout3DPlaybackCameraConfig) {
        config.cameraPosition ?: return

        mapView.getMapAsync { map ->
            map.update3dLocation(
                LatLng(
                    config.markerPosition.latitude,
                    config.markerPosition.longitude,
                ),
                config.markerAltitude,
            )
            map.moveCamera(
                SuuntoFreeCameraUpdate(
                    config.markerPosition,
                    config.cameraPosition,
                    config.cameraAltitude,
                    config.cameraPitch,
                    config.cameraBearing
                )
            )
        }
    }

    private fun SuuntoMap.updateMarkers(descriptors: List<ValueDescriptor>) {
        markers.forEach { it.remove() }
        markers.clear()
        markers.addAll(batchUpdate {
            descriptors.mapNotNull { addMarker(it) }
        })
    }

    private fun SuuntoMap.addMarker(descriptor: ValueDescriptor): SuuntoMarker? {
        val descRes = if (descriptor.isMaximum)
            R.string.workout_map_playback_flag_max
        else R.string.workout_map_playback_flag_min
        return addMarker(
            SuuntoMarkerOptions()
                .anchor(0f, 1f)
                .icon(
                    bitmapDescriptorFactory.forFlag(
                        getString(descRes).uppercase(),
                        descriptor.value,
                    )
                )
                .position(descriptor.geoPoint.latLng)
        )
    }

    private suspend fun mergeAudioAndVideo(file: File) = withContext(dispatchers.io) {
        val outputFile = viewModel.getOutputFile()
        val result = runCatching {
            outputFile.apply {
                MergeAudioAndVideo.merge(
                    file.absolutePath,
                    assets.openFd(AUDIO_FILENAME),
                    this.absolutePath,
                )
            }
        }.onFailure {
            outputFile.deleteSafely()
        }
        file.deleteSafely()
        result
    }

    private fun shareVideo(file: File) {
        VideoFileUtil.getVideoShareUri(this, file).let { uri ->
            if (workoutShareHelper.hasCustomIntentHandling()) {
                WorkoutShareTargetListDialogFragment.newInstanceForVideoSharing(uri)
                    .show(supportFragmentManager, TAG_SHARE_DIALOG)
            } else {
                workoutShareHelper.sendImplicitVideoShareIntent(
                    this,
                    uri,
                    SportieShareType.VIDEO_3D,
                )
            }
        }
    }

    private fun showErrorDialog() {
        try {
            if (supportFragmentManager.findFragmentByTag(TAG_ERROR_DIALOG) == null) {
                SimpleDialogFragment.newInstance(
                    getString(BR.string.error_generic_try_again),
                    positiveButtonText = getString(android.R.string.ok),
                    cancellable = false,
                ).show(supportFragmentManager, TAG_ERROR_DIALOG)
            }
        } catch (_: IllegalStateException) {
        }
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        finish()
    }

    override fun onDialogDismissed(tag: String?) {
        finish()
    }

    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView.onResume()
    }

    override fun onPause() {
        if (isFinishing) {
            videoRecorder.stopRecording(lifecycleScope, true)
        }
        viewModel.pausePlayback()
        mapView.onPause()
        super.onPause()
    }

    override fun onStop() {
        mapView.onStop()
        super.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }

    @Deprecated("Deprecated in Java")
    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onDestroy() {
        audioPlayer.release()
        mapView.onDestroy()
        super.onDestroy()
    }

    private fun launchOnStart(block: suspend CoroutineScope.() -> Unit) = lifecycleScope.launch {
        repeatOnLifecycle(Lifecycle.State.STARTED, block)
    }

    private fun File.deleteSafely() = runCatching { delete() }

    companion object {
        private const val TAG_ERROR_DIALOG = "error_dialog"
        private const val TAG_SHARE_DIALOG = "share_dialog"
        private const val AUDIO_FILENAME = "video_share.aac"
        private const val SCRIM_SCALING_FACTOR = 1.2f
        private val SCRIM_COLOR = Color.argb(0.4f, 0f, 0f, 0f)

        fun newStartIntent(
            context: Context,
            workoutId: Int,
            sharePopup: Boolean,
        ) = Intent(context, WorkoutMapPlaybackActivity::class.java).apply {
            putExtra(WorkoutMapPlaybackViewModel.EXTRA_WORKOUT_ID, workoutId)
            putExtra(WorkoutMapPlaybackViewModel.EXTRA_SHARE_POPUP, sharePopup)
        }
    }
}
