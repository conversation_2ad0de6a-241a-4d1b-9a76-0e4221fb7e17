package com.stt.android.workout.details

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.isNotEmpty
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.data.TimeUtils.epochToLocalZonedDateTime
import com.stt.android.domain.workout.SharingOption
import com.stt.android.utils.WindowsSubsystemForAndroidUtils
import com.stt.android.utils.addRequiresPremiumNote
import com.stt.android.workout.details.databinding.WorkoutDetailsToolbarNewBinding
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Date
import com.stt.android.R as BaseR

class WorkoutDetailToolbar
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : Toolbar(context, attrs, defStyleAttr) {
    private lateinit var title: TextView
    private lateinit var subtitle: TextView
    private lateinit var shareIcon: ImageView
    private lateinit var titleContainer: View
    private lateinit var barTitle: View
    private lateinit var toolbarData: ToolbarData

    init {
        init(context)
    }

    private fun init(context: Context) {
        val binding =
            WorkoutDetailsToolbarNewBinding.inflate(
                LayoutInflater.from(context),
                this,
                true
            )
        title = binding.title
        subtitle = binding.subtitle
        shareIcon = binding.shareIcon
        titleContainer = binding.toolbarTitleContainer
        barTitle = binding.barTitle
    }

    private fun setSubtitleText(workoutStartTime: Long, locationPlaceName: String?) {
        // time format error on some variants of Android 12. for example: ├minute: 12┤
        val dateTimeAsString = if (Build.VERSION.SDK_INT == Build.VERSION_CODES.S) {
            val dateTimeFormatter = SimpleDateFormat.getDateTimeInstance(
                SimpleDateFormat.SHORT,
                SimpleDateFormat.SHORT
            )
            dateTimeFormatter.format(Date(workoutStartTime))
        } else {
            val dateTimeFormatter = DateTimeFormatter.ofLocalizedDateTime(
                FormatStyle.SHORT,
                FormatStyle.SHORT
            )
            epochToLocalZonedDateTime(workoutStartTime).format(dateTimeFormatter)
        }
        subtitle.text = if (!locationPlaceName.isNullOrEmpty()) {
            dateTimeAsString.plus(" · ").plus(locationPlaceName)
        } else {
            dateTimeAsString
        }
    }

    private fun setSharingVisibility(sharingFlags: Int, isOwnWorkout: Boolean) {
        if (isOwnWorkout) {
            when {
                sharingFlags.contains(SharingOption.EVERYONE) -> {
                    shareIcon.isVisible = false
                }

                sharingFlags.contains(SharingOption.FOLLOWERS) -> {
                    shareIcon.isVisible = true
                    shareIcon.setImageResource(BaseR.drawable.ic_followers_12)
                }

                else -> {
                    shareIcon.isVisible = true
                    shareIcon.setImageResource(BaseR.drawable.ic_privacy_12)
                }
            }
        } else {
            shareIcon.isVisible = false
        }
    }

    fun setData(toolbarData: ToolbarData, hideMenuActions: Boolean) {
        if (this::toolbarData.isInitialized &&
            this.toolbarData == toolbarData &&
            menu.isNotEmpty()
        ) {
            return
        }
        this.toolbarData = toolbarData
        menu.clear()
        title.text = toolbarData.name
        setSubtitleText(toolbarData.workoutStartTime, toolbarData.locationPlaceName)
        setSharingVisibility(toolbarData.sharingFlags, toolbarData.isOwnWorkout)
        setNavigationIcon(SuuntoIcons.ActionBack.resource)
        titleContainer.setOnClickListener { toolbarData.onTitleClicked() }

        if (hideMenuActions) return

        setOnMenuItemClickListener { item: MenuItem ->
            toolbarData.onMenuItemClicked.invoke(
                item.itemId
            )
        }

        val isOwnWorkoutOrDebugBuild = toolbarData.isOwnWorkout  || BuildConfig.DEBUG
        inflateMenu(R.menu.additional_workout_detail_toolbar)
        if (isOwnWorkoutOrDebugBuild) {
            inflateMenu(BaseR.menu.own_workout_detail_toolbar)
        }

        menu.findItem(R.id.exportWorkout).isVisible = isOwnWorkoutOrDebugBuild && toolbarData.hasRoute
        menu.findItem(R.id.report).isVisible = !toolbarData.isOwnWorkout
        menu.findItem(BaseR.id.edit)?.isVisible = toolbarData.isOwnWorkout
        menu.findItem(BaseR.id.downloadJson)?.apply {
            // Visible if:
            // 1) set to visible in xml (makes sure it is hidden for Sports Tracker),
            // AND 2) is own workout for a field tester OR any workout for debug build.
            isVisible = isVisible and ((toolbarData.isFieldTester and toolbarData.isOwnWorkout) or BuildConfig.DEBUG)
        }

        if (!toolbarData.hasRoute) {
            menu.findItem(R.id.followRoute).isVisible = false
            menu.findItem(R.id.ghostTarget).isVisible = false
            menu.findItem(R.id.saveRoute).isVisible = false
            menu.findItem(R.id.exportRoute).isVisible = false
            menu.findItem(R.id.exportKmlRoute).isVisible = false
        }
        if (WindowsSubsystemForAndroidUtils.isWindowsSubsystemForAndroid) {
            menu.findItem(R.id.followRoute).isVisible = false
            menu.findItem(R.id.ghostTarget).isVisible = false
        }

        if (toolbarData.showPremiumRequiredNotes) {
            addPremiumRequiredNote(menu.findItem(R.id.followRoute))
            addPremiumRequiredNote(menu.findItem(R.id.ghostTarget))
            addPremiumRequiredNote(menu.findItem(R.id.saveRoute))
        }
    }

    private fun addPremiumRequiredNote(menuItem: MenuItem) {
        menuItem.title = menuItem.title.addRequiresPremiumNote(context)
    }

    // hide bar with user info from the top of page when select target workout
    fun hideBarInfo() {
        subtitle.visibility = View.GONE
        title.visibility = View.GONE
        shareIcon.visibility = View.GONE
        barTitle.visibility = View.VISIBLE
        titleContainer.isClickable = false
        setNavigationIcon(SuuntoIcons.ActionBack.resource)
        navigationIcon?.setTint(ContextCompat.getColor(context, com.stt.android.core.R.color.white))
        setBackgroundColor(ContextCompat.getColor(context, com.stt.android.core.R.color.near_black))
        menu.clear()
    }
}

@BindingAdapter(value = ["userInfo", "hideMenuActions", "hideBarInfo"], requireAll = true)
fun workoutDetailsToolbar(
    toolbar: WorkoutDetailToolbar,
    toolbarData: ViewState<ToolbarData?>?,
    hideMenuActions: Boolean,
    hideBarInfo: Boolean,
) {
    val data = toolbarData?.data ?: return
    if (toolbarData.isLoaded()) {
        toolbar.setData(data, hideMenuActions)
    }
    if (hideBarInfo) {
        toolbar.hideBarInfo()
    }
}

private fun Int.contains(sharingOption: SharingOption): Boolean {
    return this and sharingOption.backendId != 0
}
