package com.stt.android.workout.details.summary

import android.content.Context
import android.text.format.DateUtils
import com.airbnb.epoxy.EpoxyHolder
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.github.mikephil.charting.charts.ScatterChart
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.CombinedData
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.ScatterData
import com.github.mikephil.charting.data.ScatterDataSet
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.interfaces.datasets.IScatterDataSet
import com.github.mikephil.charting.utils.EntryXComparator
import com.stt.android.ThemeColors
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.exceptions.InternalDataException
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.RecentWorkoutSummary
import com.stt.android.workout.details.RecentWorkoutSummaryLineData
import com.stt.android.workout.details.summary.DefaultRecentWorkoutSummaryDataLoader.Companion.SUMMARY_PERIOD_IN_DAYS
import com.stt.android.workout.details.summary.DefaultRecentWorkoutSummaryDataLoader.Companion.calculateSummaryPeriod
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import timber.log.Timber
import java.lang.ref.WeakReference

abstract class BaseRecentWorkoutSummaryModel<T : EpoxyHolder> : EpoxyModelWithHolder<T>() {
    protected open var highlightColor: Int = 0
    protected open var color: Int = 0
    protected lateinit var viewHolder: WeakReference<T>

    protected open fun initColor(context: Context) {
        this.color = ThemeColors.primaryTextColor(context)
        this.highlightColor = ThemeColors.resolveColor(context, android.R.attr.colorAccent)
    }

    protected fun initViewHolder(holder: T) {
        if (::viewHolder.isInitialized) viewHolder.clear()
        viewHolder = WeakReference(holder)
    }

    protected suspend fun createSummary(
        recentWorkoutSummary: RecentWorkoutSummary,
        workoutHeaderController: WorkoutHeaderController,
        infoModelFormatter: InfoModelFormatter
    ): RecentWorkoutSummaryLineData? =
        withContext(IO) {
            val referenceWorkout = recentWorkoutSummary.referenceWorkout
            try {
                val referenceTime: Long = referenceWorkout.startTime
                val activityType: ActivityType = referenceWorkout.activityType
                val summaryPeriod = calculateSummaryPeriod(referenceTime)
                val workoutHeaderCount: Int

                val recentWorkoutHeaders: List<WorkoutHeader> =
                    workoutHeaderController.findNotDeletedWorkoutHeaders(
                        referenceWorkout.username,
                        activityType,
                        summaryPeriod.first,
                        summaryPeriod.second
                    ).reversed()
                workoutHeaderCount = recentWorkoutHeaders.size

                if (workoutHeaderCount == 0) {
                    null
                } else {
                    var lastWorkoutDay = summaryPeriod.first / DateUtils.DAY_IN_MILLIS
                    var xIndex = 1
                    var highLightXIndex = -1
                    val maxSize = SUMMARY_PERIOD_IN_DAYS + workoutHeaderCount
                    val colors = ArrayList<Int>(maxSize)
                    val measurementUnit: MeasurementUnit = infoModelFormatter.unit
                    val shouldAddCadence = ActivityType.CYCLING == activityType ||
                        ActivityType.MOUNTAIN_BIKING == activityType ||
                        ActivityType.GRAVEL_CYCLING == activityType

                    val durationEntries = ArrayList<Entry>(maxSize)
                    val durationBarEntries = ArrayList<BarEntry>(maxSize)
                    val distanceEntries = ArrayList<Entry>(maxSize)
                    val distanceBarEntries = ArrayList<BarEntry>(maxSize)
                    val speedEntries = ArrayList<Entry>(maxSize)
                    val speedBarEntries = ArrayList<BarEntry>(maxSize)
                    val paceEntries = ArrayList<Entry>(maxSize)
                    val paceBarEntries = ArrayList<BarEntry>(maxSize)
                    val energyEntries = ArrayList<Entry>(maxSize)
                    val energyBarEntries = ArrayList<BarEntry>(maxSize)
                    val averageHeartRateEntries = ArrayList<Entry>(maxSize)
                    val averageHeartRateBarEntries = ArrayList<BarEntry>(maxSize)
                    val averageCadenceEntries = ArrayList<Entry>(maxSize)
                    val averageCadenceBarEntries = ArrayList<BarEntry>(maxSize)

                    for (i in 0 until workoutHeaderCount) {
                        val recentWorkoutHeader = recentWorkoutHeaders[i]
                        val workoutStartTime = recentWorkoutHeader.startTime
                        val currentWorkoutDay = workoutStartTime / DateUtils.DAY_IN_MILLIS
                        val dayGap = currentWorkoutDay - lastWorkoutDay
                        if (dayGap > 1) xIndex += dayGap.toInt() - 1

                        val duration = recentWorkoutHeader.totalTime.toFloat()
                        addEntry(
                            duration,
                            xIndex,
                            recentWorkoutHeader,
                            durationEntries,
                            durationBarEntries
                        )

                        val distance = recentWorkoutHeader.totalDistance.toFloat()
                        addEntry(
                            distance,
                            xIndex,
                            recentWorkoutHeader,
                            distanceEntries,
                            distanceBarEntries
                        )

                        val speed = recentWorkoutHeader.avgSpeed.toFloat()
                        addEntry(
                            speed,
                            xIndex,
                            recentWorkoutHeader,
                            speedEntries,
                            speedBarEntries
                        )

                        val pace = measurementUnit.toPaceUnit(speed.toDouble()).toFloat()
                        addEntry(
                            pace,
                            xIndex,
                            recentWorkoutHeader,
                            paceEntries,
                            paceBarEntries
                        )

                        val energy = recentWorkoutHeader.energyConsumption.toFloat()
                        addEntry(
                            energy,
                            xIndex,
                            recentWorkoutHeader,
                            energyEntries,
                            energyBarEntries
                        )

                        val averageHeartRate = recentWorkoutHeader.heartRateAverage.toFloat()
                        addEntry(
                            averageHeartRate,
                            xIndex,
                            recentWorkoutHeader,
                            averageHeartRateEntries,
                            averageHeartRateBarEntries
                        )

                        if (shouldAddCadence) {
                            val averageCadence = recentWorkoutHeader.averageCadence.toFloat()
                            addEntry(
                                averageCadence,
                                xIndex,
                                recentWorkoutHeader,
                                averageCadenceEntries,
                                averageCadenceBarEntries
                            )
                        }

                        if (referenceTime == workoutStartTime) {
                            colors.add(highlightColor)
                            highLightXIndex = xIndex
                        } else {
                            colors.add(color)
                        }
                        lastWorkoutDay = currentWorkoutDay
                        ++xIndex

                        yield()
                    }

                    RecentWorkoutSummaryLineData(
                        recentWorkoutSummary,
                        createCombinedData(
                            durationEntries,
                            durationBarEntries,
                            colors
                        ),
                        createCombinedData(
                            distanceEntries,
                            distanceBarEntries,
                            colors
                        ),
                        createCombinedData(
                            speedEntries,
                            speedBarEntries,
                            colors
                        ),
                        createCombinedData(
                            paceEntries,
                            paceBarEntries,
                            colors
                        ),
                        createCombinedData(
                            energyEntries,
                            energyBarEntries,
                            colors
                        ),
                        createCombinedData(
                            averageHeartRateEntries,
                            averageHeartRateBarEntries,
                            colors
                        ),
                        if (shouldAddCadence) {
                            createCombinedData(
                                averageCadenceEntries,
                                averageCadenceBarEntries,
                                colors
                            )
                        } else {
                            CombinedData()
                        },
                        highLightXIndex
                    )
                }
            } catch (e: InternalDataException) {
                Timber.e(e, "Failed to load recent workout summary")
                null
            }
        }

    companion object {
        private fun addEntry(
            value: Float,
            xIndex: Int,
            workoutHeader: WorkoutHeader,
            entries: ArrayList<Entry>,
            barEntries: ArrayList<BarEntry>
        ) {
            val data = Pair(entries.size, workoutHeader)
            entries.add(Entry(xIndex.toFloat(), value, data))
            barEntries.add(BarEntry(xIndex.toFloat(), value, data))
        }

        /**
         * We're using CombinedData as a hack to show a circle on top of the bar data in the chart,
         * which is unfortunately not supported by the library.
         */
        private fun createCombinedData(
            entries: ArrayList<Entry>,
            barEntries: ArrayList<BarEntry>,
            colors: ArrayList<Int>
        ): CombinedData {
            // Data should be order based on x-axis values
            // https://github.com/PhilJay/MPAndroidChart/wiki/Setting-Data#the-order-of-entries
            entries.sortWith(EntryXComparator())
            barEntries.sortWith(EntryXComparator())
            val scatterDataSet = ScatterDataSet(entries, "").apply {
                setScatterShape(ScatterChart.ScatterShape.CIRCLE)
                setDrawValues(false)
                setDrawHighlightIndicators(false)
                this.colors = colors
                scatterShapeSize = 20f
            }
            val scattered = ArrayList<IScatterDataSet>().apply {
                add(scatterDataSet)
            }
            val barDataSet = BarDataSet(barEntries, "").apply {
                setDrawValues(false)
                this.colors = colors
            }
            val bars = ArrayList<IBarDataSet>().apply {
                add(barDataSet)
            }
            val barData = BarData(bars).apply {
                barWidth = 0.12f
            }

            return CombinedData().apply {
                setData(ScatterData(scattered))
                setData(barData)
            }
        }
    }
}
