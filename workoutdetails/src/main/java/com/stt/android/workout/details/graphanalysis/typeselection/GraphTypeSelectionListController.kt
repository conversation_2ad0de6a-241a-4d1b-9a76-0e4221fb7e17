package com.stt.android.workout.details.graphanalysis.typeselection

import android.content.Context
import androidx.appcompat.content.res.AppCompatResources
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.core.domain.GraphType
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.workout.details.R
import com.stt.android.workout.details.graphAnalysisTypeSelectionItem

class GraphTypeSelectionListController(
    private val context: Context
) : TypedEpoxyController<List<GraphTypeSelectionData>>() {
    override fun buildModels(data: List<GraphTypeSelectionData>?) {
        data?.forEach {
            graphAnalysisTypeSelectionItem {
                id(it.graphType.key)
                selected(it.selected)
                iconDrawable(
                    AppCompatResources.getDrawable(
                        context,
                        WorkoutAnalysisHelper.getGraphIconDrawableRes(it.graphType)
                    )
                )
                localizedName(getGraphTypeName(context, it.graphType))
                onClick { _, _, _, position ->
                    val item = currentData?.get(position) ?: return@onClick
                    item.onClick(item)
                }
            }
        }
    }

    private fun getGraphTypeName(context: Context, graphType: GraphType): String = when (graphType) {
        GraphType.NONE -> context.getString(R.string.hide)
        else -> WorkoutAnalysisHelper.getGraphNameTitle(context, graphType)
    }
}
