<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/SharpCornerBottomSheetStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_medium">

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/end_of_titles_barrier"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:barrierDirection="end"
            app:constraint_referenced_ids="main_graph_title,comparison_graph_title,background_graph_title" />

        <TextView
            android:id="@+id/main_graph_title"
            style="@style/Body.Large.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="150dp"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:drawablePadding="@dimen/size_spacing_small"
            android:text="@string/main_graph_title"
            app:drawableStartCompat="@drawable/graph_analysis_type_color_indicator"
            app:drawableTint="@color/graph_analysis_main"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/main_graph_options_list" />

        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/main_graph_options_list"
            android:layout_width="0dp"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:paddingStart="@dimen/size_spacing_small"
            android:paddingEnd="@dimen/size_spacing_small"
            android:scrollbars="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintStart_toEndOf="@id/end_of_titles_barrier"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/viewholder_graph_analysis_type_selection_item" />

        <TextView
            android:id="@+id/comparison_graph_title"
            style="@style/Body.Large.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="150dp"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:drawablePadding="@dimen/size_spacing_small"
            android:text="@string/comparison_graph_title"
            app:drawableStartCompat="@drawable/graph_analysis_type_color_indicator"
            app:drawableTint="@color/graph_analysis_comparison"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/comparison_graph_options_list" />

        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/comparison_graph_options_list"
            android:layout_width="0dp"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:paddingStart="@dimen/size_spacing_small"
            android:paddingEnd="@dimen/size_spacing_small"
            android:scrollbars="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/main_graph_options_list"
            app:layout_constraintStart_toEndOf="@id/end_of_titles_barrier"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/viewholder_graph_analysis_type_selection_item" />

        <TextView
            android:id="@+id/background_graph_title"
            style="@style/Body.Large.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="150dp"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:drawablePadding="@dimen/size_spacing_small"
            android:text="@string/background_graph_title"
            app:drawableStartCompat="@drawable/graph_analysis_type_color_indicator"
            app:drawableTint="@color/graph_analysis_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/background_graph_options_list" />

        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/background_graph_options_list"
            android:layout_width="0dp"
            android:layout_height="90dp"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:layout_marginBottom="@dimen/size_spacing_large"
            android:orientation="horizontal"
            android:overScrollMode="never"
            android:paddingStart="@dimen/size_spacing_small"
            android:paddingEnd="@dimen/size_spacing_small"
            android:scrollbars="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/comparison_graph_options_list"
            app:layout_constraintStart_toEndOf="@id/end_of_titles_barrier"
            app:layout_constraintEnd_toEndOf="parent"
            tools:listitem="@layout/viewholder_graph_analysis_type_selection_item" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
