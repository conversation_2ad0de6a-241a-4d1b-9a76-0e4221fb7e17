<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="text"
            type="String" />
    </data>

    <TextView
        android:id="@+id/rankingDescription"
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="?android:colorBackground"
        android:drawablePadding="4dp"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_small"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_small"
        android:text="@{text}"
        android:textAppearance="@style/Body.Medium"
        app:drawableStartCompat="@drawable/ic_achievement"
        tools:text="Fastest time on this route" />

</layout>
