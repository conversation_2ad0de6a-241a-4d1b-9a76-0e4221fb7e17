<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="reactionsData"
            type="com.stt.android.workout.details.ReactionsData" />

        <variable
            name="onLikeClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="onAvatarClicked"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="@dimen/reactions_height">

        <ImageButton
            android:id="@+id/likes_icon"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:background="?android:attr/selectableItemBackground"
            android:contentDescription="@string/like_button"
            android:onClick="@{onLikeClicked}"
            android:padding="@dimen/smaller_padding"
            android:scaleType="center"
            android:src="@{reactionsData.reactionSummary.userReacted ? @drawable/ic_thumb_up_filled : @drawable/workout_card_thumb_icon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/workout_card_thumb_icon" />

        <TextView
            android:id="@+id/likes_count"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center_vertical"
            android:paddingEnd="@dimen/size_spacing_xsmaller"
            android:text="@{String.valueOf(reactionsData.reactionSummary.count)}"
            android:textAppearance="@style/Body.Medium"
            android:textColor="@color/newAccent"
            android:visibility="@{reactionsData.reactionSummary.count > 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/likes_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry"
            tools:text="2500000" />

        <LinearLayout
            android:id="@+id/users__profile_picture_container"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center_vertical|start"
            android:onClick="@{onAvatarClicked}"
            android:orientation="horizontal"
            app:avatars="@{reactionsData.userAvatarsUrls}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/likes_count"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/userImage1"
                android:layout_width="@dimen/size_icon_medium"
                android:layout_height="@dimen/size_icon_medium"
                android:layout_marginStart="@dimen/like_profile_images_spacing"
                android:layout_marginEnd="@dimen/like_profile_images_spacing"
                android:contentDescription="@string/user_profile_picture"
                tools:src="@drawable/ic_default_profile_image_light" />

            <ImageView
                android:id="@+id/userImage2"
                android:layout_width="@dimen/size_icon_medium"
                android:layout_height="@dimen/size_icon_medium"
                android:layout_marginStart="@dimen/like_profile_images_spacing"
                android:layout_marginEnd="@dimen/like_profile_images_spacing"
                android:contentDescription="@string/user_profile_picture"
                tools:src="@drawable/ic_default_profile_image_light" />

            <ImageView
                android:id="@+id/userImage3"
                android:layout_width="@dimen/size_icon_medium"
                android:layout_height="@dimen/size_icon_medium"
                android:layout_marginStart="@dimen/like_profile_images_spacing"
                android:layout_marginEnd="@dimen/like_profile_images_spacing"
                android:contentDescription="@string/user_profile_picture"
                tools:src="@drawable/ic_default_profile_image_light" />
        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/feed_card_gradient_height"
            android:background="@drawable/section_divider_gradient"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/users__profile_picture_container" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
