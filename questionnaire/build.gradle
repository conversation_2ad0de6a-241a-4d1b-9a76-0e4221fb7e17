plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    alias libs.plugins.androidx.navigation.safeargs
}

android {
    namespace 'com.stt.android.questionnaire'
    buildFeatures.buildConfig = true
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.userDomain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.analytics)
}
