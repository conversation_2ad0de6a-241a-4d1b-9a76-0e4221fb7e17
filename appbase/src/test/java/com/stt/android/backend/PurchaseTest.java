package com.stt.android.backend;

import com.google.gson.reflect.TypeToken;
import com.stt.android.billing.Purchase;
import com.stt.android.domain.user.BackendPurchase;
import org.json.JSONException;
import org.junit.Test;

import java.lang.reflect.Type;
import java.util.Map;

import static junit.framework.TestCase.assertEquals;

public class PurchaseTest extends GsonTestCase {
    @Test
    public void testMonthlyAutoRenewActive() throws JSONException {
        String itemType = "subs";
        String jsonPurchaseInfo = "{\"orderId\":\"12999763169054705758.1314767192534503\"," +
            "\"packageName\":\"com.stt.android\"," +
            "\"productId\":\"google_premium_monthly_3_59\",\"purchaseTime\":1399555738491," +
            "\"purchaseState\":0,\"developerPayload\":\"ACTIVE:j_u_s_h\"," +
            "\"purchaseToken\":\"eldkbgmihllfkkpikblppjkm"
            + ".AO-J1OwGV8VjPc4KuEQNEOaAn0tJQjt7266gJeBtGBZydJV-nHBlCh1Xy8fSR5Xl"
            + "-t1UaXntYR4igrk9mF-bRwZZAsIWi1Pp8tQO3qmSp7_Y9y9sJpbxxLS5INX-XP2rWAzBVHP3IiTr\"}";
        String signature =
            "gV8oUYmZDh9T8zyFG2F5hM7sdkbeiBx20wmwiwehifdq0W5RYp9NCRhoWerhDqlZSrdfpJy6F8YzSzmTZgK"
                + "+4HQnRF0xhIC1MT8wBl3EDsUGwDO+mnwy0"
                + "/oLRFG3nAT1ZyN8Gl2CRghrXWkq6qRthmID9yUaqKKapUBh5HUeNoylR/CBxNFopHhl5P"
                + "+rzxpbDIEWxTOB1DylbbGzMi2z34iBptlH2I7swCC95hWQ3JKPSJ"
                + "/otndxk0iVZS6DviUWBgE9RIHXPWJ9sFmZ7pp3cMCk+jN6rj5KmShbRwUQep74FqeU"
                + "/ITYuFh6VazSJ4m39505e/sfxBlO8m3faCPbTg==";
        Purchase purchase =
            new Purchase(itemType, signature, "12999763169054705758" + ".1314767192534503",
                "com.stt.android", "google_premium_monthly_3_59", 1399555738491L, 0,
                "ACTIVE:j_u_s_h", "eldkbgmihllfkkpikblppjkm"
                + ".AO-J1OwGV8VjPc4KuEQNEOaAn0tJQjt7266gJeBtGBZydJV-nHBlCh1Xy8fSR5Xl"
                + "-t1UaXntYR4igrk9mF-bRwZZAsIWi1Pp8tQO3qmSp7_Y9y9sJpbxxLS5INX-XP2rWAzBVHP3IiTr",
                jsonPurchaseInfo);
        String actualJsonString = gson.toJson(new BackendPurchase(purchase));

        // We can't compare json strings directly as the order might change. So let's transform
        // them back to a generic map of objects and compare them.
        Type wrapperType = new TypeToken<Map<Object, Object>>() {
        }.getType();
        Map<Object, Object> actualJson = gson.fromJson(actualJsonString, wrapperType);

        String expectedJsonString =
            "{\"purchaseRawInfo\":\"{\\\"orderId\\\":\\\"12999763169054705758" +
                ".1314767192534503\\\",\\\"packageName\\\":\\\"com.stt.android\\\"," +
                "\\\"productId\\\":\\\"google_premium_monthly_3_59\\\"," +
                "\\\"purchaseTime\\\":1399555738491,\\\"purchaseState\\\":0," +
                "\\\"developerPayload\\\":\\\"ACTIVE:j_u_s_h\\\"," +
                "\\\"purchaseToken\\\":\\\"eldkbgmihllfkkpikblppjkm" +
                ".AO-J1OwGV8VjPc4KuEQNEOaAn0tJQjt7266gJeBtGBZydJV-nHBlCh1Xy8fSR5Xl" +
                "-t1UaXntYR4igrk9mF-bRwZZAsIWi1Pp8tQO3qmSp7_Y9y9sJpbxxLS5INX-XP2rWAzBVHP3IiTr" +
                "\\\"}\",\"purchaseDate\":1399555738,\"purchaseID\":\"12999763169054705758" +
                ".1314767192534503\",\"productID\":\"google_premium_monthly_3_59\"}";
        Map<Object, Object> expectedJson = gson.fromJson(expectedJsonString, wrapperType);

        assertEquals(expectedJson, actualJson);
    }
}
