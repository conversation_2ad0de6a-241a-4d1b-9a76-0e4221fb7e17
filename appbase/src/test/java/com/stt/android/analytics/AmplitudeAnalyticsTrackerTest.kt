package com.stt.android.analytics

import com.stt.android.FeatureFlags
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.user.User
import com.stt.android.domain.user.UserSettings
import com.stt.android.eventtracking.EventTracker
import com.stt.android.remote.remoteconfig.AmplitudeEventSampling
import org.assertj.core.api.Assertions
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.mock

@RunWith(MockitoJUnitRunner::class)
class AmplitudeAnalyticsTrackerTest {

    @Mock
    lateinit var featureFlags: FeatureFlags

    @Mock
    lateinit var userSettingsController: UserSettingsController

    @Mock
    lateinit var currentUserDataSource: CurrentUserDataSource

    @Mock
    lateinit var eventTracker: EventTracker

    @Mock
    lateinit var user: User

    private lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTrackerImpl

    private val watchScreenEvent = "WatchScreen"
    private val homeScreenEvent = "HomeScreen"
    private val calendarScreenEvent = "CalendarScreen"
    private val diaryScreen = "DiaryScreen"

    private val userBucketValue = 0.3

    private val analyticsEventSamplingConfig: List<AmplitudeEventSampling> = listOf(
        AmplitudeEventSampling(eventName = watchScreenEvent, eventSampling = 0.1),
        AmplitudeEventSampling(eventName = homeScreenEvent, eventSampling = 0.5),
        AmplitudeEventSampling(eventName = diaryScreen, eventSampling = 0.3)
    )

    @Before
    fun setup() {
        amplitudeAnalyticsTracker = AmplitudeAnalyticsTrackerImpl(
            featureFlags = featureFlags,
            userSettingsController = userSettingsController,
            currentUserDataSource = currentUserDataSource,
            eventTracker = eventTracker,
        )
    }

    @Test
    fun `should send home screen event`() {
        val userSettings = mock<UserSettings>()
        Mockito.`when`(userSettingsController.settings).thenReturn(userSettings)
        Mockito.`when`(currentUserDataSource.getCurrentUser()).thenReturn(user)
        Mockito.`when`(user.isFieldTester).thenReturn(false)
        Mockito.`when`(userSettings.samplingBucketValue).thenReturn(userBucketValue)
        Mockito.`when`(featureFlags.analyticsEventSamplingConfig)
            .thenReturn(analyticsEventSamplingConfig)

        val result = amplitudeAnalyticsTracker.shouldSendEventToAmplitude(homeScreenEvent)
        Assertions.assertThat(result).isTrue
    }

    @Test
    fun `should not send watch screen event`() {
        val userSettings = mock<UserSettings>()
        Mockito.`when`(userSettingsController.settings).thenReturn(userSettings)
        Mockito.`when`(currentUserDataSource.getCurrentUser()).thenReturn(user)
        Mockito.`when`(user.isFieldTester).thenReturn(false)
        Mockito.`when`(userSettings.samplingBucketValue).thenReturn(userBucketValue)
        Mockito.`when`(featureFlags.analyticsEventSamplingConfig)
            .thenReturn(analyticsEventSamplingConfig)

        val result = amplitudeAnalyticsTracker.shouldSendEventToAmplitude(watchScreenEvent)
        Assertions.assertThat(result).isFalse
    }

    @Test
    fun `should send event if sampling rate is equal to bucket value`() {
        val userSettings = mock<UserSettings>()
        Mockito.`when`(userSettingsController.settings).thenReturn(userSettings)
        Mockito.`when`(currentUserDataSource.getCurrentUser()).thenReturn(user)
        Mockito.`when`(user.isFieldTester).thenReturn(false)
        Mockito.`when`(userSettings.samplingBucketValue).thenReturn(userBucketValue)
        Mockito.`when`(featureFlags.analyticsEventSamplingConfig)
            .thenReturn(analyticsEventSamplingConfig)

        val result = amplitudeAnalyticsTracker.shouldSendEventToAmplitude(diaryScreen)
        Assertions.assertThat(result).isTrue
    }

    @Test
    fun `should send event if it's not included in sampling config`() {
        val userSettings = mock<UserSettings>()
        Mockito.`when`(userSettingsController.settings).thenReturn(userSettings)
        Mockito.`when`(currentUserDataSource.getCurrentUser()).thenReturn(user)
        Mockito.`when`(user.isFieldTester).thenReturn(false)
        Mockito.`when`(userSettings.samplingBucketValue).thenReturn(userBucketValue)
        Mockito.`when`(featureFlags.analyticsEventSamplingConfig)
            .thenReturn(analyticsEventSamplingConfig)

        val result = amplitudeAnalyticsTracker.shouldSendEventToAmplitude(calendarScreenEvent)
        Assertions.assertThat(result).isTrue
    }

    @Test
    fun `should send if the user is fieldtester`() {
        Mockito.`when`(currentUserDataSource.getCurrentUser()).thenReturn(user)
        Mockito.`when`(user.isFieldTester).thenReturn(true)

        val result = amplitudeAnalyticsTracker.shouldSendEventToAmplitude(watchScreenEvent)
        Assertions.assertThat(result).isTrue
    }
}
