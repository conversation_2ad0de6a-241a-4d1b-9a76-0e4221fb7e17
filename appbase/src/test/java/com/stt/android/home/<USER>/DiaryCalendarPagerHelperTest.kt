package com.stt.android.home.diarycalendar

import com.stt.android.home.diary.diarycalendar.parseMonth
import com.stt.android.home.diary.diarycalendar.parseWeek
import com.stt.android.home.diary.diarycalendar.parseYear
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.time.LocalDate
import java.time.Year
import java.time.YearMonth
import java.time.temporal.WeekFields
import java.util.Locale

@RunWith(MockitoJUnitRunner::class)
class DiaryCalendarPagerHelperTest {

    @Test
    fun `Year should be parsed from date string`() {
        val dateString = "2020-02-02"
        val year = parseYear(dateString)
        assertThat(year).isEqualTo(Year.of(2020))
    }

    @Test
    fun `Year should be parsed from year-month string`() {
        val yearMonthString = "2019-12"
        val year = parseYear(yearMonthString)
        assertThat(year).isEqualTo(Year.of(2019))
    }

    @Test
    fun `Year should be parsed from year string`() {
        val yearString = "2018"
        val year = parseYear(yearString)
        assertThat(year).isEqualTo(Year.of(2018))
    }

    @Test
    fun `Year should be parsed from positive offset`() {
        val positiveOffset = "2"
        val year = parseYear(positiveOffset)
        assertThat(year).isEqualTo(Year.now().plusYears(2))
    }

    @Test
    fun `Year should be parsed from a negative offset`() {
        val negativeOffset = "-3"
        val year = parseYear(negativeOffset)
        assertThat(year).isEqualTo(Year.now().minusYears(3))
    }

    @Test
    fun `Month should be parsed from a date string`() {
        val dateString = "2020-12-01"
        val month = parseMonth(dateString)
        assertThat(month.year).isEqualTo(2020)
        assertThat(month.month.value).isEqualTo(12)
    }

    @Test
    fun `Month should be parsed from a year-month string`() {
        val yearMonthString = "2019-10"
        val month = parseMonth(yearMonthString)
        assertThat(month.year).isEqualTo(2019)
        assertThat(month.month.value).isEqualTo(10)
    }

    @Test
    fun `Month should be parsed from a negative offset`() {
        val negativeOffset = "-1"
        val month = parseMonth(negativeOffset)
        val currentMonth = YearMonth.now()
        assertThat(month.year).isEqualTo(currentMonth.minusMonths(1).year)
        assertThat(month.month.value).isEqualTo(currentMonth.minusMonths(1).month.value)
    }

    @Test
    fun `Month should be parsed from a positive offset`() {
        val positiveOffset = "5"
        val month = parseMonth(positiveOffset)
        val currentMonth = YearMonth.now()
        assertThat(month.year).isEqualTo(currentMonth.plusMonths(5).year)
        assertThat(month.month.value).isEqualTo(currentMonth.plusMonths(5).month.value)
    }

    @Test
    fun `Week should be parsed from a date string`() {
        val dateString = "2019-10-29"
        val dayOfWeek = WeekFields.of(Locale.getDefault()).dayOfWeek()
        val week = parseWeek(dateString, dayOfWeek)
        assertThat(week).isEqualTo(LocalDate.of(2019, 10, 29).with(dayOfWeek, 1L))
    }

    @Test
    fun `Week should be parsed from a negative offset`() {
        val negativeOffset = "-5"
        val dayOfWeek = WeekFields.of(Locale.getDefault()).dayOfWeek()
        val currentWeek = LocalDate.now().with(dayOfWeek, 1L)
        val week = parseWeek(negativeOffset, dayOfWeek)
        assertThat(week).isEqualTo(currentWeek.minusWeeks(5))
    }

    @Test
    fun `Week should be parsed from a positive offset`() {
        val positiveOffset = "12"
        val dayOfWeek = WeekFields.of(Locale.getDefault()).dayOfWeek()
        val currentWeek = LocalDate.now().with(dayOfWeek, 1L)
        val week = parseWeek(positiveOffset, dayOfWeek)
        assertThat(week).isEqualTo(currentWeek.plusWeeks(12))
    }
}
