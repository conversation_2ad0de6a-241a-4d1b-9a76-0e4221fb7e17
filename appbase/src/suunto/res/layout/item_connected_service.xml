<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="item"
            type="com.stt.android.home.settings.connectedservices.list.ConnectedServiceItem" />
        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_medium"
        style="@style/FeedCard.ripple.noelevation">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/size_icon_large"
            android:layout_height="@dimen/size_icon_large"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:scaleType="fitCenter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/movescount_small" />

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:text="@{item.serviceMetadata.localization.viewTitle}"
            app:layout_constraintBottom_toTopOf="@+id/info"
            app:layout_constraintStart_toEndOf="@+id/icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Movescount"
            style="@style/Body.Larger" />

        <ImageView
            android:id="@+id/connected_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="@dimen/size_spacing_xsmall"
            android:src="@drawable/checkmark_grey"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/title"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:src="@drawable/checkmark_grey"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/info"
            android:layout_width="@dimen/size_spacing_zero"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/size_spacing_xxxxlarge"
            android:layout_marginTop="@dimen/size_spacing_xsmall"
            android:text="@{item.getInfoText()}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/connected_icon"
            app:layout_constraintTop_toBottomOf="@+id/title"
            app:visible="@{item.infoTextVisible()}"
            android:textColor="@color/suunto_blue"
            tools:text="Import your training library from Movescount."
            style="@style/Body.Medium" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
