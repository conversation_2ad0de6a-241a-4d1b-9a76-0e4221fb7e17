<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <import type="android.view.View" />

        <variable
            name="text"
            type="int" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="waitState"
            type="boolean" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="?suuntoItemBackgroundColor">

        <Button
            android:id="@+id/title"
            style="@style/Button.Primary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:layout_margin="10dp"
            android:onClick="@{onClickListener}"
            android:text="@{context.getString(text)}"
            android:visibility="@{ waitState ? View.INVISIBLE : View.VISIBLE }"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="Download update" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:clickable="false"
            android:visibility="@{ waitState ? View.VISIBLE : View.INVISIBLE }"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

