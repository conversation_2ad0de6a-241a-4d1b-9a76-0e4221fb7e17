<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/modal_bottomsheet"
    style="@style/RoundedCornerBottomSheetStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/bottom_sheet_bottom_padding">

    <TextView
        android:id="@+id/textview_weekly_goal_title"
        android:layout_width="@dimen/size_spacing_zero"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/bottom_sheet_title_margin"
        android:layout_marginStart="@dimen/bottom_sheet_title_margin"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin"
        android:text="@string/weekly_goal_bottom_sheet_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/HeaderLabel.Large" />

    <TextView
        android:id="@+id/textview_training_plan_enabled"
        android:layout_width="@dimen/size_spacing_zero"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin"
        android:text="@string/weekly_goal_bottom_sheet_training_plan_enabled"
        android:textSize="@dimen/text_size_medium"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/textview_weekly_goal_title"
        app:layout_constraintStart_toStartOf="@id/textview_weekly_goal_title"
        app:layout_constraintTop_toBottomOf="@id/textview_weekly_goal_title" />

    <TextView
        android:id="@+id/textview_training_duration_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:text="@string/duration"
        app:layout_constraintStart_toStartOf="@id/textview_weekly_goal_title"
        app:layout_constraintTop_toBottomOf="@id/textview_training_plan_enabled"
        style="@style/Body.Larger" />

    <TextView
        android:id="@+id/textview_training_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/textview_weekly_goal_title"
        app:layout_constraintTop_toTopOf="@+id/textview_training_duration_title"
        tools:text="3 h 00 min"
        style="@style/Body.Larger" />

    <SeekBar
        android:id="@+id/seekbar_training_duration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="6dp"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:layout_marginStart="@dimen/size_spacing_small"
        android:paddingTop="@dimen/size_spacing_small"
        android:paddingBottom="@dimen/size_spacing_small"
        android:progressDrawable="@drawable/progress_seek_bar"
        android:thumb="@drawable/thumb_seek_bar"
        app:layout_constraintEnd_toEndOf="@id/textview_weekly_goal_title"
        app:layout_constraintStart_toStartOf="@id/textview_training_duration_title"
        app:layout_constraintTop_toBottomOf="@id/textview_training_duration"
        tools:progress="40" />

    <TextView
        android:id="@+id/training_duration_min"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/textview_weekly_goal_title"
        app:layout_constraintTop_toBottomOf="@id/seekbar_training_duration"
        tools:text="1 h"
        style="@style/Body.Larger" />

    <TextView
        android:id="@+id/training_duration_max"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/textview_weekly_goal_title"
        app:layout_constraintTop_toBottomOf="@id/seekbar_training_duration"
        tools:text="25 h"
        style="@style/Body.Larger" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_training_duration_settings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="textview_training_duration_title,
        textview_training_duration, seekbar_training_duration,
        training_duration_min, training_duration_max" />

    <View
        android:id="@+id/training_plan_separator"
        android:layout_width="@dimen/size_spacing_zero"
        android:layout_height="1dp"
        android:layout_marginBottom="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin"
        android:background="@color/weekly_goal_separator"
        app:layout_constraintEnd_toEndOf="@id/training_duration_max"
        app:layout_constraintStart_toStartOf="@id/training_duration_min"
        app:layout_constraintTop_toBottomOf="@id/training_duration_max" />

    <TextView
        android:id="@+id/training_plan_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin_larger"
        android:text="@string/weekly_goal_bottom_sheet_training_plan_title"
        app:layout_constraintStart_toStartOf="@id/training_plan_separator"
        app:layout_constraintTop_toBottomOf="@id/training_plan_separator"
        style="@style/Body.Medium.Bold" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch_training_plan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin_larger"
        android:theme="@style/SwitchTheme"
        app:layout_constraintEnd_toEndOf="@id/training_plan_separator"
        app:layout_constraintTop_toBottomOf="@id/training_plan_separator" />

    <TextView
        android:id="@+id/training_plan_message"
        android:layout_width="@dimen/size_spacing_zero"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/bottom_sheet_top_margin_larger"
        android:text="@string/weekly_goal_bottom_sheet_training_plan_message"
        app:layout_constraintEnd_toEndOf="@id/training_plan_separator"
        app:layout_constraintStart_toStartOf="@id/training_plan_separator"
        app:layout_constraintTop_toBottomOf="@id/training_plan_title"
        style="@style/Body.Medium" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_training_plan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="training_plan_separator,training_plan_title,
            switch_training_plan,training_plan_message" />
</androidx.constraintlayout.widget.ConstraintLayout>
