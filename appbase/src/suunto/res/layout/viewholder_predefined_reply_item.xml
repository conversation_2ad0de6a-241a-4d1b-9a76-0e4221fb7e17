<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="predefinedReply"
            type="String" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:background="@drawable/selectable_card_background"
        android:onClick="@{onClickListener}"
        android:orientation="vertical"
        android:padding="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_medium">

        <TextView
            style="@style/Body.Larger"
            android:text="@{predefinedReply}"
            android:textColor="@color/settings_item_color"
            tools:text="Predefined reply" />

    </FrameLayout>
</layout>
