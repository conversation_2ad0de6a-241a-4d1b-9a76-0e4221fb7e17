<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.stt.android.home.dayview.DayViewActivity">

    <data />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?suuntoBackground">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/day_view_app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/day_view_toolbar"
                style="@style/Toolbar.Native"
                app:theme="@style/Toolbar.Native"
                tools:title="Today" />
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/day_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/size_divider"
            android:orientation="horizontal"
            android:scrollbars="none"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_day_view_page" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
