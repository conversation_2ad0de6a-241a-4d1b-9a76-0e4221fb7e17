<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="progressValue"
            type="int" />

        <variable
            name="description"
            type="Integer" />

        <variable
            name="statustext"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?suuntoItemBackgroundColor"
        android:paddingTop="@dimen/size_spacing_large"
        android:paddingBottom="@dimen/size_spacing_xlarge">

        <TextView
            android:id="@+id/update_loading"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="6dp"
            android:text="@{context.getString(description)}"
            android:textColor="@color/medium_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Update loading" />

        <ProgressBar
            android:id="@+id/progress"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="0dp"
            android:layout_height="@dimen/diary_calendar_activity_progress_bar_height"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:progress="@{progressValue}"
            android:progressBackgroundTint="?suuntoBackground"
            android:progressTint="@color/suunto_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/update_loading"
            tools:progress="65" />

        <TextView
            android:id="@+id/loading_status"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:includeFontPadding="true"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="6dp"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:text="@{statustext}"
            android:textColor="@color/medium_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progress"
            tools:text="3.634 Mb / 6.6Mb" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

