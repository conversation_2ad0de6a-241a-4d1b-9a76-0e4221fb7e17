<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.stt.android.login.notification.AskNotificationViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/signup_permissions_constraint_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ask_notification_imageview"
                android:layout_width="@dimen/size_icon_xxxlarge"
                android:layout_height="@dimen/size_icon_xxxlarge"
                android:layout_marginTop="100dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_enable_notification"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/notifications_title"
                style="@style/HeaderLabel.Larger"
                android:layout_marginTop="@dimen/size_spacing_xlarge"
                android:gravity="center_horizontal"
                android:padding="@dimen/size_spacing_medium"
                android:text="@string/ask_notification_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ask_notification_imageview" />

            <TextView
                android:id="@+id/notifications_description"
                style="@style/Body.Medium"
                android:layout_marginBottom="@dimen/size_spacing_large"
                android:breakStrategy="balanced"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.3"
                android:padding="@dimen/size_spacing_medium"
                android:text="@string/ask_notification_description"
                app:layout_constraintBottom_toTopOf="@id/enable_notification_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/notifications_title"
                app:layout_constraintVertical_chainStyle="spread_inside" />

            <Button
                android:id="@+id/enable_notification_button"
                style="@style/Button.Primary"
                android:layout_width="0dp"
                android:layout_margin="@dimen/size_spacing_large"
                android:onClick="@{() -> viewModel.onAskNotificationClicked()}"
                android:text="@string/enable"
                android:visibility="@{!viewModel.isPostNotificationGranted ? View.VISIBLE : View.INVISIBLE}"
                app:layout_constraintBottom_toTopOf="@+id/ask_notification_skip"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/notifications_description"
                tools:visibility="invisible" />

            <TextView
                android:id="@+id/ask_notification_textview"
                style="@style/HeaderLabel.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/enable"
                android:textAllCaps="true"
                app:layout_constraintBottom_toBottomOf="@id/enable_notification_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/enable_notification_button" />

            <ImageView
                android:id="@+id/ask_notification_image"
                android:layout_width="@dimen/size_icon_medium"
                android:layout_height="@dimen/size_icon_medium"
                android:layout_marginStart="@dimen/size_spacing_small"
                app:layout_constraintBottom_toBottomOf="@id/enable_notification_button"
                app:layout_constraintStart_toEndOf="@id/ask_notification_textview"
                app:layout_constraintTop_toTopOf="@id/enable_notification_button"
                app:srcCompat="@drawable/ic_check_white_24"
                app:tint="?android:attr/textColorPrimary" />

            <androidx.constraintlayout.widget.Group
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="@{viewModel.isPostNotificationGranted ? View.VISIBLE : View.GONE}"
                app:constraint_referenced_ids="ask_notification_textview,ask_notification_image" />

            <Button
                android:id="@+id/ask_notification_skip"
                style="@style/ButtonFlat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/size_spacing_large"
                android:onClick="@{() -> viewModel.onSkipClicked()}"
                android:text="@{!viewModel.isPostNotificationGranted ? @string/skip_str : @string/continue_str}"
                android:textAllCaps="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>
