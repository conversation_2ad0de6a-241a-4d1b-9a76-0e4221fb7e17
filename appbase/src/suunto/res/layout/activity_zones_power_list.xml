<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?suuntoBackground"
        android:orientation="vertical">

        <include
            layout="@layout/toolbar" />

        <com.stt.android.ui.utils.WidthLimiterLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include
                    android:id="@+id/runningPowerZones"
                    layout="@layout/title_summary_toggle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp" />

                <include
                    android:id="@+id/cyclingPowerZones"
                    layout="@layout/title_summary_toggle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp" />


            </LinearLayout>

        </com.stt.android.ui.utils.WidthLimiterLayout>

    </LinearLayout>

</layout>
