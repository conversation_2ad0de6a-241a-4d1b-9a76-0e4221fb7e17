<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="?suuntoBackground">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="@dimen/elevation_toolbar"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                style="@style/Toolbar.Native"
                app:theme="@style/Toolbar.Native" />
        </com.google.android.material.appbar.AppBarLayout>

        <com.stt.android.ui.utils.WidthLimiterLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="@dimen/size_spacing_xsmaller">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/device_information_header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?suuntoBackground"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/size_spacing_medium"
                        android:paddingBottom="@dimen/size_spacing_medium"
                        android:visibility="visible">

                        <TextView
                            style="@style/Body.Small.Bold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="false"
                            android:paddingStart="@dimen/size_spacing_medium"
                            android:paddingEnd="@dimen/size_spacing_medium"
                            android:text="@string/watch_details_device_information_header"
                            android:textAllCaps="true"
                            tools:text="Device information" />
                    </LinearLayout>

                    <include
                        android:id="@+id/serial"
                        layout="@layout/title_summary_preference"
                        app:enableTextSelection="@{true}"
                        app:separatorVisible="@{true}"
                        app:summaryGone="@{true}"
                        app:rightTextVisible="@{true}"/>

                    <include
                        android:id="@+id/wear_app_version"
                        layout="@layout/title_summary_preference"
                        app:enableTextSelection="@{true}"
                        app:separatorVisible="@{true}" />

                    <include
                        android:id="@+id/suunto_wear_services_version"
                        layout="@layout/title_summary_preference"
                        app:enableTextSelection="@{true}"
                        app:separatorVisible="@{true}" />

                    <include
                        android:id="@+id/wear_mdsp_version"
                        layout="@layout/title_summary_preference"
                        app:enableTextSelection="@{true}"
                        app:separatorVisible="@{true}" />

                    <include
                        android:id="@+id/watch_updates"
                        layout="@layout/title_summary_preference"
                        app:enableTextSelection="@{true}"
                        app:separatorVisible="@{true}" />

                    <include
                        android:id="@+id/watch_update_automatically"
                        layout="@layout/title_summary_toggle"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <View
                        android:id="@+id/watch_update_automatically_separator"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/very_light_gray"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </ScrollView>

        </com.stt.android.ui.utils.WidthLimiterLayout>
    </LinearLayout>
</layout>
