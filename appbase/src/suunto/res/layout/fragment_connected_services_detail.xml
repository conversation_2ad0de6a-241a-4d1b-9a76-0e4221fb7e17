<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.stt.android.home.settings.connectedservices.detail.ConnectedServicesDetailViewModel" />

        <import type="android.view.View" />

        <import type="com.stt.android.domain.connectedservices.AuthMethod" />
    </data>

    <com.stt.android.ui.utils.WidthLimiterLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?suuntoItemBackgroundColor"
            tools:theme="@style/WhiteTheme">

            <ScrollView
                android:id="@+id/scrollView2"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:fillViewport="true"
                app:layout_constraintBottom_toTopOf="@+id/divider"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_large"
                    android:paddingTop="@dimen/size_spacing_xxlarge"
                    android:paddingEnd="@dimen/size_spacing_large">

                    <LinearLayout
                        android:id="@+id/ll_connected_services_icons_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/connected_service_icon"
                            android:layout_width="@dimen/size_icon_xxxlarge"
                            android:layout_height="@dimen/size_icon_xxxlarge"
                            android:scaleType="fitCenter"
                            app:visible="@{viewModel.showServiceIcon}" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/connected_title"
                        style="@style/HeaderLabel.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/size_spacing_xxlarge"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="@dimen/size_spacing_small"
                        android:text="@{viewModel.descriptionTitle}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/ll_connected_services_icons_container" />

                    <TextView
                        android:id="@+id/connection_info"
                        style="@style/Body.Larger"
                        android:layout_width="@dimen/size_spacing_zero"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/size_spacing_xlarge"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="@dimen/size_spacing_small"
                        android:text="@{viewModel.descriptionBody}"
                        android:textSize="@dimen/text_size_larger"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/connected_title" />

                    <TextView
                        android:id="@+id/read_more"
                        style="@style/Body.Larger"
                        android:layout_width="@dimen/size_spacing_zero"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/size_spacing_xlarge"
                        android:gravity="center_horizontal"
                        android:onClick="@{() -> viewModel.onReadMore()}"
                        android:text="@string/onboarding_read_more"
                        android:textColor="@color/accent"
                        android:textSize="@dimen/text_size_larger"
                        android:visibility="@{viewModel.serviceMetadata.readMoreUrl.isEmpty() ? View.GONE : View.VISIBLE}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/connection_info" />

                    <ImageView
                        android:id="@+id/connected_service_image"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/size_spacing_large"
                        android:scaleType="fitCenter"
                        android:visibility="@{viewModel.showConnectButton ? View.VISIBLE : View.GONE}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/read_more" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_spacing_xxsmall"
                android:background="@drawable/shadow_background"
                app:layout_constraintBottom_toTopOf="@id/button_layout" />

            <LinearLayout
                android:id="@+id/button_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="?suuntoItemBackgroundColor"
                android:orientation="vertical"
                android:paddingStart="@dimen/size_spacing_xlarge"
                android:paddingTop="@dimen/size_spacing_medium"
                android:paddingEnd="@dimen/size_spacing_xlarge"
                android:paddingBottom="@dimen/size_spacing_medium"
                app:layout_constraintBottom_toBottomOf="parent">

                <Button
                    android:id="@+id/connect_button"
                    style="@style/Button.Primary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{() -> viewModel.onConnectionToggle()}"
                    android:text="@{viewModel.connectButtonText}"
                    android:visibility="@{viewModel.serviceMetadata.connectImageUrl.empty &amp;&amp; !viewModel.serviceMetadata.partnerUrl.empty &amp;&amp; viewModel.showConnectButton ? View.VISIBLE : View.GONE}" />

                <Button
                    android:id="@+id/disconnect_button"
                    style="@style/Button.Secondary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{() -> viewModel.onConnectionToggle()}"
                    android:text="@string/partner_connections_disconnect"
                    android:visibility="@{viewModel.showDisconnect ? View.VISIBLE : View.GONE}" />

                <ImageView
                    android:id="@+id/connect_button_image"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_icon_large"
                    android:onClick="@{() -> viewModel.onConnectionToggle()}"
                    android:scaleType="fitCenter"
                    android:visibility="@{viewModel.serviceMetadata.connectImageUrl.empty || !viewModel.showConnectButton ? View.GONE : View.VISIBLE}" />

                <TextView
                    android:id="@+id/openapi_connect_text"
                    style="@style/Body.Larger.Gray"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_xsmall"
                    android:gravity="center_horizontal"
                    android:text="@{viewModel.serviceMetadata.localization.instructionText}"
                    android:textSize="@dimen/text_size_larger"
                    android:visibility="@{viewModel.showConnectButton &amp;&amp; viewModel.serviceMetadata.authMethod == AuthMethod.OPEN_API ? View.VISIBLE : View.GONE}" />

                <TextView
                    android:id="@+id/openapi_connected_text"
                    style="@style/Body.Larger"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="@string/partner_connections_connected"
                    android:textSize="@dimen/text_size_larger"
                    android:visibility="@{!viewModel.showConnectButton &amp;&amp; viewModel.serviceMetadata.authMethod == AuthMethod.OPEN_API ? View.VISIBLE : View.GONE}" />

            </LinearLayout>

            <androidx.core.widget.ContentLoadingProgressBar
                style="@style/Widget.AppCompat.ProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:show="@{viewModel.isLoading}"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.stt.android.ui.utils.WidthLimiterLayout>

</layout>
