<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="sharedViewModel"
            type="com.stt.android.watch.DeviceHolderViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/size_spacing_small"
        android:paddingBottom="@dimen/size_spacing_small">

        <TextView
            android:id="@+id/upload_title"
            style="@style/HeaderLabel.Large"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_small"
            android:text="@string/forced_update_downloading_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/upload_progress"
            style="@android:style/Widget.ProgressBar.Horizontal"
            android:layout_width="0dp"
            android:layout_height="@dimen/diary_calendar_activity_progress_bar_height"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:progress="@{sharedViewModel.uploadProgressState.percentage}"
            android:progressBackgroundTint="?suuntoBackground"
            android:progressTint="@color/suunto_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/upload_title" />

        <TextView
            android:id="@+id/upload_percentage"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="true"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="6dp"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:text="@{sharedViewModel.uploadProgressState.displayPercentage}"
            android:textColor="@color/medium_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/upload_progress" />

        <TextView
            android:id="@+id/upload_info"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="6dp"
            android:text="@string/forced_update_downloading_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/upload_percentage" />

        <TextView
            android:id="@+id/get_to_know_new_watch"
            style="@style/Body.Medium.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_small"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_xsmall"
            android:layout_marginBottom="@dimen/size_spacing_xsmaller"
            android:textColor="@color/suunto_blue"
            android:text="@string/get_to_know_new_watch"
            android:visibility="@{safeUnbox(sharedViewModel.shouldShowIntroduction) ? View.VISIBLE : View.GONE}"
            android:onClick="@{() -> sharedViewModel.onFirmwareIntroductionLinkClick()}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/upload_info" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
