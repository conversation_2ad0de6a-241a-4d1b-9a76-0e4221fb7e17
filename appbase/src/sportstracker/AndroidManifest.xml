<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <provider
            android:authorities="@string/fb_app_id"
            android:exported="true"
            android:name="com.facebook.FacebookContentProvider"
            tools:ignore="ExportedContentProvider" />

        <activity android:name="com.stt.android.goals.edit.GoalEditActivity"
                  android:parentActivityName="com.stt.android.goals.summary.GoalSummaryActivity"
                  android:theme="@style/WhiteTheme"/>
        <activity android:name="com.stt.android.goals.summary.GoalSummaryActivity"
                  android:parentActivityName="com.stt.android.home.HomeActivity"
                  android:theme="@style/WhiteTheme"/>
        <activity
            android:name="com.stt.android.launcher.ProxyActivity"
            android:exported="true"
            android:theme="@style/WhiteTheme.Launcher">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <data android:scheme="com.sports-tracker" />
                <data android:scheme="com-sports-tracker" />
                <data android:host="custominbox" />
                <data android:host="campaign" />
                <data android:host="auth" />
                <data android:host="user" />
                <data android:host="workout" />
                <data android:host="followers" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter android:autoVerify="true"
                tools:ignore="AppLinkUrlError">
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="@string/firebase_dynamic_link" />
            </intent-filter>
        </activity>
    </application>
</manifest>
