package com.stt.android.ui.activities.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.stt.android.R
import com.stt.android.databinding.FragmentPowerManagementSupportBinding
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.help.BaseHelpshiftHelper
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PowerManagementSupportFragment : Fragment() {

    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val binding = DataBindingUtil.inflate<FragmentPowerManagementSupportBinding>(
            inflater,
            R.layout.fragment_power_management_support,
            container,
            false
        )
        binding.supportBtn.setOnClickListener { supportClicked() }
        return binding.root
    }

    private fun supportClicked() {
        val activity = activity
        activity?.let {
            BaseHelpshiftHelper.showSingleFaq(activity, "268", appVersionNumberForSupport)
        }
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.ui.activities.settings.FRAGMENT_TAG"

        @JvmStatic
        fun newInstance(): PowerManagementSupportFragment {
            return PowerManagementSupportFragment()
        }
    }
}
