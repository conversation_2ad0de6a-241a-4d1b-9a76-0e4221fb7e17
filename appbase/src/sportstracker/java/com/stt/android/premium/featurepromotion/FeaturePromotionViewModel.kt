package com.stt.android.premium.featurepromotion

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.subscriptions.DomainSubscriptionInfo
import com.stt.android.domain.subscriptions.DomainSubscriptionLength
import com.stt.android.domain.subscriptions.ListSubscriptionsUseCase
import com.stt.android.domain.subscriptions.anySubscriptionHasFreeTrialAvailable
import com.stt.android.premium.PremiumSubscriptionAnalytics
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class FeaturePromotionViewState(
    val promotedSubscription: DomainSubscriptionInfo?,
    val loadingPromotedSubscription: Boolean,
    val isFreeTrialAvailable: Boolean
)

@HiltViewModel
class FeaturePromotionViewModel @Inject constructor(
    handle: SavedStateHandle,
    private val listSubscriptionsUseCase: ListSubscriptionsUseCase,
    premiumSubscriptionAnalytics: PremiumSubscriptionAnalytics,
    private val dispatchers: CoroutinesDispatchers
) : ViewModel() {
    private val _viewState = MutableStateFlow(
        FeaturePromotionViewState(
            promotedSubscription = null,
            loadingPromotedSubscription = false,
            isFreeTrialAvailable = false
        )
    )
    val viewState: StateFlow<FeaturePromotionViewState>
        get() = _viewState

    init {
        _viewState.value = FeaturePromotionViewState(
            promotedSubscription = null,
            loadingPromotedSubscription = true,
            isFreeTrialAvailable = false
        )

        viewModelScope.launch(dispatchers.io) {
            try {
                val allSubscriptions = listSubscriptionsUseCase.listSubscriptions()
                val promotedSubscription = (
                    allSubscriptions.find { it.length == DomainSubscriptionLength.MONTHLY }
                        ?: allSubscriptions.find { it.length == DomainSubscriptionLength.YEARLY }
                        ?: allSubscriptions.firstOrNull()
                    )?.takeIf { it.localizedPrice != null }

                _viewState.value = FeaturePromotionViewState(
                    promotedSubscription = promotedSubscription,
                    loadingPromotedSubscription = false,
                    isFreeTrialAvailable = allSubscriptions.anySubscriptionHasFreeTrialAvailable()
                )
            } catch (e: Exception) {
                _viewState.value = FeaturePromotionViewState(
                    promotedSubscription = null,
                    loadingPromotedSubscription = false,
                    isFreeTrialAvailable = false
                )
            }
        }

        premiumSubscriptionAnalytics.trackFeaturePromotionOpened(
            source = handle[STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE] ?: AnalyticsPropertyValue.UNKNOWN,
            popupShownReason = handle[STTConstants.ExtraKeys.BUY_PREMIUM_POPUP_OPENED_REASON]
        )
    }
}
