package com.stt.android.premium

import android.content.Context
import androidx.fragment.app.FragmentManager
import com.stt.android.premium.featurepromotion.FeaturePromotionActivity
import com.stt.android.premium.featurepromotion.InDepthPremiumDescriptionActivity
import com.stt.android.premium.featurepromotion.Workout3DPlaybackPromotionDialog
import com.stt.android.premium.featurepromotion.WorkoutImpactTagsPromotionDialog
import com.stt.android.premium.purchase.PurchaseSubscriptionActivity
import javax.inject.Inject

class PremiumPromotionNavigatorImpl @Inject constructor() : PremiumPromotionNavigator {
    override fun newPremiumFeaturePromotionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) = FeaturePromotionActivity.newStartIntent(
        context = context,
        analyticsSource = analyticsSource,
        buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
    )

    override fun navigateToPremiumFeaturePromotion(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        context.startActivity(
            newPremiumFeaturePromotionIntent(
                context = context,
                analyticsSource = analyticsSource,
                buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
            )
        )
    }

    override fun newPurchaseSubscriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) = PurchaseSubscriptionActivity.newStartIntent(
        context = context,
        analyticsSource = analyticsSource,
        buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
    )

    override fun navigateToPurchaseSubscription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        context.startActivity(
            newPurchaseSubscriptionIntent(
                context = context,
                analyticsSource = analyticsSource,
                buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
            )
        )
    }

    override fun newInDepthPremiumDescriptionIntent(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) = InDepthPremiumDescriptionActivity.newStartIntent(
        context = context,
        analyticsSource = analyticsSource,
        buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
    )

    override fun navigateToInDepthPremiumDescription(
        context: Context,
        analyticsSource: String,
        buyPremiumPopupShownAnalyticsReason: String?
    ) {
        context.startActivity(
            newInDepthPremiumDescriptionIntent(
                context = context,
                analyticsSource = analyticsSource,
                buyPremiumPopupShownAnalyticsReason = buyPremiumPopupShownAnalyticsReason
            )
        )
    }

    override fun openWorkoutPlaybackPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String
    ) {
        Workout3DPlaybackPromotionDialog.show(
            analyticsSource = analyticsSource,
            fragmentManager = fragmentManager
        )
    }

    override fun openSuuntoTagsPromotionDialog(
        fragmentManager: FragmentManager,
        analyticsSource: String
    ) {
        WorkoutImpactTagsPromotionDialog.show(
            analyticsSource = analyticsSource,
            fragmentManager = fragmentManager
        )
    }
}
