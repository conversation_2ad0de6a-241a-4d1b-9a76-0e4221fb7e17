package com.stt.android.premium.featurepromotion

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.RoundedPrimaryButton
import com.stt.android.domain.subscriptions.DomainSubscriptionInfo
import com.stt.android.domain.subscriptions.DomainSubscriptionLength
import com.stt.android.domain.subscriptions.DomainSubscriptionType
import com.stt.android.premium.PremiumPromotionNavigator
import com.stt.android.premium.PromotedPrice
import com.stt.android.utils.STTConstants.ExtraKeys.BUY_PREMIUM_POPUP_OPENED_REASON
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class FeaturePromotionActivity : AppCompatActivity() {
    private val viewModel: FeaturePromotionViewModel by viewModels()

    @Inject
    lateinit var premiumPromotionNavigator: PremiumPromotionNavigator

    private val finishWithResultOkAfterSuccessfulPurchaseListener =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                setResult(Activity.RESULT_OK)
                finish()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        var dismissing by mutableStateOf(false)
        fun dismissActivity() {
            dismissing = true
            finish()
        }

        setContent {
            AppTheme {
                Scaffold(
                    topBar = {
                        TopAppBar(onNavigationBackClick = ::dismissActivity)
                    }
                ) { paddingValues ->
                    val viewState = viewModel.viewState.collectAsState().value

                    if (viewState.loadingPromotedSubscription) {
                        Box(
                            modifier = Modifier
                                .padding(paddingValues)
                                .fillMaxSize()
                                .background(MaterialTheme.colors.surface),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    } else {
                        FeaturePromotionScreen(
                            promotedSubscription = viewState.promotedSubscription,
                            isFreeTrialAvailable = viewState.isFreeTrialAvailable,
                            onContinueToPurchaseClick = ::openPurchaseActivity,
                            onExplorePremiumClick = ::openInDepthDescriptionActivity,
                            modifier = Modifier.padding(paddingValues)
                        )
                    }
                }
            }
        }
    }

    private fun openPurchaseActivity() {
        val intent = premiumPromotionNavigator.newPurchaseSubscriptionIntent(
            context = this,
            analyticsSource = intent.extras?.getString(NAVIGATED_FROM_SOURCE)
                ?: PremiumPurchaseFlowScreenSource.PREMIUM_FEATURE_SUMMARY_SCREEN,
            buyPremiumPopupShownAnalyticsReason = intent.extras?.getString(BUY_PREMIUM_POPUP_OPENED_REASON)
        ) ?: return

        finishWithResultOkAfterSuccessfulPurchaseListener.launch(intent)
    }

    private fun openInDepthDescriptionActivity() {
        val intent = premiumPromotionNavigator.newInDepthPremiumDescriptionIntent(
            context = this,
            analyticsSource = intent.extras?.getString(NAVIGATED_FROM_SOURCE)
                ?: PremiumPurchaseFlowScreenSource.PREMIUM_FEATURE_SUMMARY_SCREEN,
            buyPremiumPopupShownAnalyticsReason = intent.extras?.getString(BUY_PREMIUM_POPUP_OPENED_REASON)
        ) ?: return

        finishWithResultOkAfterSuccessfulPurchaseListener.launch(intent)
    }

    companion object {
        fun newStartIntent(
            context: Context,
            analyticsSource: String,
            buyPremiumPopupShownAnalyticsReason: String?
        ) = Intent(context, FeaturePromotionActivity::class.java).apply {
            putExtra(NAVIGATED_FROM_SOURCE, analyticsSource)
            putExtra(BUY_PREMIUM_POPUP_OPENED_REASON, buyPremiumPopupShownAnalyticsReason)
        }
    }
}

@Composable
private fun TopAppBar(onNavigationBackClick: () -> Unit) {
    TopAppBar(
        title = {
            // No title
        },
        navigationIcon = {
            IconButton(onClick = onNavigationBackClick) {
                Icon(
                    painter = painterResource(R.drawable.all_ic_arrow_left),
                    contentDescription = stringResource(R.string.back),
                    tint = Color.Black,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small)
                )
            }
        },
        backgroundColor = Color.White,
        elevation = 0.dp,
    )
}

@Composable
private fun LoadingState(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        CircularProgressIndicator(
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun FeaturePromotionScreen(
    promotedSubscription: DomainSubscriptionInfo?,
    isFreeTrialAvailable: Boolean,
    onContinueToPurchaseClick: () -> Unit,
    onExplorePremiumClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.narrowContent()
    ) {
        Surface(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            ContentBody(
                isFreeTrialAvailable = isFreeTrialAvailable,
                onExplorePremiumClick = onExplorePremiumClick
            )
        }

        Surface {
            Footer(
                promotedSubscription = promotedSubscription,
                isFreeTrialAvailable = isFreeTrialAvailable,
                onContinueToPurchaseClick = onContinueToPurchaseClick,
            )
        }
    }
}

@Composable
private fun ContentBody(
    isFreeTrialAvailable: Boolean,
    onExplorePremiumClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(horizontal = MaterialTheme.spacing.large)
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

        Text(
            text = stringResource(id = R.string.feature_promotion_header_title).uppercase(),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

        val headerBody = if (isFreeTrialAvailable) {
            "${stringResource(id = R.string.feature_promotion_header_body)} ${stringResource(id = R.string.start_free_trial_and_try_for_30_days)}"
        } else {
            stringResource(id = R.string.feature_promotion_header_body)
        }
        Text(
            text = headerBody,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))

        Text(
            text = stringResource(id = R.string.feature_promotion_with_premium_you_get).uppercase(),
            modifier = Modifier.fillMaxWidth(), // Make sure text aligns at start while Column is otherwise center-aligned
            style = MaterialTheme.typography.bodyLargeBold
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        PromotionSection(
            iconId = R.drawable.phone_outline,
            textId = R.string.feature_promotion_section_ad_free
        )
        PromotionSection(
            iconId = R.drawable.ic_map_type_outline,
            textId = R.string.feature_promotion_section_maps
        )
        PromotionSection(
            iconId = R.drawable.ic_dashboard_widget_progress,
            textId = R.string.feature_promotion_section_progress
        )
        PromotionSection(
            iconId = R.drawable.ic_route_fill,
            textId = R.string.feature_promotion_section_routes
        )
        PromotionSection(
            iconId = R.drawable.analysis_outline,
            textId = R.string.feature_promotion_section_analyze_and_compare_workouts
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        Text(
            text = stringResource(id = R.string.explore_all_premium_features).uppercase(),
            modifier = Modifier
                .clickable(onClick = onExplorePremiumClick)
                .padding(all = MaterialTheme.spacing.smaller),
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colors.primary
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
    }
}

@Composable
private fun ColumnScope.PromotionSection(
    @DrawableRes iconId: Int,
    @StringRes textId: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = iconId),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.large)
        )

        Text(
            text = stringResource(id = textId),
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLarge,
        )
    }
}

@Composable
private fun Footer(
    promotedSubscription: DomainSubscriptionInfo?,
    isFreeTrialAvailable: Boolean,
    onContinueToPurchaseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Divider(modifier = Modifier.fillMaxWidth())
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))

        if (promotedSubscription != null) {
            Text(
                text = stringResource(id = R.string.price_starting_from),
                style = MaterialTheme.typography.bodySmall
            )
            PromotedPrice(promotedSubscription)
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }

        RoundedPrimaryButton(
            onClick = onContinueToPurchaseClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.large)
        ) {
            val buttonTextRes = if (isFreeTrialAvailable) {
                R.string.continue_to_free_trial
            } else {
                R.string.buy_premium
            }
            Text(text = stringResource(id = buttonTextRes).uppercase())
        }

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
    }
}

@Preview(name = "Portrait", widthDp = 420, heightDp = 700)
@Preview(name = "Landscape", widthDp = 900, heightDp = 520)
@Composable
private fun PreviewFeaturePromotionScreenNoFreeTrial() {
    PreviewFeaturePromotionScreenWitData(isFreeTrialAvailable = false)
}

@Preview(name = "Portrait", widthDp = 420, heightDp = 700)
@Composable
private fun PreviewFeaturePromotionScreenWithFreeTrial() {
    PreviewFeaturePromotionScreenWitData(isFreeTrialAvailable = true)
}

@Composable
private fun PreviewFeaturePromotionScreenWitData(isFreeTrialAvailable: Boolean) {
    val promotedSubscription = DomainSubscriptionInfo(
        "Monthly",
        DomainSubscriptionType.ACTIVE,
        DomainSubscriptionLength.MONTHLY,
        true,
        "2,49 €",
        0,
        100
    )

    AppTheme {
        FeaturePromotionScreen(
            promotedSubscription = promotedSubscription,
            isFreeTrialAvailable = isFreeTrialAvailable,
            onContinueToPurchaseClick = { },
            onExplorePremiumClick = { }
        )
    }
}

@Preview(name = "Portrait", widthDp = 420, heightDp = 700)
@Composable
private fun PreviewFeaturePromotionScreenNoPromotedPrice() {
    AppTheme {
        FeaturePromotionScreen(
            promotedSubscription = null,
            isFreeTrialAvailable = false,
            onContinueToPurchaseClick = { },
            onExplorePremiumClick = { }
        )
    }
}
