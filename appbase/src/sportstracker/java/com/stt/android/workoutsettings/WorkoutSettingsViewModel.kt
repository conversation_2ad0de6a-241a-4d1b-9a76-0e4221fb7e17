package com.stt.android.workoutsettings

import android.content.SharedPreferences
import androidx.lifecycle.viewModelScope
import com.stt.android.FeatureFlags
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.YES
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.FirebaseUserProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.hr.HeartRateDeviceManager
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_SUUNTO_VERTICAL_AD
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_SUUNTO_VERTICAL_AD_DEFAULT
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import javax.inject.Inject

@HiltViewModel
class WorkoutSettingsViewModel @Inject constructor(
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
    private val heartRateDeviceManager: HeartRateDeviceManager,
    private val emarsysAnalytics: EmarsysAnalyticsImpl,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    featureFlags: FeatureFlags,
    coroutinesDispatchers: CoroutinesDispatchers,
    @FeatureTogglePreferences
    private val featureTogglePreferences: SharedPreferences,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
) : BaseWorkoutSettingsViewModel() {

    private val _hasSelectedPremiumVoiceFeedback = MutableStateFlow(false)
    val hasSelectedPremiumVoiceFeedback: Flow<Boolean>
        get() = _hasSelectedPremiumVoiceFeedback

    private val _hasSelectedPremiumGhostTarget = MutableStateFlow(false)
    val hasSelectedPremiumGhostTarget: Flow<Boolean>
        get() = _hasSelectedPremiumGhostTarget

    private val _hasSelectedPremiumFollowRoute = MutableStateFlow(false)
    val hasSelectedPremiumFollowRoute: Flow<Boolean>
        get() = _hasSelectedPremiumFollowRoute

    private val sharedIsSubscribedToPremium = isSubscribedToPremiumUseCase.invoke()
        .shareIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(stopTimeoutMillis = 5000L),
            replay = 1
        )

    val showRequiresPremiumLabels: Flow<Boolean> = sharedIsSubscribedToPremium.map { !it }

    val showAd: Flow<Boolean> =
        sharedIsSubscribedToPremium
            .flatMapLatest { isSubscribed ->
                if (isSubscribed) {
                    flowOf(false)
                } else if (!showSuuntoVerticalBannerAd()) {
                    flowOf(false)
                } else {
                    flowOf(true)
                }
            }
            .flowOn(coroutinesDispatchers.io)

    override fun onSettingsUpdated() {
        super.onSettingsUpdated()
        _hasSelectedPremiumVoiceFeedback.value = hasVoiceFeedback
        _hasSelectedPremiumGhostTarget.value = hasGhostTarget
        _hasSelectedPremiumFollowRoute.value = hasFollowRoute || hasFollowWorkout
    }

    fun trackHrBeltPairedAnalytics() {
        if (heartRateDeviceManager.hasPairedDevice()) {
            val beltName = heartRateDeviceManager.pairedDeviceName
                ?.takeUnless { it.isEmpty() }
                ?: AnalyticsPropertyValue.UNKNOWN
            amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.HR_BELT_PAIRED, beltName)
            emarsysAnalytics.trackStringUserProperty(AnalyticsUserProperty.HR_BELT_PAIRED, beltName)
            firebaseAnalyticsTracker.trackUserProperty(FirebaseUserProperty.HR_BELT_OWNER, YES)
        } else {
            amplitudeAnalyticsTracker.trackUserProperty(AnalyticsUserProperty.HR_BELT_PAIRED, AnalyticsPropertyValue.NO)
            emarsysAnalytics.trackStringUserProperty(AnalyticsUserProperty.HR_BELT_PAIRED, AnalyticsPropertyValue.NO)
        }
    }

    private fun showSuuntoVerticalBannerAd(): Boolean = featureTogglePreferences.getBoolean(
        KEY_SHOW_SUUNTO_VERTICAL_AD,
        KEY_SHOW_SUUNTO_VERTICAL_AD_DEFAULT
    )
}
