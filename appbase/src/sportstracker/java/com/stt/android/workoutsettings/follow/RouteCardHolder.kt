package com.stt.android.workoutsettings.follow

import android.content.res.Resources
import android.view.LayoutInflater
import android.view.ViewGroup
import com.stt.android.controllers.UserSettingsController
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workoutsettings.WorkoutSettingsActivity

class RouteCardHolder(
    inflater: LayoutInflater,
    parent: ViewGroup,
    activity: WorkoutSettingsActivity,
    resources: Resources,
    userSettingsController: UserSettingsController,
    infoModelFormatter: InfoModelFormatter,
    onAddToWatchToggledListener: OnAddToWatchToggledListener?
) : WorkoutSelectionRouteCardHolder(
    inflater,
    parent,
    activity,
    resources,
    userSettingsController,
    infoModelFormatter
)
