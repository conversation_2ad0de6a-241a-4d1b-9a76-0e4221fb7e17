package com.stt.android.home.diary.diarycalendar

import android.content.Context
import com.stt.android.home.diary.diarycalendar.workoutlist.CalendarWorkoutListActivity
import java.time.LocalDate

class BrandDiaryCalendarToDayViewNavigation : DiaryCalendarToDayViewNavigation {

    override fun navigateToDayView(context: Context, date: LocalDate) {
        val intent = CalendarWorkoutListActivity.newIntent(
            context,
            date,
            null,
            null,
            null,
            null
        )
        context.startActivity(intent)
    }
}
