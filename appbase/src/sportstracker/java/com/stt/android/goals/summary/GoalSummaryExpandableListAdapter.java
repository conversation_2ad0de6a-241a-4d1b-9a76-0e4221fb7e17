package com.stt.android.goals.summary;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.stt.android.R;
import com.stt.android.ThemeColors;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.data.TimeUtils;
import com.stt.android.data.goaldefinition.GoalDefinitionExtKt;
import com.stt.android.domain.goaldefinition.GoalDefinition;
import com.stt.android.domain.user.Goal;
import com.stt.android.domain.user.GoalSummary;
import com.stt.android.utils.CalendarProvider;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

class GoalSummaryExpandableListAdapter extends BaseExpandableListAdapter {
    private static class ViewTag {
        TextView timeFrame;
        TextView summaryTime;
        TextView workouts;
        LinearProgressIndicator summaryProgress;

        ImageView firstActivity;
        ImageView secondActivity;
        ImageView thirdActivity;
        TextView extraActivity;
    }

    private final Context context;
    private final Resources resources;
    private final CalendarProvider calendarProvider;
    private final int currentYear;
    private final LayoutInflater inflater;
    private final UserSettingsController userSettingsController;
    private final String[] months;

    private GoalSummary goalSummary;
    private GoalDefinition goalDefinition;
    private int target;

    private boolean showGoalsBeforeTimeBegins;
    private List<List<Goal>> goalsByYear;
    private List<List<Goal>> goalsBeforeTimeBeginsByYear;

    GoalSummaryExpandableListAdapter(Context context, UserSettingsController userSettingsController,
        CalendarProvider calendarProvider) {
        this.context = context;
        resources = context.getResources();
        this.calendarProvider = calendarProvider;
        currentYear = calendarProvider.getCalendar().get(Calendar.YEAR);
        this.userSettingsController = userSettingsController;
        inflater = LayoutInflater.from(context);
        months = resources.getStringArray(R.array.abbreviated_months);
        showGoalsBeforeTimeBegins = false;
    }

    private List<List<Goal>> categorizeGoalsByYear(List<Goal> goals) {
        List<List<Goal>> goalsByYear = new ArrayList<>();
        Calendar calendar = calendarProvider.getCalendar();
        int year = 0;
        for (Goal goal : goals) {
            calendar.setTimeInMillis(goal.getStartTime());
            List<Goal> goalsOfTheYear;
            int goalStartYear = calendar.get(Calendar.YEAR);
            if (year == goalStartYear) {
                goalsOfTheYear = goalsByYear.get(goalsByYear.size() - 1);
            } else {
                goalsOfTheYear = new ArrayList<>();
                goalsByYear.add(goalsOfTheYear);
                year = goalStartYear;
            }
            goalsOfTheYear.add(goal);
        }
        return goalsByYear;
    }

    boolean getShowGoalsBeforeTimeBegins() {
        return showGoalsBeforeTimeBegins;
    }
    void setShowGoalsBeforeTimeBegins(boolean show) {
        showGoalsBeforeTimeBegins = show;
        notifyDataSetChanged();
    }

    void setGoalSummary(@Nullable GoalSummary goalSummary) {
        if (this.goalSummary == goalSummary) return;

        if (goalSummary != null) {
            goalDefinition = goalSummary.getGoalDefinition();
            goalsByYear = categorizeGoalsByYear(goalSummary.getGoals());
            goalsBeforeTimeBeginsByYear =
                categorizeGoalsByYear(goalSummary.getGoalsBeforeTimeBegins());

            if (goalDefinition.getPeriod() == GoalDefinition.Period.CUSTOM) {
                // as per UI spec, for custom goals, the progress bars will be scaled against the
                // best week
                target = goalSummary.getMaxAchieved();
                if (target == 0) {
                    target = goalDefinition.getTarget();
                }
            } else {
                target = goalDefinition.getTarget();
            }
        } else {
            goalDefinition = null;
            goalsByYear = null;
            goalsBeforeTimeBeginsByYear = null;
        }

        this.goalSummary = goalSummary;
        notifyDataSetChanged();
    }

    @Override
    public int getGroupCount() {
        int count = goalsByYear == null ? 0 : goalsByYear.size();
        if (goalsBeforeTimeBeginsByYear != null && showGoalsBeforeTimeBegins) {
            count += goalsBeforeTimeBeginsByYear.size();
        }
        return count;
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        if (goalsByYear == null) {
            return 0;
        }
        List group = getGroup(groupPosition);
        return group == null ? 0 : group.size();
    }

    @Override
    public List<Goal> getGroup(int groupPosition) {
        int size = goalsByYear.size();
        return groupPosition < size ? goalsByYear.get(groupPosition)
            : goalsBeforeTimeBeginsByYear.get(groupPosition - size);
    }

    @Override
    public Goal getChild(int groupPosition, int childPosition) {
        return getGroup(groupPosition).get(childPosition);
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return groupPosition * 100 + childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return false;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView,
        ViewGroup parent) {
        ConstraintLayout groupView =
            (ConstraintLayout) (convertView == null ? inflater.inflate(
                R.layout.expandable_list_group_view,
                parent, false) : convertView);
        TextView date = groupView.findViewById(R.id.date);
        int size = goalsByYear.size();
        if (groupPosition < size) {
            date.setText(Integer.toString(currentYear - groupPosition));
        } else {
            int year = currentYear - groupPosition;
            if (size > 0) {
                ++year;
            }
            date.setText(resources.getString(R.string.goal_summary_earlier, year));
        }

        ImageView openIndicator = groupView.findViewById(R.id.openIndicator);
        View bottomDivider = groupView.findViewById(R.id.bottomDivider);

        float scaleY = 1f;
        float elevation = 0f;
        int bottomDividerVisibility = View.GONE;

        if (isExpanded) {
            scaleY = -1;
            bottomDividerVisibility = View.VISIBLE;
        } else {
            if (groupPosition == getGroupCount() - 1) {
                elevation = resources.getDimension(R.dimen.size_spacing_xxsmall);
            }
        }

        openIndicator.setScaleY(scaleY);
        groupView.setElevation(elevation);
        bottomDivider.setVisibility(bottomDividerVisibility);

        return groupView;
    }

    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isLastChild,
        View convertView, ViewGroup parent) {
        View view;
        ViewTag viewTag;
        if (convertView == null) {
            view = inflater.inflate(R.layout.workout_summary_list_item, parent, false);

            viewTag = new ViewTag();
            viewTag.timeFrame = view.findViewById(R.id.workoutSummaryTimeFrame);
            viewTag.summaryProgress = view.findViewById(R.id.summaryProgress);
            viewTag.summaryTime = view.findViewById(R.id.summaryTotalValue);
            viewTag.workouts = view.findViewById(R.id.totalWorkoutsValue);
            viewTag.firstActivity = view.findViewById(R.id.firstActivityIcon);
            viewTag.secondActivity = view.findViewById(R.id.secondActivityIcon);
            viewTag.thirdActivity = view.findViewById(R.id.thirdActivityIcon);
            viewTag.extraActivity = view.findViewById(R.id.extraActivityTypes);
            view.findViewById(R.id.workoutItemSummaryContainer).setVisibility(View.INVISIBLE);
            view.setTag(viewTag);
        } else {
            view = convertView;
            viewTag = (ViewTag) view.getTag();
        }

        Goal goal = getChild(groupPosition, childPosition);
        Resources resources = context.getResources();

        viewTag.timeFrame.setText(buildTimeFrameText(goal));

        // if the progress bar is empty, it means the user hasn't done anything
        // so, if the user has achieved less than 1%, we force it to show 1%
        int progress = target == 0 ? 100 : 100 * goal.achieved / target;
        if (progress == 0 && goal.achieved > 0) {
            progress = 1;
        }
        int progressBarColor = ThemeColors.resolveColor(context, android.R.attr.colorAccent);
        if (progress >= 100) {
            progressBarColor = resources.getColor(R.color.text_progressbar_full);
        }
        viewTag.summaryProgress.setProgress(progress);
        viewTag.summaryProgress.setIndicatorColor(progressBarColor);
        if (goalDefinition.getType() == GoalDefinition.Type.WORKOUTS) {
            viewTag.summaryTime.setText(
                resources.getQuantityString(R.plurals.goal_workouts, goal.workoutIds.size(),
                    goal.workoutIds.size()));
            viewTag.workouts.setText(null);
        } else {
            viewTag.summaryTime.setText(GoalDefinitionExtKt.getAmountString(
                goalDefinition.getType(), context, goal.achieved,
                    userSettingsController.getSettings().getMeasurementUnit()));
            viewTag.workouts.setText(
                resources.getQuantityString(R.plurals.goal_workouts, goal.workoutIds.size(),
                    goal.workoutIds.size()));
        }

        int size = goal.activities.size();
        viewTag.firstActivity.setImageDrawable(
            size > 0 ? goal.activities.get(0).getIcon(context) : null);
        viewTag.secondActivity.setImageDrawable(
            size > 1 ? goal.activities.get(1).getIcon(context) : null);
        viewTag.thirdActivity.setImageDrawable(
            size > 2 ? goal.activities.get(2).getIcon(context) : null);
        viewTag.extraActivity.setText(size > 3 ? String.format(Locale.getDefault(), "+%d", size - 3) : null);

        return view;
    }

    private String buildTimeFrameText(Goal goal) {
        long now = System.currentTimeMillis();
        long startTime = goal.getStartTime();
        long endTime = goal.getEndTime();
        if (now >= startTime && now <= endTime) {
            // current period
            return context.getResources()
                .getString(goalDefinition.getPeriod() == GoalDefinition.Period.MONTHLY
                    ? R.string.this_month : R.string.this_week);
        }

        if (now > endTime && now <= (endTime + (endTime - startTime))) {
            // previous period
            // goal.endTime - goal.startTime is the time for 1 period
            return context.getResources()
                .getString(goalDefinition.getPeriod() == GoalDefinition.Period.MONTHLY
                    ? R.string.last_month : R.string.last_week);
        }

        if (goal.goalDefinition.getPeriod() == GoalDefinition.Period.MONTHLY) {
            Calendar calendar = calendarProvider.getCalendar();
            calendar.setTimeInMillis(startTime);
            return months[calendar.get(Calendar.MONTH)];
        } else {
            LocalDate date = TimeUtils.epochToLocalZonedDateTime(startTime).toLocalDate();
            return TimeUtils.dateWithoutYearFormatter(Locale.getDefault()).format(date);
        }
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }
}
