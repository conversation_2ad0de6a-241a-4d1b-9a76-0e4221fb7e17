package com.stt.android.goals.summary;

import android.content.Context;
import android.content.res.Resources;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import com.stt.android.R;
import com.stt.android.data.TimeUtils;
import com.stt.android.data.goaldefinition.GoalDefinitionExtKt;
import com.stt.android.databinding.GoalSummaryHeaderBinding;
import com.stt.android.domain.goaldefinition.GoalDefinition;
import com.stt.android.domain.user.Goal;
import com.stt.android.domain.user.GoalSummary;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.workout.ActivityType;
import java.util.List;
import java.util.Locale;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;

public class GoalSummaryHeaderView extends RelativeLayout {

    private GoalSummaryHeaderBinding binding;

    public GoalSummaryHeaderView(Context context) {
        super(context);
        initialize(context);
    }

    public GoalSummaryHeaderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initialize(context);
    }

    public GoalSummaryHeaderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initialize(context);
    }

    public GoalSummaryHeaderView(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initialize(context);
    }

    private void initialize(Context context) {
        binding = GoalSummaryHeaderBinding.inflate(LayoutInflater.from(context), this);
    }

    public void setGoalSummary(GoalSummary goalSummary, MeasurementUnit measurementUnit) {
        GoalDefinition goalDefinition = goalSummary.getGoalDefinition();
        Resources resources = getResources();
        if (goalDefinition.getPeriod() == GoalDefinition.Period.CUSTOM) {
            binding.title.setText(String.format(
                Locale.getDefault(), "%s %s", GoalDefinitionExtKt.getTitle(goalDefinition, resources),
                GoalDefinitionExtKt.getAmountString(goalDefinition.getType(), getContext(),
                    goalDefinition.getTarget(), measurementUnit)));
        } else {
            binding.title.setText(String.format(Locale.getDefault(), "%s %s %s", GoalDefinitionExtKt.toString(goalDefinition.getPeriod(), resources),
                GoalDefinitionExtKt.toString(goalDefinition.getType(), resources),
                GoalDefinitionExtKt.getAmountString(goalDefinition.getType(),
                    getContext(), goalDefinition.getTarget(), measurementUnit)));
        }

        List<ActivityType> activityTypes = GoalDefinitionExtKt.getActivities(goalDefinition);
        int size = activityTypes.size();

        ImageView[] activityIcons = new ImageView[] {
            binding.firstActivityIcon,
            binding.secondActivityIcon,
            binding.thirdActivityIcon,
            binding.fourthActivityIcon,
            binding.fifthActivityIcon
        };

        if (size > 0 && size <= activityIcons.length) {
            binding.activities.setText(null);
            for (int i = 0; i < activityIcons.length; ++i) {
                if (i < size) {
                    activityIcons[i].setImageResource(activityTypes.get(i).getIconId());
                    activityIcons[i].setVisibility(View.VISIBLE);
                } else {
                    activityIcons[i].setVisibility(View.GONE);
                }
            }
        } else {
            if (size == 0) {
                binding.activities.setText(R.string.goal_summary_all_activities);
            } else {
                binding.activities.setText(
                    resources.getQuantityString(R.plurals.goal_summary_activities_count, size,
                        size));
            }
            for (ImageView imageView : activityIcons) {
                imageView.setVisibility(View.GONE);
            }
        }

        switch (goalDefinition.getPeriod()) {
            case WEEKLY:
                binding.currentTitle.setText(R.string.goal_this_week);
                binding.label1.setText(R.string.goal_summary_success_week);
                binding.periodAverageLabel.setText(R.string.goal_summary_weekly_average);
                binding.periodBestLabel.setText(R.string.goal_summary_weekly_best);
                break;
            case MONTHLY:
                binding.currentTitle.setText(R.string.goal_this_month);
                binding.label1.setText(R.string.goal_summary_success_month);
                binding.periodAverageLabel.setText(R.string.goal_summary_monthly_average);
                binding.periodBestLabel.setText(R.string.goal_summary_monthly_best);
                break;
            case CUSTOM:
                binding.currentTitle.setText(R.string.goal_summary_progress);
                binding.label1.setText(R.string.goal_summary_average_workout);
                binding.periodAverageLabel.setText(R.string.goal_summary_weekly_average);
                binding.periodBestLabel.setText(R.string.goal_summary_weekly_best);
                break;
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter
            .ofLocalizedDate(FormatStyle.LONG);
        ZonedDateTime startDateTime =
            TimeUtils.epochToLocalZonedDateTime(goalDefinition.getStartTime());

        boolean isCustomPeriod = goalDefinition.getPeriod() == GoalDefinition.Period.CUSTOM;
        if (isCustomPeriod) {
            ZonedDateTime endDateTime =
                TimeUtils.epochToLocalZonedDateTime(
                    GoalDefinitionExtKt.getEndTimeOrMaxValue(goalDefinition));
            binding.startDate.setText(
                String.format(Locale.getDefault(), "%s - %s", startDateTime.format(dateTimeFormatter),
                    endDateTime.format(dateTimeFormatter)));
        } else {
            binding.startDate.setText(
                String.format(Locale.getDefault(), "%s %s",
                    resources.getString(R.string.goal_period_start_from),
                    startDateTime.format(dateTimeFormatter)));
        }

        int visibility = isCustomPeriod ? View.GONE : View.VISIBLE;
        binding.lastDivider.setVisibility(visibility);
        binding.totalLabel.setVisibility(visibility);
        binding.total.setVisibility(visibility);
        binding.currentStreakLabel.setVisibility(visibility);
        binding.currentStreak.setVisibility(visibility);
        binding.longestStreakLabel.setVisibility(visibility);
        binding.longestStreak.setVisibility(visibility);

        int achieved;
        int target = goalDefinition.getTarget();
        GoalDefinition.Type type = goalDefinition.getType();
        List<Goal> goals = goalSummary.getGoals();
        if (goalDefinition.getPeriod() == GoalDefinition.Period.CUSTOM) {
            achieved = goalSummary.getTotalAchieved();

            int workouts = 0;
            int totalTime = 0;
            int goalCount = goals.size();
            for (int i = 0; i < goalCount; ++i) {
                Goal goal = goals.get(i);
                workouts += goal.workoutIds.size();
                totalTime += goal.totalTime;
            }

            if (type == GoalDefinition.Type.WORKOUTS) {
                binding.value1.setText(
                    GoalDefinitionExtKt.getAmountString(GoalDefinition.Type.DURATION, getContext(),
                    workouts == 0 ? 0 : totalTime / workouts, measurementUnit));
            } else {
                binding.value1.setText(GoalDefinitionExtKt.getAmountString(type, getContext(),
                    workouts == 0 ? 0 : goalSummary.getTotalAchieved() / workouts,
                    measurementUnit));
            }

            binding.total.setText(null);
            binding.currentStreak.setText(null);
            binding.longestStreak.setText(null);
        } else {
            achieved = goals.size() == 0 ? 0 : goals.get(0).achieved;

            binding.value1.setText(
                String.format(Locale.getDefault(), "%d/%d", goalSummary.getSuccessfulGoals(), goalSummary.getTotal()));

            binding.total.setText(GoalDefinitionExtKt.getAmountString(goalDefinition.getType(),
                getContext(), goalSummary.getTotalAchieved(), measurementUnit));
            binding.currentStreak.setText(Integer.toString(goalSummary.getCurrentStreak()));
            binding.longestStreak.setText(Integer.toString(goalSummary.getLongestStreak()));
        }
        binding.currentAchievedTarget.setText(GoalDefinitionExtKt.getAmountTargetString(
            goalDefinition, getContext(), achieved, measurementUnit));
        binding.currentProgress.setProgress(target == 0 ? 100 : 100 * achieved / target);

        if (type == GoalDefinition.Type.WORKOUTS) {
            binding.periodAverageValue.setText(String.format(Locale.getDefault(), "%.1f", goalSummary.getAverageAchieved()));
        } else {
            binding.periodAverageValue.setText(GoalDefinitionExtKt.getAmountString(goalDefinition.getType(),
                getContext(), goalSummary.getAverageAchieved(), measurementUnit));
        }
        binding.periodBestValue.setText(GoalDefinitionExtKt.getAmountString(goalDefinition.getType(),
            getContext(), goalSummary.getMaxAchieved(), measurementUnit));
    }
}
