package com.stt.android.common.ui

import androidx.core.widget.ContentLoadingProgressBar
import androidx.databinding.BindingAdapter

/**
 * Binding adapter for showing and hiding [ContentLoadingProgressBar]
 */
@BindingAdapter(value = ["show"], requireAll = true)
fun showContentLoadingProgressBar(progressBar: ContentLoadingProgressBar, show: <PERSON>olean) {
    if (show) progressBar.show() else progressBar.hide()
}
