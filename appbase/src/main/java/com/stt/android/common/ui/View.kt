package com.stt.android.common.ui

import android.view.View
import android.view.ViewTreeObserver
import androidx.core.view.doOnDetach
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import java.lang.ref.WeakReference
import kotlin.coroutines.resume

/**
 * Suspends execution until this View's [ViewTreeObserver.OnGlobalLayoutListener] is invoked.
 * Useful when you want to get the size of the view after it's been laid out.
 */
suspend fun View.awaitGlobalLayout() = suspendCancellableCoroutine<Unit> { cont ->
    doOnDetach {
        cont.cancel()
    }
    val viewRef = WeakReference(this)
    val listener = object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            viewRef.get()?.let { view ->
                if (view.isAttachedToWindow) {
                    Timber.v("onGlobalLayout() has been invoked for view: $view. Removing listener.")
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (cont.isActive) {
                        cont.resume(Unit)
                    } else {
                        cont.cancel()
                    }
                } else {
                    cont.cancel()
                }
            }
        }
    }
    cont.invokeOnCancellation {
        viewRef.get()?.viewTreeObserver?.removeOnGlobalLayoutListener(listener)
    }

    if (isAttachedToWindow) {
        Timber.v("Registering OnGlobalLayoutListener for view: $this")
        viewRef.get()?.viewTreeObserver?.addOnGlobalLayoutListener(listener)
    } else {
        cont.cancel()
    }
}
