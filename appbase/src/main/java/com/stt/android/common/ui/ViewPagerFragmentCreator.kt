package com.stt.android.common.ui

import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.annotation.StyleRes
import androidx.fragment.app.Fragment

/**
 * Interface for creating fragments that should be used with ViewPager.
 * See DiaryPagerAdapterNew for an example how to use this interface.
 */
interface ViewPagerFragmentCreator {
    /**
     * Creates a fragment instance.
     * @return Returns an instance of the fragment
     */
    fun create(args: Bundle? = null): Fragment

    /**
     * Get the resource ID of the icon to be used for the tab of this fragment
     */
    @DrawableRes
    fun getIconId(): Int

    /**
     * Get the resouce ID of the style to be used in this fragment
     */
    @StyleRes
    fun getTabStyleId(): Int

    /**
     * Get the tab type of the fragment
     */
    fun getTabTypeForAnalytics(): String

    /**
     * Get content description for tab. Used by screen readers and automated UI tests.
     */
    @StringRes
    fun getTabContentDescriptionStringResId(): Int

    /**
     * This will be used to decide if we show the time range spinner in the toolbar or not
     */
    fun hasTimeRange(): <PERSON><PERSON>an
}
