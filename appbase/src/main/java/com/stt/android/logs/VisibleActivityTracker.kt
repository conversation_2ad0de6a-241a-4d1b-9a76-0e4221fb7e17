package com.stt.android.logs

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.os.Bundle
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VisibleActivityTracker @Inject constructor() : ActivityLifecycleCallbacks {

    private var _visibleActivity: WeakReference<Activity>? = null

    val visibleActivity: Activity?
        get() = _visibleActivity?.get()

    // When called from an activity, call it before activity started.
    // Don't use this property in a transparent themed activity,
    // since the parent activity will be started first, and this will always return true.
    val isForeground: Boolean
        get() = visibleActivity != null

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        logMessage("onActivityCreated: " + activity.localClassName)
        savedInstanceState?.keySet()?.forEach { key ->
            logMessage("savedInstanceState: $key\n")
        }
    }

    override fun onActivityStarted(activity: Activity) {
        logMessage("onActivityStarted: " + activity.localClassName)
        if (activity.javaClass != visibleActivity?.javaClass) {
            _visibleActivity = WeakReference(activity)
        }
    }

    override fun onActivityResumed(activity: Activity) {
        logMessage("onActivityResumed: " + activity.localClassName)
    }

    override fun onActivityPaused(activity: Activity) {
        logMessage("onActivityPaused: " + activity.localClassName)
    }

    override fun onActivityStopped(activity: Activity) {
        logMessage("onActivityStopped: " + activity.localClassName)
        if (activity.javaClass == visibleActivity?.javaClass) {
            _visibleActivity = null
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        logMessage("onActivitySaveInstanceState: " + activity.localClassName)
        for (key in outState.keySet()) {
            logMessage("outState: $key\n")
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        logMessage("onActivityDestroyed: " + activity.localClassName)
    }

    private fun logMessage(message: String) {
        Timber.d(message)
        FirebaseCrashlytics.getInstance().log(message)
    }
}
