package com.stt.android.di

import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.domain.refreshable.Refreshable
import com.stt.android.domain.workout.RunPushSyncRefreshable
import com.stt.android.home.SyncFeedData
import com.stt.android.refreshable.RefreshablesInitialRunDoneStore
import com.stt.android.refreshable.SharedPrefsRefreshablesInitialRunDoneStore
import com.stt.android.remoteconfig.RemoteConfigSync
import com.stt.android.session.StartupSync
import dagger.Binds
import dagger.Module

@Module
abstract class RefreshablesModule {
    @Binds
    abstract fun bindRefreshablesInitialRunDoneStore(
        sharedPrefsRefreshablesInitialRunDoneStore: SharedPrefsRefreshablesInitialRunDoneStore
    ): RefreshablesInitialRunDoneStore

    companion object {
        fun provideCommonRefreshables(
            syncFeedData: SyncFeedData,
            startupSync: StartupSync,
            runPushSyncRefreshable: RunPushSyncRefreshable,
            emarsysAnalytics: EmarsysAnalyticsImpl,
            remoteConfigSync: RemoteConfigSync,
        ): Set<Refreshable> {
            val refreshables: MutableSet<Refreshable> = LinkedHashSet()

            // Note that the order refreshables are added will be the order they're executed
            refreshables.add(startupSync)
            // first we upload locally changed workout data
            refreshables.add(runPushSyncRefreshable)
            // then we update the feed
            refreshables.add(syncFeedData)
            // check emarsys userId
            refreshables.add(emarsysAnalytics)
            // update remote config
            refreshables.add(remoteConfigSync)
            return refreshables
        }
    }
}
