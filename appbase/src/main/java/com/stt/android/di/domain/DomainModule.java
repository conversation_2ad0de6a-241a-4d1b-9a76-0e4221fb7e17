package com.stt.android.di.domain;

import com.stt.android.di.ConnectivityModule;
import com.stt.android.domain.di.ComputationThread;
import com.stt.android.domain.di.IoThread;
import com.stt.android.domain.di.MainThread;

import dagger.Module;
import dagger.Provides;
import io.reactivex.Scheduler;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

@SuppressWarnings("WeakerAccess")
@Module
@ConnectivityModule
public abstract class DomainModule {
    @Provides
    @IoThread
    public static Scheduler provideIoScheduler() {
        return Schedulers.io();
    }

    @Provides
    @ComputationThread
    public static Scheduler provideComputationScheduler() {
        return Schedulers.computation();
    }

    @Provides
    @MainThread
    public static Scheduler provideMainScheduler() {
        return AndroidSchedulers.mainThread();
    }
}
