package com.stt.android.glance.dataloader

import android.content.SharedPreferences
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.glance.data.MonthlyCalendarHomeWidgetInfo
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.ui.map.selection.toGetWorkoutStatisticsWithSummaryUseCaseParams
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.CalendarUtils.buildDayOfWeekLabels
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import com.stt.android.utils.iterator
import java.time.LocalDate
import javax.inject.Inject

class MonthlyCalendarHomeWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase,
    private val diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder,
    private val sharedPreferences: SharedPreferences,
    private val calendarProvider: CalendarProvider,
) {
    suspend fun load(): MonthlyCalendarHomeWidgetInfo {
        val granularity = MyTracksGranularity.Type.THIS_MONTH
        val username = currentUserController.username
        val showPredictions = sharedPreferences.getBoolean(
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT,
        )
        val getWorkoutParams = granularity.toGetWorkoutStatisticsWithSummaryUseCaseParams(
            username,
            showPredictions,
            calendarProvider
        )
        val summary = getWorkoutStatisticsWithSummaryUseCase(getWorkoutParams)
        val weekDayLabels = buildDayOfWeekLabels(
            LocalDate.now(),
            calendarProvider.getDayOfWeekField()
        )
        val dataForGranularity = createDiaryBubbleData(
            data = summary,
            startDate = getWorkoutParams.firstDay,
            endDate = getWorkoutParams.endDay,
            weekDayLabels = weekDayLabels,
            diaryCalendarListContainerBuilder = diaryCalendarListContainerBuilder
        )

        return MonthlyCalendarHomeWidgetInfo(
            calendarProvider = calendarProvider,
            bubbleData = dataForGranularity,
        )
    }

    private fun createDiaryBubbleData(
        data: DailyWorkoutStatisticsWithSummary,
        startDate: LocalDate,
        endDate: LocalDate,
        weekDayLabels: List<String>,
        diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder
    ) = DiaryBubbleData(
        startDate = startDate,
        endDate = endDate,
        bubbles = createCalendarData(data, startDate, endDate, diaryCalendarListContainerBuilder),
        weekDayLabels = weekDayLabels,
        onMonthClicked = {}
    )

    private fun createCalendarData(
        data: DailyWorkoutStatisticsWithSummary,
        startDate: LocalDate,
        endDate: LocalDate,
        diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder
    ): List<DiaryBubbleContainer> {
        val bubbleList = mutableListOf<DiaryBubbleContainer>()
        for (day in startDate..endDate) {
            val diaryCalendarDailyData = data.dailyData[day] ?: DiaryCalendarDailyData.EMPTY
            bubbleList.add(
                DiaryBubbleContainer(
                    id = day.toString(),
                    startDate = day,
                    bubbleType = diaryCalendarListContainerBuilder.getBubbleType(
                        day,
                        diaryCalendarDailyData
                    ),
                    dayData = diaryCalendarDailyData,
                    onDateClicked = {}
                )
            )
        }
        return bubbleList
    }
}
