package com.stt.android.ui.components

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import com.stt.android.R
import com.stt.android.domain.workout.ActivityType

class DistanceValueDialogFragment :
    DialogFragment(),
    InlineEditor.InlineEditorActionListener<Double> {

    private var selectedListener: DistanceValueSelectedListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectedListener = (activity as? DistanceValueSelectedListener)
    }

    @SuppressLint("InflateParams")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = layoutInflater.inflate(R.layout.dialog_distance_value, null)
        val distanceEditor = view.findViewById<DistanceEditor>(R.id.distanceEditor)
        val initialValue = arguments?.getDouble(INITIALVALUE)
        val titleRes = arguments?.getInt(CUSTOM_TITLE, 0)?.takeIf { it != 0 }
            ?: R.string.distance
        val activityTypeId = arguments?.getInt(ACTIVITY_TYPE_ID)

        val isForceShortDistance = arguments?.getBoolean(IS_FORCE_SHORT_DISTANCE) ?: false

        distanceEditor.setInitialValues(
            activityTypeId,
            isForceShortDistance,
            this
        )

        val maxDistanceArg = arguments?.getDouble(MAX_DISTANCE, -1.0)
        if (maxDistanceArg != null && maxDistanceArg > 0) {
            distanceEditor.setMaxDistance(maxDistanceArg)
        }
        distanceEditor.value = initialValue
        val builder = AlertDialog.Builder(requireContext())
            .setPositiveButton(R.string.ok) { _, _ ->
                onDialogOkClicked(distanceEditor.value)
            }
            .setNegativeButton(R.string.cancel, null)
            .setView(view)
            .setTitle(titleRes)
        return builder.create()
    }

    private fun onDialogOkClicked(value: Double?) {
        if (value != null) {
            // selectedListener in case this Dialog called from an activity
            selectedListener?.distanceUpdatedFromDialog(tag, value)
            tag?.let {
                setFragmentResult(it, bundleOf(it to value))
            }
            dismiss()
        }
    }

    interface DistanceValueSelectedListener {
        fun distanceUpdatedFromDialog(tag: String?, value: Double)
    }

    companion object {
        private const val INITIALVALUE = "initialvalue"
        private const val MAX_DISTANCE = "maxdistance"
        private const val IS_FORCE_SHORT_DISTANCE = "isForceShortDistance"
        private const val CUSTOM_TITLE = "customtitle"
        private const val ACTIVITY_TYPE_ID = "activityTypeId"

        @JvmStatic
        @JvmOverloads
        fun newInstance(
            initialValue: Double,
            maxDistance: Double? = null,
            @StringRes titleRes: Int = 0,
            activityType: ActivityType? = null,
            isForceShortDistance: Boolean = false,
        ): DistanceValueDialogFragment {
            val dialogFragment = DistanceValueDialogFragment()
            dialogFragment.arguments = Bundle().apply {
                putDouble(INITIALVALUE, initialValue)
                maxDistance?.let { putDouble(MAX_DISTANCE, it) }
                putInt(CUSTOM_TITLE, titleRes)
                if (activityType != null) {
                    // in some cases, it is not possible to have activity type ex: ST goal
                    // try to edit a gooal in ST, it is required to use Distance Editor but
                    // we don't have any activity type
                    putInt(ACTIVITY_TYPE_ID, activityType.id)
                }
                putBoolean(IS_FORCE_SHORT_DISTANCE, isForceShortDistance)
            }
            return dialogFragment
        }
    }

    override fun onInlineEditorDoneClicked(value: Double?) {
        onDialogOkClicked(value)
    }
}
