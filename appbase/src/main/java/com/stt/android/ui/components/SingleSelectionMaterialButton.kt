package com.stt.android.ui.components

import android.content.Context
import android.util.AttributeSet
import com.google.android.material.button.MaterialButton

/**
 * A MaterialButton that can be used in a MaterialButtonToggleGroup and cannot be unselected by
 * tapping twice. This button can only be unselected by tapping another button in the same group
 * (or programmatically).
 */
class SingleSelectionMaterialButton
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : MaterialButton(context, attrs, defStyleAttr) {

    override fun toggle() {
        if (!isChecked) {
            super.toggle()
        }
    }
}
