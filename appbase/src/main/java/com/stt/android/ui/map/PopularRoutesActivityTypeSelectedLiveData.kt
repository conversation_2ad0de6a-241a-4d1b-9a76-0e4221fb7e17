package com.stt.android.ui.map

import android.content.SharedPreferences
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import com.stt.android.di.MapPreferences
import com.stt.android.domain.workout.ActivityType
import com.stt.android.models.MapSelectionModel
import com.stt.android.utils.STTConstants.MapPreferences.KEY_SELECTED_POPULAR_ROUTE_ACTIVITY_TYPES
import javax.inject.Inject

class PopularRoutesActivityTypeSelectedLiveData
@Inject constructor(
    private val mapSelectionModel: MapSelectionModel,
    @MapPreferences private val sharedPreferences: SharedPreferences
) : LiveData<List<ActivityType>?>() {

    private val listener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
        if (key == KEY_SELECTED_POPULAR_ROUTE_ACTIVITY_TYPES) {
            // The new value will be available from mapSelectionModel only after the
            // call sequence that triggered the shared preference change has finished.
            Handler(Looper.getMainLooper()).post { updateValue() }
        }
    }

    override fun onActive() {
        updateValue()
        sharedPreferences.registerOnSharedPreferenceChangeListener(listener)
    }

    override fun onInactive() {
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(listener)
    }

    private fun updateValue() {
        mapSelectionModel.selectedPopularRoutesActivityTypes.let {
            if (value != it) value = it
        }
    }
}
