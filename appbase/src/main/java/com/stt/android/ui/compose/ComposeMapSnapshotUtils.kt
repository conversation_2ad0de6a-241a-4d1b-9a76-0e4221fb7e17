package com.stt.android.ui.compose

import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntSize
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.google.android.gms.maps.model.LatLng
import com.mapbox.geojson.MultiPolygon
import com.mapbox.geojson.Polygon
import com.mapbox.geojson.gson.GeometryGeoJson
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.routes.TopRoute
import com.stt.android.home.dashboardv2.widgets.DashboardMapSnapshotData
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.maps.MapType
import com.stt.android.remote.ComposeMapSnapshotOkHttpClient
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.IOException
import timber.log.Timber
import javax.inject.Inject

/**
 * FOR GOOGLE MAPS: You need to add a MapSnapshotterFragment in your fragment layout with the exact
 * same size as the map snapshots that will be displayed. This allows generating the snapshots when
 * using Google Maps.
 *
 * If you show multiple sizes of snapshots, you will need multiple instances of MapSnapshotterFragment.
 * See: https://github.com/SportsTrackingTechnologies/STTAndroid/wiki/Displaying-and-generating-map-snapshots
 */
@Composable
fun WorkoutMapSnapshot(
    id: Int,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
) {
    MapSnapshot(
        specFactory = { width: Int, height: Int ->
            MapSnapshotSpec.Workout(
                workoutId = id,
                width = width,
                height = height,
                explicitProviderName = explicitProviderName,
                explicitMapType = explicitMapType,
            )
        },
        placeholder = placeholder,
        backgroundColor = backgroundColor,
        modifier = modifier,
    )
}

/**
 * FOR GOOGLE MAPS: You need to add a MapSnapshotterFragment in your fragment layout with the exact
 * same size as the map snapshots that will be displayed. This allows generating the snapshots when
 * using Google Maps.
 *
 * If you show multiple sizes of snapshots, you will need multiple instances of MapSnapshotterFragment.
 * See: https://github.com/SportsTrackingTechnologies/STTAndroid/wiki/Displaying-and-generating-map-snapshots
 */
@Composable
fun WorkoutPolylineMapSnapshot(
    polyline: String,
    disableZoomToBounds: Boolean,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
) {
    MapSnapshot(
        specFactory = { width: Int, height: Int ->
            MapSnapshotSpec.WorkoutPolyline(
                polyline = polyline,
                width = width,
                height = height,
                explicitProviderName = explicitProviderName,
                explicitMapType = explicitMapType,
                disableZoomToBounds = disableZoomToBounds,
            )
        },
        placeholder = placeholder,
        backgroundColor = backgroundColor,
        modifier = modifier,
    )
}

/**
 * FOR GOOGLE MAPS: You need to add a MapSnapshotterFragment in your fragment layout with the exact
 * same size as the map snapshots that will be displayed. This allows generating the snapshots when
 * using Google Maps.
 *
 * If you show multiple sizes of snapshots, you will need multiple instances of MapSnapshotterFragment.
 * See: https://github.com/SportsTrackingTechnologies/STTAndroid/wiki/Displaying-and-generating-map-snapshots
 */
@Composable
fun RouteMapSnapshot(
    routeId: String,
    segmentsModifiedDateMillis: Long,
    showTurnByTurnWaypoints: Boolean,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
) {
    MapSnapshot(
        specFactory = { width: Int, height: Int ->
            MapSnapshotSpec.Route(
                routeId = routeId,
                segmentsModifiedDateMillis = segmentsModifiedDateMillis,
                showTurnByTurnWaypoints = showTurnByTurnWaypoints,
                width = width,
                height = height,
                explicitProviderName = explicitProviderName,
                explicitMapType = explicitMapType
            )
        },
        placeholder = placeholder,
        backgroundColor = backgroundColor,
        modifier = modifier
    )
}

/**
 * FOR GOOGLE MAPS: You need to add a MapSnapshotterFragment in your fragment layout with the exact
 * same size as the map snapshots that will be displayed. This allows generating the snapshots when
 * using Google Maps.
 *
 * If you show multiple sizes of snapshots, you will need multiple instances of MapSnapshotterFragment.
 * See: https://github.com/SportsTrackingTechnologies/STTAndroid/wiki/Displaying-and-generating-map-snapshots
 *
 * NOTE:
 * - Supports only Polygon and MultiPolygon geometries currently.
 * - Does not support holes in polygons currently.
 */
@Composable
fun GeoJsonGeometryMapSnapshot(
    id: String,
    url: String,
    @ColorRes colorRes: Int,
    lineWidthPx: Float,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
    viewModel: MapSnapshotViewModel = hiltViewModel()
) {
    val geoJson by downloadFile(url = url, okHttpClient = viewModel.okHttpClient)
    val coordinates by getCoordinatesFromGeoJsonGeometry(geoJsonFeature = geoJson)

    if (coordinates != null) {
        MapSnapshot(
            specFactory = { width: Int, height: Int ->
                MapSnapshotSpec.Polylines(
                    id = id,
                    coordinates = coordinates!!,
                    colorRes = colorRes,
                    lineWidthPx = lineWidthPx,
                    width = width,
                    height = height,
                    explicitProviderName = explicitProviderName,
                    explicitMapType = explicitMapType
                )
            },
            placeholder = placeholder,
            backgroundColor = backgroundColor,
            modifier = modifier
        )
    } else if (url.isNotBlank()) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(backgroundColor)
        )
    }
}

@Composable
fun DashboardMapSnapshot(
    snapshotInfo: DashboardMapSnapshotData,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
) {
    MapSnapshot(
        specFactory = { width: Int, height: Int ->
            MapSnapshotSpec.DashboardMapWidget(
                snapshotInfo = snapshotInfo,
                width = width,
                height = height,
                explicitProviderName = explicitProviderName,
                explicitMapType = explicitMapType,
            )
        },
        placeholder = placeholder,
        backgroundColor = backgroundColor,
        modifier = modifier,
    )
}

@Composable
fun PopularRouteMapSnapshot(
    route: TopRoute,
    modifier: Modifier = Modifier,
    @DrawableRes placeholder: Int? = null,
    backgroundColor: Color = MaterialTheme.colors.background,
    explicitProviderName: String? = null,
    explicitMapType: MapType? = null,
) {
    MapSnapshot(
        specFactory = { width: Int, height: Int ->
            MapSnapshotSpec.PopularRoute(
                route = route,
                segmentsModifiedDateMillis = route.modifiedDate,
                showTurnByTurnWaypoints = route.turnWaypointsEnabled,
                width = width,
                height = height,
                explicitProviderName = explicitProviderName,
                explicitMapType = explicitMapType
            )
        },
        placeholder = placeholder,
        backgroundColor = backgroundColor,
        modifier = modifier
    )
}

@HiltViewModel
class MapSnapshotViewModel @Inject constructor(
    @ComposeMapSnapshotOkHttpClient val okHttpClient: OkHttpClient
) : ViewModel() {
    override fun onCleared() {
        super.onCleared()
        okHttpClient.cache?.close()
    }
}

@Composable
private fun MapSnapshot(
    specFactory: (Int, Int) -> MapSnapshotSpec,
    @DrawableRes placeholder: Int?,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    var size by remember { mutableStateOf(IntSize.Zero) }
    var spec: MapSnapshotSpec? by remember { mutableStateOf(null) }
    if (size != IntSize.Zero) {
        spec = specFactory(size.width, size.height)
    }
    val state: State<ComposeSnapshotState> = loadMapSnapshot(spec = spec)
    val painter = if (state.value.spec == spec && state.value.snapshot != null) {
        rememberDrawablePainter(drawable = state.value.snapshot)
    } else {
        if (placeholder != null) painterResource(id = placeholder) else null
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor)
            .onGloballyPositioned { size = it.size }
    ) {
        if (painter != null) {
            val (width, height) = with(LocalDensity.current) {
                size.width.toDp() to size.height.toDp()
            }

            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .width(width)
                    .height(height)
                    .align(Alignment.Center),
                contentScale = ContentScale.None,
            )
        }
    }
}

//region Loading map snapshot

// This @SuppressLint annotation should be removed once the fix is released.
// https://issuetracker.google.com/issues/376491756
@SuppressLint("ProduceStateDoesNotAssignValue")
@Composable
private fun loadMapSnapshot(
    spec: MapSnapshotSpec?
): State<ComposeSnapshotState> {
    val context = LocalContext.current
    val snapshotter = MapSnapshotter.instance
    return produceState(initialValue = ComposeSnapshotState(isLoading = true), spec, snapshotter) {
        value = if (spec == null || snapshotter == null) {
            ComposeSnapshotState(isLoading = spec == null)
        } else {
            runSuspendCatching {
                val fromCache = snapshotter.getFromCache(spec, context)
                val drawable = if (fromCache != null) {
                    Timber.d("Loading a compose map snapshot from cache")
                    fromCache
                } else {
                    Timber.d("Creating a new compose map snapshot")
                    snapshotter.requestSnapshot(spec, context)
                }
                ComposeSnapshotState(spec, drawable)
            }.getOrElse { e ->
                Timber.w(e, "Loading compose map snapshot failed.")
                ComposeSnapshotState(error = e)
            }
        }
    }
}

private data class ComposeSnapshotState(
    val spec: MapSnapshotSpec? = null,
    val snapshot: Drawable? = null,
    val isLoading: Boolean = false,
    val error: Throwable? = null,
)
//endregion

//region GeoJson parsing
private typealias ExteriorPolygon = ImmutableList<LatLng>

/**
 * NOTE:
 * - Supports only Polygon and MultiPolygon geometries currently.
 * - Does not support holes in polygons currently.
 */
// This @SuppressLint annotation should be removed once the fix is released.
// https://issuetracker.google.com/issues/376491756
@SuppressLint("ProduceStateDoesNotAssignValue")
@Composable
private fun getCoordinatesFromGeoJsonGeometry(
    geoJsonFeature: String?
): State<ImmutableList<ExteriorPolygon>?> {
    return produceState<ImmutableList<ExteriorPolygon>?>(initialValue = null, geoJsonFeature) {
        value = runSuspendCatching {
            withContext(Dispatchers.Default) {
                parseGeoJsonGeometry(geoJsonFeature)?.toPersistentList()
            }
        }.getOrElse { e ->
            Timber.w(e, "Failed to get coordinates from geojson: $geoJsonFeature")
            null
        }
    }
}

private fun parseGeoJsonGeometry(geoJsonFeature: String?): ImmutableList<ExteriorPolygon>? {
    if (geoJsonFeature == null) return null

    return when (val geometry = GeometryGeoJson.fromJson(geoJsonFeature)) {
        is MultiPolygon -> geometry.polygons().mapNotNull { it.outer() }
        is Polygon -> geometry.outer()?.run { listOf(this) }
        else -> throw Exception("Failed to convert points to coordinates.")
    }?.map { outer ->
        outer.coordinates().map { LatLng(it.latitude(), it.longitude()) }.toPersistentList()
    }?.toPersistentList()
}
//endregion

//region File download
// This @SuppressLint annotation should be removed once the fix is released.
// https://issuetracker.google.com/issues/376491756
@SuppressLint("ProduceStateDoesNotAssignValue")
@Composable
private fun downloadFile(
    url: String,
    okHttpClient: OkHttpClient
): State<String?> {
    return produceState<String?>(initialValue = null, url) {
        value = if (url.isBlank()) {
            Timber.d("Download URI is blank")
            null
        } else {
            download(url, okHttpClient)
        }
    }
}

private suspend fun download(url: String, okHttpClient: OkHttpClient): String? =
    withContext(Dispatchers.IO) {
        runSuspendCatching {
            retryIO(times = 3) {
                if (isActive) {
                    val request = Request.Builder().url(url).build()
                    okHttpClient.newCall(request)
                        .execute()
                        .use { response ->
                            response.body?.string()
                        }
                } else {
                    null
                }
            }
        }.getOrElse { e ->
            Timber.w(e, "Failed to download file from $url")
            null
        }
    }

private suspend fun <T> retryIO(
    times: Int = Int.MAX_VALUE,
    initialDelay: Long = 100, // 0.1 second
    maxDelay: Long = 1000, // 1 second
    factor: Double = 2.0,
    block: suspend () -> T
): T {
    var currentDelay = initialDelay
    repeat(times - 1) {
        try {
            return block()
        } catch (e: IOException) {
            // you can log an error here and/or make a more finer-grained
            // analysis of the cause to see if retry is needed
        }
        delay(currentDelay)
        currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelay)
    }
    return block() // last attempt
}
//endregion
