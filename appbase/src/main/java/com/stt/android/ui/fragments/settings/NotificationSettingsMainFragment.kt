package com.stt.android.ui.fragments.settings

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.NotificationManagerCompat
import androidx.core.view.isVisible
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.databinding.NotificationSettingsMainFragmentBinding
import com.stt.android.domain.user.NotificationSettings
import com.stt.android.exceptions.InternalDataException
import com.stt.android.ui.components.editors.SwitchEditor
import com.stt.android.ui.fragments.BaseCurrentUserControllerFragment
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class NotificationSettingsMainFragment : BaseCurrentUserControllerFragment() {

    private var binding: NotificationSettingsMainFragmentBinding? = null

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    lateinit var notificationSettings: NotificationSettings

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = NotificationSettingsMainFragmentBinding.inflate(inflater, container, false).also {
        binding = it
    }.root

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {
            pushGroupTitle.title.setText(R.string.settings_push_notifications_activities)
            localGroupTitle.title.setText(R.string.settings_push_notifications_reminders)
            emailGroupTitle.title.setText(R.string.email_notifications)
        }

        notificationSettings = userSettingsController.settings.notificationSettings

        initializeAppNotificationsLink()
        initializePushNotificationSettings()
        val suuntoFlavor = resources.getBoolean(R.bool.suuntoFlavorSpecific)
        setLocalNotificationsGroupEnabled(suuntoFlavor)
        setEmailNotificationsGroupEnabled(!suuntoFlavor)
    }

    override fun onStart() {
        super.onStart()
        binding?.apply {
            workoutCommentedPushEnabled.isChecked = notificationSettings.workoutCommentPushEnabled
            workoutSharedPushEnabled.isChecked = notificationSettings.workoutSharePushEnabled
            workoutLikedPushEnabled.isChecked = notificationSettings.workoutReactionPushEnabled
            facebookFriendJoinedPushEnabled.isChecked = notificationSettings.facebookFriendJoinPushEnabled
            newFollowerPushEnabled.isChecked = notificationSettings.newFollowerPushEnabled
            workoutCommentedEmailEnabled.isChecked = notificationSettings.workoutCommentEmailEnabled
            newFollowerEmailEnabled.isChecked = notificationSettings.newFollowerEmailEnabled
            workoutSharedEmailEnabled.isChecked = notificationSettings.workoutShareEmailEnabled
            newActivitySyncedLocalEnabled.isChecked = notificationSettings.newActivitySyncedLocalEnabled
        }

        updateAppNotifications()
    }

    override fun onStop() {
        try {
            userSettingsController.storeSettings(
                userSettingsController.settings.updateNotificationSettings(notificationSettings)
            )
        } catch (e: InternalDataException) {
            Timber.e(e, "Failed to store settings")
        }
        super.onStop()
    }

    private fun initializeAppNotificationsLink() = binding?.apply {
        appNotifications.setOnClickListener { openNotificationSettingsSystemUI() }
    }

    private fun updateAppNotifications() = binding?.apply {
        val enabled = NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()
        appNotifications.isVisible = !enabled
        workoutCommentedPushEnabled.setEnabledCompat(enabled)
        workoutSharedPushEnabled.setEnabledCompat(enabled)
        workoutLikedPushEnabled.setEnabledCompat(enabled)
        facebookFriendJoinedPushEnabled.setEnabledCompat(enabled)
        newFollowerPushEnabled.setEnabledCompat(enabled)
        newActivitySyncedLocalEnabled.setEnabledCompat(enabled)
    }

    private fun SwitchEditor.setEnabledCompat(enabled: Boolean) = setEnabledCompat(enabled) {
        openNotificationSettingsSystemUI()
    }

    private fun openNotificationSettingsSystemUI() {
        val context = requireContext()
        val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
        intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
        startActivity(intent)
    }

    private fun initializePushNotificationSettings() = binding?.apply {
        workoutCommentedPushEnabled.setOnCheckedChangeListener { isChecked ->
            notificationSettings = notificationSettings.toBuilder()
                .workoutCommentPushEnabled(isChecked)
                .build()
            trackProperty(AnalyticsUserProperty.PUSH_SETTING_COMMENTS, isChecked)
        }

        workoutSharedPushEnabled.setOnCheckedChangeListener { isChecked ->
            notificationSettings = notificationSettings.toBuilder()
                .workoutSharePushEnabled(isChecked)
                .build()
            trackProperty(AnalyticsUserProperty.PUSH_SETTING_FRIENDS_WORKOUTS, isChecked)
        }

        workoutLikedPushEnabled.setOnCheckedChangeListener { isChecked ->
            notificationSettings = notificationSettings.toBuilder()
                .workoutReactionPushEnabled(isChecked)
                .build()
            trackProperty(AnalyticsUserProperty.PUSH_SETTING_LIKES, isChecked)
        }

        newFollowerPushEnabled.setOnCheckedChangeListener { isChecked ->
            notificationSettings = notificationSettings.toBuilder()
                .newFollowerPushEnabled(isChecked)
                .build()
            trackProperty(AnalyticsUserProperty.PUSH_SETTING_NEW_FOLLOWERS, isChecked)
        }

        disableFacebookFriendJoinedIfNeeded()
    }

    private fun setLocalNotificationsGroupEnabled(enabled: Boolean) = binding?.apply {
        if (enabled) {
            newActivitySyncedLocalEnabled.setOnCheckedChangeListener { isChecked ->
                notificationSettings = notificationSettings.toBuilder()
                    .newActivitySyncedLocalEnabled(isChecked)
                    .build()
            }
        } else {
            localGroupTitle.root.isVisible = false
            newActivitySyncedLocalEnabled.isVisible = false
        }
    }

    private fun setEmailNotificationsGroupEnabled(enabled: Boolean) = binding?.apply {
        if (enabled) {
            workoutCommentedEmailEnabled.setOnCheckedChangeListener { isChecked ->
                notificationSettings = notificationSettings.toBuilder()
                    .workoutCommentEmailEnabled(isChecked)
                    .build()
                trackProperty(AnalyticsUserProperty.EMAIL_SETTINGS_COMMENTS, isChecked)
            }

            newFollowerEmailEnabled.setOnCheckedChangeListener { isChecked ->
                notificationSettings = notificationSettings.toBuilder()
                    .newFollowerEmailEnabled(isChecked)
                    .build()
                trackProperty(AnalyticsUserProperty.EMAIL_SETTINGS_NEW_FOLLOWERS, isChecked)
            }

            workoutSharedEmailEnabled.setOnCheckedChangeListener { isChecked ->
                notificationSettings = notificationSettings.toBuilder()
                    .workoutShareEmailEnabled(isChecked)
                    .build()
                trackProperty(AnalyticsUserProperty.EMAIL_SETTINGS_FRIENDS_WORKOUTS, isChecked)
            }
        } else {
            emailGroupTitle.root.isVisible = false
            workoutCommentedEmailEnabled.isVisible = false
            newFollowerEmailEnabled.isVisible = false
            workoutSharedEmailEnabled.isVisible = false

            // Disable email notifications for Suunto app
            notificationSettings = notificationSettings.toBuilder()
                .newFollowerEmailEnabled(false)
                .workoutCommentEmailEnabled(false)
                .workoutShareEmailEnabled(false)
                .build()
        }
    }

    private fun NotificationSettingsMainFragmentBinding.disableFacebookFriendJoinedIfNeeded() {
        if (!FlavorUtils.isSuuntoAppChina && resources.getBoolean(R.bool.showSocialWorkoutSharing)) {
            facebookFriendJoinedPushEnabled.isVisible = true
            facebookFriendJoinedPushEnabled.setOnCheckedChangeListener { isChecked ->
                notificationSettings = notificationSettings.toBuilder()
                    .facebookFriendJoinPushEnabled(isChecked)
                    .build()
                trackProperty(AnalyticsUserProperty.PUSH_SETTING_FRIENDS_JOIN, isChecked)
            }
        } else {
            facebookFriendJoinedPushEnabled.isVisible = false
        }
    }

    private fun trackProperty(userProperty: String, value: Boolean) {
        amplitudeAnalyticsTracker.trackUserProperty(userProperty, value)
        emarsysAnalytics.trackBooleanUserProperty(userProperty, value)
    }
}
