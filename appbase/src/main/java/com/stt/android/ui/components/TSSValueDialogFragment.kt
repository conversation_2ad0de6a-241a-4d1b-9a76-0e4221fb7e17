package com.stt.android.ui.components

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.core.view.updatePadding
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.R
import com.stt.android.data.workout.tss.getNameStringResId
import com.stt.android.databinding.TssValueEditorItemBinding
import com.stt.android.domain.workouts.tss.TSS
import kotlin.math.roundToInt

class TSSValueDialogFragment : DialogFragment() {
    private var viewHolders: List<TSSMethodViewHolder> = emptyList()
    private var selectedTssMethodHolder: TSSMethodViewHolder? = null

    @SuppressLint("InflateParams")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        /**
         * Options are shown in a vertical LinearLayout instead of a ListView / RecyclerView
         * because re-renders of ListViews cause issues with keyboard focus
         */
        val viewParent = LinearLayout(requireContext(), null)
        viewParent.isFocusable = true
        viewParent.isFocusableInTouchMode = true
        viewParent.orientation = LinearLayout.VERTICAL
        viewParent.updatePadding(top = resources.getDimensionPixelSize(R.dimen.size_spacing_small))

        val allTssMethods: List<TSS> =
            arguments?.getParcelableArrayList(TSS_OPTIONS) ?: emptyList()
        val selectedTssMethod: TSS? = arguments?.getParcelable(CURRENT_TSS)
        val valueViews = mutableListOf<TSSMethodViewHolder>()

        for (tss in allTssMethods) {
            val vh = TSSMethodViewHolder(viewParent, tss)
            vh.setOnClickListener {
                val imm: InputMethodManager =
                    requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(viewParent.windowToken, 0)
                viewParent.clearFocus()

                setSelectedTss(vh)
            }
            vh.setEditorFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    setSelectedTss(vh)
                }
            }

            vh.setSelected(tss == selectedTssMethod)

            viewParent.addView(vh.getRootView())
            valueViews.add(vh)
        }

        viewHolders = valueViews

        return AlertDialog.Builder(requireContext())
            .setPositiveButton(R.string.ok) { _, _ ->
                selectedTssMethodHolder?.let {
                    tag?.let { nonNullTag ->
                        setFragmentResult(nonNullTag, bundleOf(nonNullTag to it.tss))
                    }
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .setView(viewParent)
            .setTitle(R.string.workout_values_headline_tss)
            .create()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewHolders = emptyList()
        selectedTssMethodHolder = null
    }

    private fun setSelectedTss(viewHolder: TSSMethodViewHolder) {
        selectedTssMethodHolder = viewHolder
        arguments?.putParcelable(CURRENT_TSS, viewHolder.tss)
        viewHolders.forEach { it.setSelected(it == viewHolder) }
    }

    class TSSMethodViewHolder(
        containerView: ViewGroup,
        var tss: TSS,
    ) {
        private val binding = TssValueEditorItemBinding.inflate(
            LayoutInflater.from(containerView.context),
            containerView,
            false
        )

        init {
            binding.tssCalculationMethod.setText(tss.calculationMethod.getNameStringResId(callManualTSS = false))

            val editableValue = binding.tssEditableValue
            val staticValue = binding.tssStaticValue

            val valueText = tss.trainingStressScore.roundToInt().toString()
            staticValue.text = valueText
            editableValue.setText(valueText, TextView.BufferType.EDITABLE)

            if (tss.calculationMethod == TSSCalculationMethod.MANUAL) {
                editableValue.visibility = View.VISIBLE
                staticValue.visibility = View.GONE

                GenericFloatEditor.setupEditTextForFloatInputs(editableValue)

                editableValue.doAfterTextChanged { text ->
                    if (text == null) return@doAfterTextChanged

                    val floatValue = GenericFloatEditor.extractFloat(text)
                    if (floatValue in 0f..999f) {
                        tss = tss.copy(trainingStressScore = floatValue)
                        staticValue.text = floatValue.toString()
                    } else {
                        text.replace(0, text.length, tss.trainingStressScore.toString())
                    }
                }
            } else {
                editableValue.visibility = View.GONE
                staticValue.visibility = View.VISIBLE
            }
        }

        fun setSelected(selected: Boolean) {
            binding.tssCalculationMethod.isChecked = selected
        }

        fun setOnClickListener(listener: View.OnClickListener?) {
            binding.root.setOnClickListener(listener)
        }

        fun setEditorFocusChangeListener(listener: View.OnFocusChangeListener?) {
            binding.tssEditableValue.onFocusChangeListener = listener
        }

        fun getRootView(): View = binding.root
    }

    companion object {
        private const val CURRENT_TSS = "CURRENT_TSS"
        private const val TSS_OPTIONS = "TSS_OPTIONS"

        @JvmStatic
        fun newInstance(current: TSS?, options: List<TSS>): TSSValueDialogFragment {
            val fragment = TSSValueDialogFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(CURRENT_TSS, current)
                putParcelableArrayList(TSS_OPTIONS, ArrayList(options))
            }

            return fragment
        }
    }
}
