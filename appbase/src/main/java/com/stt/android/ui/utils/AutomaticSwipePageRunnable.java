package com.stt.android.ui.utils;

import android.os.Handler;
import androidx.viewpager.widget.ViewPager;
import android.view.MotionEvent;
import android.view.View;

/**
 * Changes the view pager page automatically after {@link #swipeDelay}.
 * You must call {@link #start()} and {@link #stop()} (probably from onResume/onPause).
 */
public class AutomaticSwipePageRunnable implements Runnable {
    private final ViewPager pager;
    private final Handler swipePagerHandler;
    private final long swipeDelay;

    /**
     *
     * @param pager
     * @param swipeDelay delay in milliseconds
     */
    public AutomaticSwipePageRunnable(ViewPager pager, long swipeDelay) {
        this.pager = pager;
        this.swipePagerHandler = new Handler();
        this.swipeDelay = swipeDelay;
        pager.setOnTouchListener(new CancelAutoSwipeTouchListener(this));
    }

    @Override
    public void run() {
        int nextPage = pager.getCurrentItem() + 1;
        int totalPages = pager.getAdapter().getCount();
        if (nextPage == totalPages) {
            nextPage = 0;
        }
        pager.setCurrentItem(nextPage, true);
        swipePagerHandler.postDelayed(this, swipeDelay);
    }

    public void start() {
        swipePagerHandler.postDelayed(this, swipeDelay);
    }

    public void stop() {
        swipePagerHandler.removeCallbacks(this);
    }

    private static class CancelAutoSwipeTouchListener implements View.OnTouchListener {
        private final AutomaticSwipePageRunnable automaticSwipePageRunnable;

        private CancelAutoSwipeTouchListener(AutomaticSwipePageRunnable automaticSwipePageRunnable) {
            this.automaticSwipePageRunnable = automaticSwipePageRunnable;
        }

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            // Remove the automatic swiping as soon as the user touches the pager
            automaticSwipePageRunnable.stop();
            return false;
        }

    }
}
