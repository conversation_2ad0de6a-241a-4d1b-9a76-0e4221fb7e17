package com.stt.android.ui.components;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import com.stt.android.R;
import com.stt.android.controllers.DiveExtensionDataModel;
import com.stt.android.controllers.SlopeSkiDataModel;
import com.stt.android.databinding.WorkoutSnapshotViewInnerBinding;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.SpeedPaceState;
import com.stt.android.domain.workouts.ActivityGroup;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper;
import com.stt.android.infomodel.SummaryItem;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.workouts.details.values.WorkoutValue;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

// TODO Merge with WorkoutSummaryDataView
@AndroidEntryPoint
public class WorkoutSnapshotView extends LinearLayout {
    private final static String ZERO_SPEED_PACE = "00'00";

    private WorkoutSnapshotViewInnerBinding binding;

    @Inject
    SlopeSkiDataModel slopeSkiDataModel;

    @Inject
    DiveExtensionDataModel diveExtensionDataModel;

    @Inject
    InfoModelFormatter infoModelFormatter;

    private boolean showActivityType = false;
    private boolean showLabels = false;
    private boolean showAverageSpeedPace = true;

    private boolean useUserSpeedPaceState = false;

    private Subscription loadSlopeSkiSubscription;
    private Subscription loadDiveSubscription;

    public WorkoutSnapshotView(Context context) {
        super(context);
        init(context);
    }

    public WorkoutSnapshotView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WorkoutSnapshotView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public WorkoutSnapshotView(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        setOrientation(HORIZONTAL);
        binding = WorkoutSnapshotViewInnerBinding.inflate(LayoutInflater.from(context), this);
    }

    public void setShowActivityType(boolean show) {
        if (showActivityType != show) {
            showActivityType = show;
            postInvalidate();
        }
    }

    public void setShowLabels(boolean show) {
        if (showLabels != show) {
            showLabels = show;
            postInvalidate();
        }
    }

    public void setShowAverageSpeedPace(boolean show) {
        if (showAverageSpeedPace != show) {
            showAverageSpeedPace = show;
            postInvalidate();
        }
    }

    public void setUseUserSpeedPaceState(boolean useUserSpeedPaceState) {
        if (this.useUserSpeedPaceState != useUserSpeedPaceState) {
            this.useUserSpeedPaceState = useUserSpeedPaceState;
            postInvalidate();
        }
    }

    public void setWorkoutHeader(final @Nullable WorkoutHeader workoutHeader) {
        if (workoutHeader == null) {
            return;
        }
        if (workoutHeader.getActivityType().isSlopeSki()) {
            unsubscribeLoadSlopSkiSubscription();
            loadSlopeSkiSubscription = slopeSkiDataModel.load(workoutHeader)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    slopeSkiSummary -> {
                        if (slopeSkiSummary == null) {
                            showWorkoutSnapshotForNonSlopeSkiAndNonDive(workoutHeader);
                        } else {
                            setWorkoutSnapshot(workoutHeader.getActivityType(),
                                workoutHeader.getTotalDistance(), workoutHeader.getTotalTime(),
                                workoutHeader.getAvgSpeed(), workoutHeader.getEnergyConsumption(),
                                workoutHeader.getHeartRateAverage(), slopeSkiSummary.getTotalRuns(),
                                slopeSkiSummary.getDescentDistanceInMeters(), null, null);
                        }
                    },
                    error -> showWorkoutSnapshotForNonSlopeSkiAndNonDive(workoutHeader)
                );
        } else if (workoutHeader.getActivityType().getSupportsDiveProfile()) {
            unsubscribeLoadDiveSubscription();
            loadDiveSubscription = diveExtensionDataModel.load(workoutHeader)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    diveExtension -> {
                        if (diveExtension == null) {
                            showWorkoutSnapshotForNonSlopeSkiAndNonDive(workoutHeader);
                        } else {
                            setWorkoutSnapshot(workoutHeader.getActivityType(),
                                workoutHeader.getTotalDistance(), workoutHeader.getTotalTime(),
                                workoutHeader.getAvgSpeed(), workoutHeader.getEnergyConsumption(),
                                workoutHeader.getHeartRateAverage(), -1,
                                -1, diveExtension.getMaxDepth(),
                                diveExtension.getMaxDepthTemperature());
                        }
                    },
                    error -> showWorkoutSnapshotForNonSlopeSkiAndNonDive(workoutHeader)
                );
        } else {
            showWorkoutSnapshotForNonSlopeSkiAndNonDive(workoutHeader);
        }
    }

    private void unsubscribeLoadSlopSkiSubscription() {
        if (loadSlopeSkiSubscription != null) {
            loadSlopeSkiSubscription.unsubscribe();
            loadSlopeSkiSubscription = null;
        }
    }

    private void unsubscribeLoadDiveSubscription() {
        if (loadDiveSubscription != null) {
            loadDiveSubscription.unsubscribe();
            loadDiveSubscription = null;
        }
    }

    void showWorkoutSnapshotForNonSlopeSkiAndNonDive(WorkoutHeader workoutHeader) {
        setWorkoutSnapshot(workoutHeader.getActivityType(), workoutHeader.getTotalDistance(),
            workoutHeader.getTotalTime(), workoutHeader.getAvgSpeed(),
            workoutHeader.getEnergyConsumption(), workoutHeader.getHeartRateAverage(), -1, -1.0, null,
            null);
    }

    public void setWorkoutSnapshot(@Nullable ActivityType activityType, double distance,
        double duration, double speed, double energy, double heartRate, int skiRuns,
        double skiDistance, Float maxDepth, Float maxDepthTemperature) {
        if (activityType == null) {
            return;
        }

        if (showActivityType) {
            binding.activityType.setVisibility(View.VISIBLE);
            binding.activityType.setImageResource(activityType.getIconId());
        } else {
            binding.activityType.setVisibility(View.GONE);
        }

        binding.durationLabel.setVisibility(showLabels ? View.VISIBLE : View.GONE);
        binding.duration.setText(infoModelFormatter.formatValue(SummaryItem.DURATION, duration).getValue());

        if (activityType.getSupportsDiveProfile()) {
            setLabel(binding.secondLabel, showLabels, R.string.workout_values_headline_max_depth);

            WorkoutValue formattedMaxDepth = null;
            if (maxDepth != null) {
                try {
                    formattedMaxDepth =
                        infoModelFormatter.formatValue(SummaryItem.MAXDEPTH, maxDepth);
                } catch (Exception e) {
                    Timber.w(e, "Failed to format dive max depth with infoModelFormatter");
                }
            }

            if (formattedMaxDepth != null) {
                binding.secondData.setText(formattedMaxDepth.getValue());
                if (formattedMaxDepth.getUnitString() != null) {
                    binding.secondUnit.setText(formattedMaxDepth.getUnitString());
                } else if (formattedMaxDepth.getUnit() != null) {
                    binding.secondUnit.setText(formattedMaxDepth.getUnit());
                }
            } else {
                binding.secondData.setText("");
                binding.secondUnit.setText("");
            }

            WorkoutValue formattedDiveMaxDepthTemperature = null;
            if (maxDepthTemperature != null) {
                try {
                    formattedDiveMaxDepthTemperature =
                        infoModelFormatter.formatValue(SummaryItem.DIVEMAXDEPTHTEMPERATURE,
                            maxDepthTemperature);
                } catch (Exception e) {
                    Timber.w(e,
                        "Failed to format dive max depth temperature with infoModelFormatter");
                }
            }

            setLabel(binding.thirdLabel, showLabels,
                R.string.workout_values_headline_dive_max_depth_temperature);
            if (formattedDiveMaxDepthTemperature != null) {
                binding.thirdData.setText(formattedDiveMaxDepthTemperature.getValue());
                if (formattedDiveMaxDepthTemperature.getUnitString() != null) {
                    binding.thirdUnit.setText(formattedDiveMaxDepthTemperature.getUnitString());
                } else if (formattedDiveMaxDepthTemperature.getUnit() != null) {
                    binding.thirdUnit.setText(formattedDiveMaxDepthTemperature.getUnit());
                }
            } else {
                binding.thirdData.setText("");
                binding.thirdUnit.setText("");
            }
        } else if (
            (activityType.isIndoor() ||
                new ActivityTypeToGroupMapper().activityTypeIdToGroup(activityType.getId())
                    == ActivityGroup.TeamAndRacketSports)
                && activityType != ActivityType.GOLF
                && !activityType.isSwimming()
        ) {
            setLabel(binding.secondLabel, showLabels, com.stt.android.core.R.string.energy_capital);
            binding.secondData.setText(Long.toString(Math.round(energy)));
            binding.secondUnit.setText(com.stt.android.core.R.string.kcal);

            setLabel(binding.thirdLabel, showLabels, com.stt.android.core.R.string.heart_rate_capital);
            binding.thirdData.setText(heartRate > 0L ? Long.toString(Math.round(heartRate)) : "- -");
            binding.thirdUnit.setText(com.stt.android.core.R.string.bpm);
        } else if (activityType.isSlopeSki() && skiRuns >= 0 && skiDistance >= 0.0) {
            setLabel(binding.secondLabel, showLabels, R.string.ski_runs_capital);
            binding.secondData.setText(Integer.toString(skiRuns));
            binding.secondUnit.setText(null);

            setLabel(binding.thirdLabel, showLabels, R.string.ski_distance_capital);
            WorkoutValue downhillDistance = infoModelFormatter.formatValue(SummaryItem.DISTANCE, skiDistance);
            binding.thirdData.setText(downhillDistance.getValue());
            binding.thirdUnit.setText(downhillDistance.getUnitLabel(infoModelFormatter.getContext()));
        } else if (activityType.getUsesNauticalUnits()) {
            setLabel(binding.secondLabel, showLabels, R.string.distance);
            WorkoutValue nauticalDistance = infoModelFormatter.formatValue(SummaryItem.NAUTICALDISTANCE, distance);
            binding.secondData.setText(nauticalDistance.getValue());
            binding.secondUnit.setText(nauticalDistance.getUnitLabel(infoModelFormatter.getContext()));

            setLabel(binding.thirdLabel, showLabels, R.string.workout_values_headline_avg_speed);
            WorkoutValue nauticalSpeed = infoModelFormatter.formatValue(SummaryItem.AVGNAUTICALSPEED, speed);
            binding.thirdData.setText(nauticalSpeed.getValue());
            binding.thirdUnit.setText(nauticalSpeed.getUnitLabel(infoModelFormatter.getContext()));
        } else if (activityType.isSwimming()) {
            setLabel(binding.secondLabel, showLabels, R.string.distance);
            WorkoutValue swimDistanceWorkoutValue = infoModelFormatter.formatValue(SummaryItem.SWIMDISTANCE, distance);
            binding.secondData.setText(swimDistanceWorkoutValue.getValue());
            binding.secondUnit.setText(swimDistanceWorkoutValue.getUnitLabel(getContext()));

            // InfoModelFormatter formats invalid pace of 0 speed as empty string (or warning emoji in debug).
            // In most places we won't show that invalid pace at all but here we should default to zeros.
            String paceDataStr;
            if (speed > 0) {
                paceDataStr = infoModelFormatter.formatValue(SummaryItem.AVGSWIMPACE, speed).getValue();
            } else {
                paceDataStr = ZERO_SPEED_PACE;
            }

            setLabel(binding.thirdLabel, showLabels, R.string.workout_values_headline_avg_pace);
            binding.thirdData.setText(paceDataStr);
            binding.thirdUnit.setText(infoModelFormatter.getUnit().getSwimPaceUnit());
        } else {
            setLabel(binding.secondLabel, showLabels, com.stt.android.core.R.string.distance_capital);
            WorkoutValue distanceWorkoutValue = infoModelFormatter.formatValue(SummaryItem.DISTANCE, distance);
            binding.secondData.setText(distanceWorkoutValue.getValue());
            binding.secondUnit.setText(distanceWorkoutValue.getUnitLabel(infoModelFormatter.getContext()));

            SpeedPaceState speedPaceState =
                ActivityTypeHelper.getSpeedPaceState(useUserSpeedPaceState, getContext(), activityType);
            if (speedPaceState == SpeedPaceState.SPEED) {
                setLabel(binding.thirdLabel, showLabels,
                    showAverageSpeedPace ? R.string.avg_speed_capital : com.stt.android.core.R.string.speed_capital);
                WorkoutValue speedWorkoutValue = infoModelFormatter.formatValue(SummaryItem.AVGSPEED, speed);
                binding.thirdData.setText(speedWorkoutValue.getValue());
                binding.thirdUnit.setText(speedWorkoutValue.getUnitLabel(infoModelFormatter.getContext()));
            } else {

                // InfoModelFormatter formats invalid pace of 0 speed as empty string (or warning emoji in debug).
                // In most places we won't show that invalid pace at all but here we should default to zeros.
                String paceDataStr;
                if (speed > 0) {
                    paceDataStr = infoModelFormatter.formatValue(SummaryItem.AVGPACE, speed).getValue();
                } else {
                    paceDataStr = ZERO_SPEED_PACE;
                }

                setLabel(binding.thirdLabel, showLabels,
                    showAverageSpeedPace ? R.string.avg_pace_capital : com.stt.android.core.R.string.pace_capital);
                binding.thirdData.setText(paceDataStr);
                binding.thirdUnit.setText(infoModelFormatter.getUnit().getPaceUnit());
            }
        }
    }

    private static void setLabel(TextView textView, boolean show, @StringRes int text) {
        if (show) {
            textView.setVisibility(View.VISIBLE);
            textView.setText(text);
        } else {
            textView.setVisibility(View.GONE);
        }
    }
}
