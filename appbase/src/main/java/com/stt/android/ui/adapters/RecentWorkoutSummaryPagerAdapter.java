package com.stt.android.ui.adapters;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import com.amersports.formatter.unit.jscience.JScienceUnitConverter;
import com.stt.android.R;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.domain.user.workout.RecentWorkoutSummary;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.ui.activities.RecentWorkoutSummaryActivity;
import com.stt.android.ui.components.charts.RecentWorkoutSummaryChart;
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper;

public class RecentWorkoutSummaryPagerAdapter extends RecentWorkoutPagerAdapter
    implements View.OnClickListener {
    private final RecentWorkoutSummary recentWorkoutSummary;
    private final AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    public RecentWorkoutSummaryPagerAdapter(Context context,
        RecentWorkoutSummary recentWorkoutSummary,
        WorkoutHeader referenceWorkout,
        @NonNull InfoModelFormatter infoModelFormatter,
        @NonNull JScienceUnitConverter unitConverter,
        AmplitudeAnalyticsTracker amplitudeAnalyticsTracker
    ) {
        super(context, referenceWorkout, infoModelFormatter, unitConverter);
        this.recentWorkoutSummary = recentWorkoutSummary;
        this.amplitudeAnalyticsTracker = amplitudeAnalyticsTracker;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View rootView = inflater.inflate(R.layout.summary_page, container, false);

        RecentWorkoutSummaryChart chart = rootView.findViewById(R.id.recentWorkoutSummaryChart);
        RecentWorkoutItemHelper.INSTANCE.initChart(context, referenceWorkout, recentWorkoutSummary,
            chart, position, infoModelFormatter, unitConverter);
        rootView.setOnClickListener(this);

        container.addView(rootView, 0);
        return rootView;
    }

    @Override
    public void onClick(View v) {
        if (amplitudeAnalyticsTracker != null) {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.THIRTY_DAY_SUMMARY_GRAPH_CLICK);
        }
        context.startActivity(
            RecentWorkoutSummaryActivity.Companion.newStartIntent(context, referenceWorkout));
    }
}
