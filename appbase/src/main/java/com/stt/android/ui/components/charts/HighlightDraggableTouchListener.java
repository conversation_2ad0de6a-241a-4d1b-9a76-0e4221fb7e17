package com.stt.android.ui.components.charts;

import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View;
import com.github.mikephil.charting.charts.BarLineChartBase;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.utils.ViewPortHandler;

public class HighlightDraggableTouchListener implements View.OnTouchListener {
    /**
     * If the horizontal distance between touch point and high light is equal or less than this
     * (in DP), disable the dragging gesture for the chart.
     */
    private static final float ENABLE_DRAG_HIGHLIGHT_THRESHOLD = 8.0F;

    private final BarLineChartBase chart;
    private final float threshold;

    public HighlightDraggableTouchListener(BarLineChartBase chart) {
        this.chart = chart;

        DisplayMetrics metrics = chart.getResources().getDisplayMetrics();
        threshold = ENABLE_DRAG_HIGHLIGHT_THRESHOLD * ((float) metrics.densityDpi
            / DisplayMetrics.DENSITY_DEFAULT);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        // when the user touches on the screen near a highlight, we disable the dragging gesture
        // for the chart, so that the user can drag the highlight
        // when the user's finger leaves, we resume the dragging gesture for the chart
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            Highlight[] highlighted = chart.getHighlighted();
            if (highlighted == null || highlighted.length == 0) {
                // the highlight array might be null
                // e.g. https://fabric.io/sporst-tracker/android/apps/com.stt
                // .android/issues/563b8268f5d3a7f76b584130
                return false;
            }

            float highlightXIndex = highlighted[0].getX();
            float lowestVisibleXIndex = chart.getLowestVisibleX();
            float highestVisibleXIndex = chart.getHighestVisibleX();
            if (highlightXIndex < lowestVisibleXIndex || highlightXIndex > highestVisibleXIndex) {
                return false;
            }

            // try to figure out the x position of the high light and assign it to
            // projectedHighlightX
            ViewPortHandler viewPortHandler =
                chart.getViewPortHandler(); // it tells the position of the chart
            float projectedHighlightX = viewPortHandler.contentLeft()
                + viewPortHandler.contentWidth() * (highlightXIndex - lowestVisibleXIndex) / (
                highestVisibleXIndex
                    - lowestVisibleXIndex);
            if (Math.abs(projectedHighlightX - event.getX()) <= threshold) {
                chart.setDragEnabled(false);
            }
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
            chart.setDragEnabled(true);
        }

        return false;
    }
}
