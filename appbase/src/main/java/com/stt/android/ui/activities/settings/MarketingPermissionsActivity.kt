package com.stt.android.ui.activities.settings

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.MenuItem
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.ActivityMarketingPermissionsBinding
import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.BaseUrlV2
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MarketingPermissionsActivity: AppCompatActivity() {
    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var generateOTPUseCase: GenerateOTPUseCase

    @Inject
    @BaseUrlV2
    lateinit var baseUrlV2: String

    private lateinit var viewBinding: ActivityMarketingPermissionsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewBinding = ActivityMarketingPermissionsBinding.inflate(layoutInflater)
        setContentView(viewBinding.root)
        setSupportActionBar(viewBinding.toolbar)
        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
        }

        viewBinding.retry.setOnClickListener { load() }
        initializeWebView()

        onBackPressedDispatcher.addCallback {
            if (viewBinding.webView.canGoBack()) {
                viewBinding.webView.goBack()
            } else {
                finish()
            }
        }

        if (savedInstanceState == null) {
            load()
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initializeWebView() = with(viewBinding.webView) {
        settings.javaScriptEnabled = true
        settings.cacheMode = WebSettings.LOAD_NO_CACHE

        webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                if (request?.url?.toString()?.startsWith(CALLBACK_URL) == true) {
                    finish()
                    return true
                }

                return super.shouldOverrideUrlLoading(view, request)
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                onLoadingFinished()
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                if (request?.isForMainFrame == true) {
                    onLoadingFailed()
                }
            }
        }

        webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                viewBinding.loadingSpinner.isVisible = newProgress < 100
            }
        }
    }

    private fun load() = with(viewBinding) {
        retry.isVisible = false
        webView.isInvisible = true
        webView.loadUrl(createMarketingPermissionsUrl(), createMarketingPermissionsHeader())
    }

    private fun onLoadingFailed() = with(viewBinding) {
        webView.isInvisible = true
        retry.isVisible = true
    }

    private fun onLoadingFinished() = with(viewBinding) {
        webView.isVisible = !retry.isVisible
    }

    override fun onDestroy() {
        viewBinding.webView.apply {
            webViewClient = WebViewClient()
            webChromeClient = WebChromeClient()
        }
        super.onDestroy()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        viewBinding.webView.saveState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        viewBinding.webView.restoreState(savedInstanceState)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun createMarketingPermissionsUrl(): String =
        "${baseUrlV2}policyandterms/edit?brand=${brand()}&callbackUrl=$CALLBACK_URL"

    private fun brand(): String = if (FlavorUtils.isSuuntoApp) {
        "SUUNTOAPP"
    } else {
        "SPORTSTRACKER"
    }

    private fun createMarketingPermissionsHeader(): Map<String, String> =
        currentUserController.session
            ?.getAuthorizationHeaders()
            ?.apply {
                put(BaseRemoteApi.HEADER_TOTP_KEY, generateOTPUseCase.generateTOTP())
            }
            ?: emptyMap()

    private companion object {
        const val CALLBACK_URL = "suunto://action/agree-tos"
    }
}
