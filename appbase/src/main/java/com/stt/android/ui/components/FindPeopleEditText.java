package com.stt.android.ui.components;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnTouchListener;
import android.view.inputmethod.InputMethodManager;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat;
import com.stt.android.ThemeColors;
import com.stt.android.compose.ui.R;

public class FindPeopleEditText extends AppCompatEditText
    implements OnTouchListener, View.OnFocusChangeListener {

    private SearchClosedListener searchClosedListener;

    public FindPeopleEditText(Context context) {
        super(context);
        init();
    }

    public FindPeopleEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FindPeopleEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        super.setOnFocusChangeListener(this);
        setOnTouchListener(this);
    }

    public void setSearchClosedListener(SearchClosedListener searchClosedListener) {
        this.searchClosedListener = searchClosedListener;
    }

    /**
     * If search field is empty and user presses back to close soft keyboard let's clear focus also
     */
    @Override
    public boolean onKeyPreIme(int keyCode, KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK
            && event.getAction() == KeyEvent.ACTION_UP
            && getText().length() == 0) {
            this.clearFocus();
        }
        return super.onKeyPreIme(keyCode, event);
    }

    @Override
    public void onFocusChange(View view, boolean b) {
        onSearchFocusChanged(b);
    }

    private void onSearchFocusChanged(boolean hasFocus) {
        if (hasFocus) {
            VectorDrawableCompat arrow = VectorDrawableCompat.create(
                getResources(), R.drawable.ic_action_back, null);
            arrow.setTint(ThemeColors.resolveColor(getContext(), android.R.attr.textColorPrimary));
            this.setCompoundDrawablesWithIntrinsicBounds(arrow, null, null, null);
        } else {
            VectorDrawableCompat search =
                VectorDrawableCompat.create(getResources(), com.stt.android.core.R.drawable.ic_search_fill, null);
            this.setCompoundDrawablesWithIntrinsicBounds(search, null, null, null);
            this.setText("");
            this.setError(null);
            //hide keyboard
            InputMethodManager im =
                (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            im.hideSoftInputFromWindow(this.getWindowToken(), 0);
        }

        if (searchClosedListener != null) {
            searchClosedListener.onSearchClosed();
        }
    }

    /**
     * Here we catch touch events for custom arrow icon that will be shown only when search has
     * focus
     */
    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        if (hasFocus()) {
            // In case we have a left drawable being shown
            Drawable leftDrawable = getCompoundDrawables()[0];
            if (leftDrawable != null) {
                // Check if the user tapped the left drawable
                boolean tappedX =
                    motionEvent.getX() < (getPaddingLeft() + leftDrawable.getIntrinsicWidth());
                if (tappedX) {
                    // clear focus on edit text when the user
                    // releases the tap
                    if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                        this.clearFocus();
                    }
                    return true;
                }
            }
        }
        return false;
    }

    public interface SearchClosedListener {
        void onSearchClosed();
    }
}
