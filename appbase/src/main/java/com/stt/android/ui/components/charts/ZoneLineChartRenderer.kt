package com.stt.android.ui.components.charts

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import com.github.mikephil.charting.animation.ChartAnimator
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.renderer.LineChartRenderer
import com.github.mikephil.charting.utils.ViewPortHandler

/**
 * Renders the line chart using intensity zone colors.
 * See [setZones]
 */
open class ZoneLineChartRenderer(
    chart: LineDataProvider,
    animator: ChartA<PERSON>mator,
    viewPortHandler: ViewPortHandler
) : LineChartRenderer(chart, animator, viewPortHandler) {
    data class Zone(
        val bottomLimit: Float,
        val color: Int
    )

    private lateinit var zones: List<Zone>

    override fun drawCubicBezier(dataSet: ILineDataSet) {
        val restoreId = mBitmapCanvas?.saveLayer(
            0f,
            0f,
            mBitmapCanvas.width.toFloat(),
            mBitmapCanvas.height.toFloat(),
            Paint()
        )
        super.drawCubicBezier(dataSet)

        colorizeZones(mBitmapCanvas, dataSet)

        restoreId?.let { mBitmapCanvas.restoreToCount(restoreId) }
    }

    override fun drawHorizontalBezier(dataSet: ILineDataSet) {
        val restoreId = mBitmapCanvas?.saveLayer(
            0f,
            0f,
            mBitmapCanvas.width.toFloat(),
            mBitmapCanvas.height.toFloat(),
            Paint()
        )
        super.drawHorizontalBezier(dataSet)

        colorizeZones(mBitmapCanvas, dataSet)

        restoreId?.let { mBitmapCanvas.restoreToCount(restoreId) }
    }

    override fun drawDataSet(c: Canvas?, dataSet: ILineDataSet) {
        val restoreId = c?.saveLayer(
            0f,
            0f,
            c.width.toFloat(),
            c.height.toFloat(),
            Paint()
        )
        super.drawDataSet(c, dataSet)

        if (dataSet.mode == LineDataSet.Mode.LINEAR ||
            dataSet.mode == LineDataSet.Mode.STEPPED
        ) {
            colorizeZones(c, dataSet)
        }

        restoreId?.let { c.restoreToCount(restoreId) }
    }

    fun setZones(zones: List<Zone>) {
        this.zones = zones
    }

    private fun colorizeZones(
        c: Canvas?,
        dataSet: ILineDataSet
    ) {
        if (dataSet.axisDependency == YAxis.AxisDependency.RIGHT) {
            return
        }

        val transformer = mChart.getTransformer(dataSet.axisDependency)
        val entryCount = dataSet.entryCount
        if (c != null && entryCount > 0 && this::zones.isInitialized) {
            val firstEntry = dataSet.getEntryForIndex(0)
            val paint = Paint().apply {
                xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
            }

            var top = 0f
            var bottom: Float

            zones.mapIndexed { index, zone ->
                zone.color to transformer.getPixelForValues(firstEntry.x, zone.bottomLimit)
            }.forEach { (color, point) ->
                paint.color = color

                val left = 0f
                val right = c.width.toFloat()
                bottom = point.y.toFloat()

                c.drawRect(left, top, right, bottom, paint)
                top = point.y.toFloat()
            }
        }
    }

    fun resetZoneLimits() {
        zones = emptyList()
    }
}
