package com.stt.android.ui.activities;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.work.WorkManager;
import com.stt.android.FirstPairedDeviceType;
import com.stt.android.R;
import com.stt.android.analytics.SyncFirstPairedDeviceEventJob;
import com.stt.android.bluetooth.BleHelper;
import com.stt.android.bluetooth.BleHrModel;
import com.stt.android.bluetooth.HrEventListener;
import com.stt.android.di.FirstPairedDevicePreferences;
import com.stt.android.hr.BluetoothHeartRateEvent;
import com.stt.android.hr.HeartRateDeviceConnectionManager;
import com.stt.android.hr.HeartRateMonitorType;
import com.stt.android.hr.HeartRateUpdateProvider;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;
import timber.log.Timber;

@AndroidEntryPoint
public class DisplayHeartRateActivity extends DisplaySensorActivity
    implements HeartRateDeviceConnectionManager.Callbacks, HrEventListener {
    public static Intent newStartIntent(Context context, BluetoothDevice device) {
        return new Intent(context, DisplayHeartRateActivity.class).putExtra(
            BluetoothDevice.EXTRA_DEVICE, device);
    }

    private final BroadcastReceiver heartRateUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            BluetoothHeartRateEvent event =
                intent.getParcelableExtra(STTConstants.ExtraKeys.HEART_RATE_EVENT);
            switch (event.getAction()) {
                case BluetoothHeartRateEvent.ACTION_ERROR:
                    Timber.w("HR read error");
                    backToSetup();
                    break;
                case BluetoothHeartRateEvent.ACTION_DISCONNECTED:
                    backToSetup();
                    break;
                case BluetoothHeartRateEvent.ACTION_UPDATE:
                    binding.currentValue.setText(Integer.toString(event.getHeartRate()));

                    int batteryLevel = event.getBatteryStatus().getLevel();
                    if (batteryLevel < 0) {
                        binding.batteryStatus.setVisibility(View.GONE);
                    } else {
                        binding.batteryStatus.setImageResource(
                            DisplayHeartRateActivity.getBatteryResource(batteryLevel));
                        binding.batteryStatus.setVisibility(View.VISIBLE);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private static int getBatteryResource(int level) {
        if (level < 10) {
            return R.drawable.hr_battery_0;
        } else if (level < 25) {
            return R.drawable.hr_battery_1;
        } else if (level < 50) {
            return R.drawable.hr_battery_2;
        } else if (level < 75) {
            return R.drawable.hr_battery_3;
        } else {
            return R.drawable.hr_battery_4;
        }
    }

    @Inject
    HeartRateDeviceConnectionManager heartRateDeviceConnectionManager;

    @Inject
    HeartRateUpdateProvider heartRateUpdateProvider;

    @Inject
    @Nullable
    BleHrModel bleHrModel;

    @Inject
    LocalBroadcastManager localBroadcastManager;

    @FirstPairedDevicePreferences
    @Inject
    SharedPreferences firstPairedDevicePreferences;

    @Inject
    dagger.Lazy<WorkManager> workManager;


    private final Runnable updateUi = new Runnable() {
        @Override
        public void run() {
            binding.currentValue.setText(Integer.toString(currentHeartRate));
            binding.batteryStatus.setVisibility(View.GONE);
        }
    };
    private int currentHeartRate;

    private BluetoothDevice bluetoothDevice;
    private HeartRateMonitorType hrmType;

    @SuppressLint("MissingPermission")
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initializeUi(R.string.your_current_hr, R.string.note_hr_save_power, R.string.hr_ready_use,
            com.stt.android.core.R.string.bpm);

        Intent intent = getIntent();
        bluetoothDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

        hrmType = HeartRateMonitorType.fromName(bluetoothDevice.getName());
        if (hrmType == null) {
            if (BleHelper.supportsBle(this)) {
                // for whatever reason, BLE scanning might return a null device name
                hrmType = HeartRateMonitorType.SMART;
                IllegalStateException exception = new IllegalStateException(
                    String.format("Unknown Bluetooth device %s", bluetoothDevice.toString()));
                Timber.w(exception);
            }
        }
        if (hrmType == null) {
            throw new IllegalStateException("Unknown Bluetooth device");
        }

        Timber.d("DisplayHeartRateActivity.onCreate() Bluetooth device: %s HRM Type: %s",
            bluetoothDevice, hrmType);
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (hrmType.requiresBluetoothLowEnergy()) {
            if (bleHrModel != null) {
                bleHrModel.addListener(this);
                try {
                    bleHrModel.requestUpdates(bluetoothDevice);
                } catch (Exception e) {
                    Timber.e(e, "Failed to request updates from HR device");
                }
            } else {
                throw new IllegalStateException(
                    "Missing BLE support while we're trying to connect to a SMART HR.");
            }
        } else {
            heartRateDeviceConnectionManager.addListener(this);
            heartRateDeviceConnectionManager.connect(getApplicationContext(), bluetoothDevice,
                hrmType);
        }
    }

    @Override
    protected void onPause() {
        try {
            localBroadcastManager.unregisterReceiver(heartRateUpdateReceiver);
        } catch (IllegalArgumentException e) {
            // the user paused the activity before the receiver is registered, do nothing
        }

        if (hrmType.requiresBluetoothLowEnergy()) {
            if (bleHrModel != null) {
                bleHrModel.removeListener(this);
                bleHrModel.stopUpdates();
            } else {
                throw new IllegalStateException(
                    "Missing BLE support while we're trying to connect to a SMART HR.");
            }
        } else {
            heartRateUpdateProvider.stopRequestingHeartRateUpdates();
            heartRateDeviceConnectionManager.removeListener(this);
            heartRateDeviceConnectionManager.disconnect(getApplicationContext());
        }

        super.onPause();
    }

    @SuppressLint("MissingPermission")
    @Override
    public void onConnected(BluetoothSocket socket) {
        Timber.d("Bluetooth heart rate monitor connected");

        localBroadcastManager.registerReceiver(heartRateUpdateReceiver,
            new IntentFilter(STTConstants.BroadcastActions.HEART_RATE_UPDATE));

        heartRateUpdateProvider.requestHeartRateUpdates(socket, hrmType);

        // send first paired event to data hub
        String key =
            String.format(STTConstants.FirstPairedDevicePreferences.KEY_FIRST_PAIRED_DEVICE,
                FirstPairedDeviceType.HEART_RATE_BELT.getType(), bluetoothDevice.getAddress());
        boolean deviceSynced = firstPairedDevicePreferences.getBoolean(key, false);
        if (!deviceSynced) {
            SyncFirstPairedDeviceEventJob.Companion.enqueue(workManager.get(),
                bluetoothDevice.getAddress(), bluetoothDevice.getName(),
                FirstPairedDeviceType.HEART_RATE_BELT);
        }
    }

    @Override
    public void onConnectionError(Throwable e) {
        Timber.w(e, "Bluetooth heart rate monitor connection failed");
        backToSetup();
    }

    private void backToSetup() {
        startActivity(new Intent(this, SetupHeartRateBeltActivity.class));
        finish();
    }

    @Override
    public void onDisconnected() {
        Timber.w("Bluetooth heart rate monitor disconnected");
        backToSetup();
    }

    @Override
    public void onUnpaired() {
        Timber.w("Bluetooth heart rate monitor unpaired.");
        backToSetup();
    }

    @Override
    public void onNoConnection() {
        Timber.w("No Bluetooth connection.");
        backToSetup();
    }

    @Override
    public void onHrUpdated(long timestamp, int heartRateBpm) {
        currentHeartRate = heartRateBpm;
        runOnUiThread(updateUi);
    }

    @Override
    public void onDeviceConnected() {
        // do nothing
    }

    @Override
    public void onDeviceDisconnected() {
        backToSetup();
    }
}
