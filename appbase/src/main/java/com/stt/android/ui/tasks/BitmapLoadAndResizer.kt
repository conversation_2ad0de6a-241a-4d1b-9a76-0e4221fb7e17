package com.stt.android.ui.tasks

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.exifinterface.media.ExifInterface
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.toBitmap
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.Point
import com.stt.android.ui.utils.BitmapUtils
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.IOException
import java.util.concurrent.CancellationException
import java.util.concurrent.ExecutionException
import javax.inject.Inject

class BitmapLoadAndResizer
@Inject constructor(
    private val context: Context,
    private val dispatchers: CoroutinesDispatchers
) {
    @Throws(
        InterruptedException::class,
        ExecutionException::class,
        CancellationException::class
    )
    suspend fun loadBitmapAtTargetResolution(
        uri: Uri,
        targetWidth: Int,
        targetHeight: Int
    ): Bitmap = withContext(dispatchers.io) {
        // Decode image size
        val opts = BitmapFactory.Options()
        opts.inJustDecodeBounds = true

        // Check EXIF rotation
        var rotate90 = false
        try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                val exif = ExifInterface(inputStream)
                // Need to swap width and height if rotated by +-90 or +-270.
                // Coil respects EXIF rotation, but size returned by BitmapFactory does not
                // consider it.
                rotate90 = exif.rotationDegrees % 180 != 0
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to parse rotation from EXIF information")
        }

        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            BitmapFactory.decodeStream(inputStream, null, opts)
            val (actualWidth, actualHeight) = if (rotate90) {
                BitmapUtils.boundSize(
                    currentSize = opts.outHeight to opts.outWidth,
                    targetSize = targetWidth to targetHeight
                )
            } else {
                BitmapUtils.boundSize(
                    currentSize = opts.outWidth to opts.outHeight,
                    targetSize = targetWidth to targetHeight
                )
            }

            Timber.v("Original picture size: ${opts.outWidth}x${opts.outHeight} rotate90=$rotate90")

            val request = ImageRequest.Builder(context.applicationContext)
                .data(uri)
                .size(actualWidth, actualHeight)
                .build()
            context.imageLoader.execute(request).image?.toBitmap()
        } ?: throw IOException("Failed to open stream for $uri")
    }

    fun positionFromImage(uri: Uri): Point? =
        try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                // code shamelessly stolen from: http://stackoverflow.com/a/15403927
                val exif = ExifInterface(inputStream)
                val latitude = exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE)
                val latitudeRef = exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE_REF)
                val longitude = exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE)
                val longitudeRef = exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF)
                val exifTags = sequenceOf(latitude, latitudeRef, longitude, longitudeRef)
                if (exifTags.any { it.isNullOrBlank() }) {
                    return null
                } else {
                    val latDeg = BitmapUtils.convertToDegree(latitude!!)
                    val lngDeg = BitmapUtils.convertToDegree(longitude!!)
                    val lat = if (latitudeRef == "N") latDeg else 0 - latDeg
                    val lng = if (longitudeRef == "E") lngDeg else 0 - lngDeg
                    Point(longitude = lng, latitude = lat)
                }
            }
        } catch (e: NumberFormatException) {
            Timber.e(e, "Failed to get EXIF info from image")
            null
        } catch (e: IOException) {
            Timber.e(e, "Failed to get EXIF info from image")
            null
        }
}
