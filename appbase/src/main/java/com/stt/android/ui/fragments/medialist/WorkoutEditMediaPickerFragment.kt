package com.stt.android.ui.fragments.medialist

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.transition.TransitionManager
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentWorkoutEditMediaPickerBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.openAppSettings
import com.stt.android.multimedia.gallery.MediaGalleryActivity
import com.stt.android.multimedia.picker.MediaInfoForPicker
import com.stt.android.utils.HarmonyUtils
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber

@AndroidEntryPoint
class WorkoutEditMediaPickerFragment :
    ViewStateListFragment2<WorkoutMediaContainer, WorkoutEditMediaPickerViewModel>() {
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { isGranted: Map<String, @JvmSuppressWildcards Boolean>? ->
        // reject and don't ask again. jump to system setting
        val allGranted = isGranted?.values?.all { it }
        if (allGranted == false && !shouldShowRational()) {
            activity?.openAppSettings()
        }
        if (allGranted == true) {
            onStoragePermissionForMediaGallery()
        }
        Timber.d("storage permission request result:%s", isGranted)
    }

    override val viewModel: WorkoutEditMediaPickerViewModel by viewModels()
    override val layoutId = R.layout.fragment_workout_edit_media_picker

    private val binding: FragmentWorkoutEditMediaPickerBinding get() = requireBinding()

    val isMediaEdited: Boolean
        get() = viewModel.isMediaEdited

    val workoutHeader: WorkoutHeader?
        get() = arguments?.getParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER)

    val pictureCount: Int
        get() = viewModel.pictureCount

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        workoutHeader?.let {
            viewModel.clearMedia()
            viewModel.loadWorkoutMedia(it)
        }
    }

    private fun shouldShowRational(): Boolean {
        return PermissionUtils.STORAGE_PERMISSIONS.any {
            shouldShowRequestPermissionRationale(it)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.onGalleryClicked.observeK(viewLifecycleOwner) {
            launchGallery()
        }

        if (arguments?.getBoolean(SAVE_AUTOMATICALLY_EXTRA) == true) {
            viewModel.mediaAddedOrRemoved.observeK(viewLifecycleOwner) {
                lifecycleScope.launch {
                    saveMedia()
                }
            }
        }

        binding.addImageText.setOnClickListener {
            launchGallery()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    private fun updateEmptyStateVisibility() {
        val viewState = viewModel.viewState.value

        val showEmptyState =
            viewState?.isLoaded() == true && viewState.data?.media?.isEmpty() == true

        TransitionManager.beginDelayedTransition(binding.root as ViewGroup)
        binding.addImageIcon.visibility = if (showEmptyState) View.VISIBLE else View.GONE
        binding.addImageText.visibility = if (showEmptyState) View.VISIBLE else View.GONE
    }

    override fun onStateChanged(state: ViewState<WorkoutMediaContainer?>) {
        super.onStateChanged(state)
        updateEmptyStateVisibility()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == PICK_MEDIA_FROM_GALLERY_REQUEST_CODE && resultCode == RESULT_OK) {
            val result =
                data?.getParcelableArrayListExtra<MediaInfoForPicker>(MediaGalleryActivity.GALLERY_RESULT_KEY)
            if (result != null) {
                viewModel.addMedia(result)
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    suspend fun saveMedia() {
        workoutHeader?.let {
            viewModel.saveMedia(it)
        }
    }

    private fun showApplyPermissionConfirmationDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle(R.string.request_permission)
            .setMessage(R.string.request_storage_permission_purpose)
            .setPositiveButton(
                R.string.allow
            ) { _, _ ->
                requestPermissionLauncher?.launch(PermissionUtils.STORAGE_PERMISSIONS)
            }
            .setNegativeButton(
                R.string.cancel
            ) { dialog, _ -> dialog.dismiss() }
            .show()
    }

    private fun launchGallery() {
        if (EasyPermissions.hasPermissions(
                requireContext(),
                *PermissionUtils.STORAGE_PERMISSIONS
            )
        ) {
            onStoragePermissionForMediaGallery()
        } else {
            showApplyPermissionConfirmationDialog()
        }
    }


    private fun onStoragePermissionForMediaGallery() {
        val intent = workoutHeader?.let {
            MediaGalleryActivity.newIntentForPickingWithPrioritizedTimePeriod(
                requireContext(),
                prioritizedTimePeriodStart = it.startTime,
                prioritizedTimePeriodEnd = it.stopTime
            )
        } ?: MediaGalleryActivity.newIntentForPicking(requireContext())

        startActivityForResult(intent, PICK_MEDIA_FROM_GALLERY_REQUEST_CODE)
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.ui.fragments.medialist.PICKER_FRAGMENT"

        private const val PICK_MEDIA_FROM_GALLERY_REQUEST_CODE = 202
        private const val SAVE_AUTOMATICALLY_EXTRA =
            "com.stt.android.ui.fragments.medialist.SAVE_AUTOMATICALLY"

        @JvmStatic
        fun newInstance(
            workoutHeader: WorkoutHeader?,
            saveAutomatically: Boolean,
        ) = WorkoutEditMediaPickerFragment().apply {
            arguments = bundleOf(
                STTConstants.ExtraKeys.WORKOUT_HEADER to workoutHeader,
                SAVE_AUTOMATICALLY_EXTRA to saveAutomatically
            )
        }
    }
}
