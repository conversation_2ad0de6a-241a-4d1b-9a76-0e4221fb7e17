package com.stt.android.ui.map.selection

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R
import com.stt.android.domain.workout.ActivityType

@Composable
fun ActivityTypesSelector(
    @StringRes titleResId: Int,
    @StringRes subTitleResId: Int?,
    selectedTypes: List<ActivityType>,
    allTypes: List<ActivityType>,
    onSelectChanged: (List<ActivityType>) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isAllSelected by remember {
        mutableStateOf(selectedTypes.size == allTypes.size)
    }
    var isNoneSelected by remember {
        mutableStateOf(selectedTypes.isEmpty())
    }
    var selectedActivityTypes by remember(selectedTypes) {
        mutableStateOf(if (isAllSelected) emptyList() else selectedTypes)
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = titleResId),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xxsmall
                ),
        )

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xxsmall))

        subTitleResId?.let {
            Text(
                text = stringResource(id = subTitleResId),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.xxsmall
                    )
            )
        }

        LazyRow(
            modifier = Modifier
                .padding(top = MaterialTheme.spacing.smaller),
            contentPadding = PaddingValues(horizontal = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
        ) {
            item {
                ActivityTypeFilterItem(
                    isSelected = isNoneSelected,
                    iconResId = com.stt.android.R.drawable.visibility_off_outline,
                    iconColor = MaterialTheme.colorScheme.primary,
                    itemDescription = stringResource(com.stt.android.R.string.popular_routes_none),
                    iconSize = MaterialTheme.iconSizes.medium,
                    onSelected = { selected ->
                        isNoneSelected = selected
                        isAllSelected = !selected
                        selectedActivityTypes = emptyList()

                        val update = if (selected) emptyList() else allTypes
                        onSelectChanged(update)
                    }
                )
            }

            item {
                ActivityTypeFilterItem(
                    isSelected = isAllSelected,
                    iconResId = R.drawable.ic_activity_type_all_outline,
                    iconColor = MaterialTheme.colorScheme.primary,
                    itemDescription = stringResource(R.string.activity_type_all),
                    iconSize = MaterialTheme.iconSizes.medium,
                    onSelected = { selected ->
                        isAllSelected = selected
                        isNoneSelected = !selected
                        selectedActivityTypes = emptyList()

                        val update = if (selected) allTypes else emptyList()
                        onSelectChanged(update)
                    }
                )
            }

            items(allTypes) { activityType ->
                ActivityTypeFilterItem(
                    isSelected = selectedActivityTypes.any { it == activityType },
                    iconResId = activityType.iconId,
                    iconColor = colorResource(activityType.colorId),
                    itemDescription = activityType.getLocalizedName(context.resources),
                    onSelected = { selected ->
                        val updated = if (selected) {
                            selectedActivityTypes + activityType
                        } else {
                            selectedActivityTypes - activityType
                        }

                        isNoneSelected = updated.isEmpty()
                        isAllSelected = updated.size == allTypes.size
                        selectedActivityTypes = if (isAllSelected) emptyList() else updated

                        onSelectChanged(updated)
                    }
                )
            }
        }
    }
}

@Composable
private fun ActivityTypeFilterItem(
    isSelected: Boolean,
    @DrawableRes iconResId: Int,
    iconColor: Color,
    itemDescription: String,
    onSelected: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    iconSize: Dp = MaterialTheme.iconSizes.xlarge
) {
    Box(
        modifier = modifier.clickableThrottleFirst(
            indication = null,
            interactionSource = remember { MutableInteractionSource() }
        ) {
            onSelected(!isSelected)
        }
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(68.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(62.dp)
                    .clip(CircleShape)
                    .background(Color.White)
                    .then(
                        if (isSelected)
                            Modifier.border(
                                3.dp,
                                MaterialTheme.colorScheme.primary,
                                CircleShape
                            )
                        else Modifier
                    ),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(52.dp)
                        .clip(CircleShape)
                        .background(
                            if (isSelected) {
                                iconColor
                            } else {
                                MaterialTheme.colorScheme.background
                            }
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        modifier = Modifier.size(iconSize),
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(iconResId)
                            .build(),
                        contentDescription = null,
                        colorFilter = if (isSelected) {
                            ColorFilter.tint(MaterialTheme.colorScheme.surface)
                        } else {
                            ColorFilter.tint(MaterialTheme.colorScheme.mediumGrey)
                        }
                    )
                }
            }

            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))

            Text(
                text = itemDescription,
                textAlign = TextAlign.Center,
                minLines = 2,
                style = MaterialTheme.typography.bodySmall,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurface
                }
            )
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun TopRoutesActivityTypeFilterScreenPreview() {
    M3AppTheme {
        ActivityTypesSelector(
            titleResId = com.stt.android.R.string.popular_routes_title,
            subTitleResId = com.stt.android.R.string.popular_routes_subtitle,
            selectedTypes = emptyList(),
            allTypes = listOf(
                ActivityType.RUNNING,
                ActivityType.HIKING,
                ActivityType.TREKKING,
                ActivityType.WALKING,
                ActivityType.TRAIL_RUNNING,
                ActivityType.CYCLING,
                ActivityType.MOUNTAIN_BIKING,
                ActivityType.CROSS_COUNTRY_SKIING,
            ),
            onSelectChanged = {}
        )
    }
}




