package com.stt.android.ui.activities.map;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.maps.SuuntoMap;
import com.stt.android.ui.map.GhostMarkerManager;
import com.stt.android.ui.workout.widgets.GhostAheadBehindWidget;
import com.stt.android.ui.workout.widgets.GhostTimeDistanceWidget;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.RecordWorkoutService;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;

@AndroidEntryPoint
public class OngoingAndGhostWorkoutMapActivity extends OngoingAndFollowWorkoutMapActivity {
    private static final long REFRESH_INTERVAL = 500L;
    /**
     * Handler responsible to force a redraw of the map view. We need it in case that the current
     * user location is
     * not being updated (not available or out of current map)
     */
    private final Handler redrawHandler = new Handler(Looper.getMainLooper());
    private GhostMarkerManager ghostMarkerManager;
    private final Runnable refreshMapTask = new Runnable() {
        @Override
        public void run() {
            RecordWorkoutService rws = getRecordWorkoutService();
            SuuntoMap map = getMap();
            if (rws != null && map != null) {
                if (ghostMarkerManager == null) {
                    ghostMarkerManager = new GhostMarkerManager(
                        OngoingAndGhostWorkoutMapActivity.this, map);
                }
                ghostMarkerManager.update(rws);
            }

            redrawHandler.postDelayed(this, REFRESH_INTERVAL);
        }
    };

    FrameLayout ghostDistanceTimeContainer;
    FrameLayout ghostAheadBehindContainer;

    @Inject
    GhostTimeDistanceWidget ghostTimeDistanceWidget;

    @Inject
    GhostAheadBehindWidget ghostAheadBehindWidget;

    public static Intent newStartIntent(Context context, WorkoutHeader targetWorkout) {
        return new Intent(context, OngoingAndGhostWorkoutMapActivity.class).putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, targetWorkout);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ghostDistanceTimeContainer = findViewById(R.id.ghostDistanceTimeContainer);
        ghostAheadBehindContainer = findViewById(R.id.ghostAheadBehindContainer);

        ghostDistanceTimeContainer.addView(
            ghostTimeDistanceWidget.inflate(LayoutInflater.from(this), ghostDistanceTimeContainer));
        ghostAheadBehindContainer.addView(
            ghostAheadBehindWidget.inflate(LayoutInflater.from(this), ghostAheadBehindContainer));
    }

    @Override
    public void onResume() {
        super.onResume();
        redrawHandler.postDelayed(refreshMapTask, REFRESH_INTERVAL);

        ghostTimeDistanceWidget.onStart();
        ghostAheadBehindWidget.onStart();

        ghostTimeDistanceWidget.onVisible();
        ghostAheadBehindWidget.onVisible();
    }

    @Override
    public void onPause() {
        redrawHandler.removeCallbacks(refreshMapTask);

        ghostTimeDistanceWidget.onHidden();
        ghostAheadBehindWidget.onHidden();

        ghostTimeDistanceWidget.onStop();
        ghostAheadBehindWidget.onStop();
        super.onPause();
    }

    @NonNull
    @Override
    protected String getBuyPremiumPopupShownAnalyticsSource() {
        return AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_RECORDING_MAP;
    }
}
