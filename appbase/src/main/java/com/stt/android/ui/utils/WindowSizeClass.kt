package com.stt.android.ui.utils

import android.app.Activity
import android.view.Display
import androidx.annotation.VisibleForTesting
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.toComposeRect
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.window.layout.WindowMetricsCalculator
import com.stt.android.extensions.displayCompat
import kotlin.math.min

// TODO: 30.5.2022 Migrate to androidx.compose.material3:material3-window-size-class
/**
 * WindowSizeClasses describing the wide and height of the display, including the system UI.
 *
 * Based on:
 * https://developer.android.com/guide/topics/large-screens/support-different-screen-sizes#window_size_classes
 *
 * @see WindowMetricsCalculator
 */
enum class WindowSizeClass {
    Compact,
    Medium,
    Expanded;

    companion object {
        fun compute(
            activity: Activity,
            block: (width: WindowSizeClass, height: WindowSizeClass) -> Unit
        ) {
            val (widthWindowSizeClass, heightWindowSizeClass) = compute(activity)
            block(widthWindowSizeClass, heightWindowSizeClass)
        }

        /**
         * Returns the WindowSizeClasses for width and height of the current Window.
         */
        fun compute(activity: Activity): Pair<WindowSizeClass, WindowSizeClass> {
            val metrics = WindowMetricsCalculator.getOrCreate()
                .computeCurrentWindowMetrics(activity)

            val widthDp = metrics.bounds.width() /
                activity.resources.displayMetrics.density
            val widthWindowSizeClass = when {
                widthDp < 600f -> Compact
                widthDp < 840f -> Medium
                else -> Expanded
            }

            val heightDp = metrics.bounds.height() /
                activity.resources.displayMetrics.density
            val heightWindowSizeClass = when {
                heightDp < 480f -> Compact
                heightDp < 900f -> Medium
                else -> Expanded
            }
            return Pair(widthWindowSizeClass, heightWindowSizeClass)
        }

        /**
         * Returns the WindowSizeClass based on the lowest dimension of the Display for [activity].
         * Use the physical dimensions from [Display.Mode] to get the opened size of the display
         * even on closed foldable devices.
         */
        fun computeLowestDimensionWindowSizeClass(activity: Activity): WindowSizeClass? {
            val displayMode: Display.Mode = activity.displayCompat?.mode ?: return null

            // Calculate the dp of the lowest (typically width in portrait) dimension of the mode.
            val displayPortraitWidthDp = min(
                displayMode.physicalWidth,
                displayMode.physicalHeight
            ) / activity.resources.displayMetrics.density

            return when {
                displayPortraitWidthDp < 600f -> Compact
                displayPortraitWidthDp < 840f -> Medium
                else -> Expanded
            }
        }
    }
}

/**
 * Remembers the [WindowSizeClass] class for the window corresponding to the current window metrics.
 *
 * Replace with androidx.compose.material3:material3-window-size-class after migrating to
 * Material3 or resolving issues with Preview.
 */
@Composable
fun Activity.rememberWindowSizeClass(): WindowSizeClass {
    // Get the size (in pixels) of the window
    val windowSize = rememberWindowSize()

    // Convert the window size to [Dp]
    val windowDpSize = with(LocalDensity.current) {
        windowSize.toDpSize()
    }

    // Calculate the window size class
    return getWindowSizeClass(windowDpSize)
}

/**
 * Remembers the [Size] in pixels of the window corresponding to the current window metrics.
 */
@Composable
fun Activity.rememberWindowSize(): Size {
    val configuration = LocalConfiguration.current
    // WindowMetricsCalculator implicitly depends on the configuration through the activity,
    // so re-calculate it upon changes.
    val windowMetrics = remember(configuration) {
        WindowMetricsCalculator.getOrCreate().computeCurrentWindowMetrics(this)
    }
    return windowMetrics.bounds.toComposeRect().size
}

data class WindowInfo(val windowWidthSizeClass: WindowSizeClass, val windowSize: DpSize)

/**
 * Remembers [WindowInfo] the [Size] corresponding to the current window metrics.
 */
@Composable
fun Activity.rememberWindowInfo(): WindowInfo {
    val configuration = LocalConfiguration.current
    // WindowMetricsCalculator implicitly depends on the configuration through the activity,
    // so re-calculate it upon changes.
    val windowMetrics = remember(configuration) {
        WindowMetricsCalculator.getOrCreate().computeCurrentWindowMetrics(this)
    }

    val windowSize = windowMetrics.bounds.toComposeRect().size

    // Convert the window size to [Dp]
    val windowDpSize = with(LocalDensity.current) {
        windowSize.toDpSize()
    }

    // Calculate the window size class
    return WindowInfo(
        windowWidthSizeClass = getWindowSizeClass(windowDpSize),
        windowSize = windowDpSize
    )
}

/**
 * Partitions a [DpSize] into a enumerated [WindowSizeClass] class.
 */
@VisibleForTesting
fun getWindowSizeClass(windowDpSize: DpSize): WindowSizeClass = when {
    windowDpSize.width < 0.dp -> throw IllegalArgumentException("Dp value cannot be negative")
    windowDpSize.width < 600.dp -> WindowSizeClass.Compact
    windowDpSize.width < 840.dp -> WindowSizeClass.Medium
    else -> WindowSizeClass.Expanded
}
