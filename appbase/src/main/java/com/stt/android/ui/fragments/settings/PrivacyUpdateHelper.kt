package com.stt.android.ui.fragments.settings

import com.stt.android.remote.workouts.update.SharingFlags
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow



// Need global save state
object PrivacyUpdateHelper {

    private val _sharingFlagsFlow = MutableStateFlow(SharingFlags.UNKNOWN)
    val sharingFlags: StateFlow<SharingFlags> = _sharingFlagsFlow
    var until: Long = 0L
    fun updateSharingFlags(sharingFlags: SharingFlags) {
        _sharingFlagsFlow.tryEmit(sharingFlags)
    }
}
