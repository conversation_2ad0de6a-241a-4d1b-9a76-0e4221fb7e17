package com.stt.android.routes.widget;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.OvalShape;
import androidx.annotation.UiThread;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.stt.android.R;
import com.stt.android.domain.workout.ActivityType;
import java.util.List;
import java.util.Locale;

public class ActivityTypeIcons extends LinearLayout {

    private static final float ACTIVITY_IC_BACKGROUND_SIZE_BIG_IN_DP = 60.0f;
    private static final float ACTIVITY_IC_BACKGROUND_SIZE_SMALL_IN_DP = 45.0f;
    private static final float ACTIVITY_IC_SIZE_SMALL_IN_DP = 24.0f;
    private static final float ACTIVITY_IC_SIZE_BIG_IN_DP = 45.0f;

    public ActivityTypeIcons(Context context) {
        super(context);
    }

    public ActivityTypeIcons(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ActivityTypeIcons(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public ActivityTypeIcons(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    /**
     * Sets the icon of the activity.
     *
     * @param activities list of {@link ActivityType}s
     * @param limit number of activity icons to be shown as horizontal series
     */
    @UiThread
    public void setActivityTypes(List<ActivityType> activities, int limit) {
        int activityCount = activities == null ? 0 : activities.size();
        if (activityCount == 0) {
            return;
        }
        removeAllViews();

        List<ActivityType> shownTypes;
        boolean showMore = limit != -1 && activityCount > limit;
        if (showMore) {
            shownTypes = activities.subList(0, limit);
        } else {
            shownTypes = activities;
        }
        int shown = shownTypes.size();
        boolean smallIcons = shown > 1;
        for (int i = 0; i < shown; ++i) {
            ActivityType activityType = shownTypes.get(i);
            // Create Image View
            float bgSizeInDp = smallIcons ? ACTIVITY_IC_BACKGROUND_SIZE_SMALL_IN_DP
                : ACTIVITY_IC_BACKGROUND_SIZE_BIG_IN_DP;
            float iconSizeInDp = smallIcons ? ACTIVITY_IC_SIZE_SMALL_IN_DP : ACTIVITY_IC_SIZE_BIG_IN_DP;
            int padding = calculatePadding(bgSizeInDp, iconSizeInDp);
            ImageView imageView = new ImageView(getContext());
            LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
            params.rightMargin = 20;//todo: get real value
            imageView.setLayoutParams(params);
            imageView.setBackgroundDrawable(createBackgroundShape(bgSizeInDp));
            imageView.setPadding(padding, padding, padding, padding);
            imageView.setImageResource(activityType.getIconId());
            addView(imageView);
        }

        if (showMore) {
            LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
            TextView tv = new TextView(getContext());
            tv.setLayoutParams(params);
            tv.setBackgroundDrawable(
                createBackgroundShape(ACTIVITY_IC_BACKGROUND_SIZE_SMALL_IN_DP));
            tv.setGravity(Gravity.CENTER);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
            addView(tv);
            tv.setText(String.format(Locale.US, "+%d", activityCount - limit));
        }

        invalidate();
    }

    private ShapeDrawable createBackgroundShape(float bgSizeInDp) {
        ShapeDrawable icActivityBg = new ShapeDrawable(new OvalShape());
        icActivityBg.setIntrinsicWidth(convertDpToPixel(getResources(), bgSizeInDp));
        icActivityBg.setIntrinsicHeight(convertDpToPixel(getResources(), bgSizeInDp));
        icActivityBg.getPaint().setColor(ContextCompat.getColor(getContext(), R.color.darker_gray));
        icActivityBg.getPaint().setAntiAlias(true);
        return icActivityBg;
    }

    private int calculatePadding(float bgSizeInDp, float iconSizeInDp) {
        return convertDpToPixel(getResources(), bgSizeInDp / 2 - iconSizeInDp / 2);
    }

    private static int convertDpToPixel(Resources resources, float value) {
        return Math.round(value * resources.getDisplayMetrics().density);
    }
}
