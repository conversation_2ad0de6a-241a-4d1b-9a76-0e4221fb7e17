package com.stt.android.utils

import com.stt.android.domain.android.DeviceFeatureStates
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class AndroidFeatureStatesModule {
    @Binds
    abstract fun bindAndroidFeatureStates(featureStateHelper: AndroidFeatureStates): DeviceFeatureStates
}
