package com.stt.android.utils;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;

import com.stt.android.utils.STTConstants;

public class CustomUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
    private final SharedPreferences preferences;
    private final Thread.UncaughtExceptionHandler defaultUncaughtExceptionHandler;

    public CustomUncaughtExceptionHandler(Context context, Thread.UncaughtExceptionHandler defaultUncaughtExceptionHandler) {
        preferences = PreferenceManager.getDefaultSharedPreferences(context);
        this.defaultUncaughtExceptionHandler = defaultUncaughtExceptionHandler;
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        preferences.edit()
                .putInt(STTConstants.DefaultPreferences.KEY_CRASH_COUNT, preferences.getInt(
                    STTConstants.DefaultPreferences.KEY_CRASH_COUNT, 0) + 1)
                .apply();

        if (defaultUncaughtExceptionHandler != null) {
            defaultUncaughtExceptionHandler.uncaughtException(thread, ex);
        }
    }
}
