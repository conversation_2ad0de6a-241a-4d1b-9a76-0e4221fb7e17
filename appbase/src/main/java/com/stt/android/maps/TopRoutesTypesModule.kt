package com.stt.android.maps

import com.stt.android.domain.workout.ActivityType
import com.stt.android.remote.di.BaseUrlConfiguration
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class TopRoutesTypesModule {
    companion object {
        @Provides
        @TopRouteTypes
        fun provideTopRouteTypes(baseUrlConfiguration: BaseUrlConfiguration): List<TopRouteType> {
            fun topRouteType(
                name: String,
                activityType: ActivityType,
            ): TopRouteType = TopRouteType(
                name = name,
                activityType = activityType,
                tileUrl = "${baseUrlConfiguration.tileServerUrl}toproutes/$name/{z}/{x}/{y}.mvt"
            )

            return listOf(
                topRouteType("Running", ActivityType.RUNNING),
                topRouteType("Hiking", ActivityType.HIKING),
                topRouteType("Trekking", ActivityType.TREKKING),
                topRouteType("Walking", ActivityType.WALKING),
                topRouteType("TrailRunning", ActivityType.TRAIL_RUNNING),
                topRouteType("Cycling", ActivityType.CYCLING),
                topRouteType("MountainBiking", ActivityType.MOUNTAIN_BIKING),
                topRouteType("CrossCountrySkiing", ActivityType.CROSS_COUNTRY_SKIING)
            )
        }
    }
}
