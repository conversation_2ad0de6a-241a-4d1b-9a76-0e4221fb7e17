package com.stt.android.maps

import android.graphics.drawable.Drawable
import android.graphics.drawable.TransitionDrawable
import android.widget.ImageView
import androidx.annotation.MainThread
import androidx.core.graphics.drawable.toDrawable
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.coroutines.ConflatedEventBus
import com.stt.android.coroutines.runSuspendCatching
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.yield
import timber.log.Timber
import java.lang.ref.WeakReference
import com.stt.android.core.R as CR

/**
 * A helper class to manage how map snapshots are shown in [ImageView] views.
 *
 * All snapshot binding and clearing operations are handled via a tasks queue that is executed
 * sequentially in the UI thread using coroutines. The execution happens in [runBinderExecutor].
 *
 * The external API for this functionality are the [ImageView.bindMapSnapshot] and
 * [ImageView.clearMapSnapshot] extension functions. They can be used from anywhere in the UI
 * layer even without access to a coroutine context.
 */
internal class MapSnapshotBinder {

    // Tasks related to binding map snapshots to ImageViews. Executed sequentially in UI thread.
    sealed class SnapshotBindTask(open val viewRef: WeakReference<ImageView>) {
        abstract suspend fun execute(scope: CoroutineScope)

        data class Bind(
            override val viewRef: WeakReference<ImageView>,
            val spec: MapSnapshotSpec
        ) : SnapshotBindTask(viewRef) {
            override suspend fun execute(scope: CoroutineScope) {
                val view = viewRef.get() ?: return
                val mapSnapshotter = MapSnapshotter.instance ?: return

                val state = view.stateTag
                if (state.isSuccess && state.spec == spec) {
                    // Already showing this snapshot
                    return
                } else if (state.isLoading) {
                    if (state.spec == spec) {
                        // Same spec, job is still ongoing, don't do anything
                        return
                    } else {
                        // Loading some other snapshot -> cancel
                        state.spec?.let {
                            mapSnapshotter.demoteToBackgroundPriority(it)
                        }
                        state.job?.cancel()
                    }
                }

                val cached = mapSnapshotter.getFromCache(spec, view.context)
                if (cached != null) {
                    view.stateTag = MapSnapshotViewState.Success(spec)
                    // If we get the image from cache, just show it immediately without transition
                    view.setImageDrawable(cached)
                } else {
                    view.clearSnapshot()

                    val job = scope.launch {
                        runSuspendCatching {
                            mapSnapshotter.requestSnapshot(spec, view.context)
                        }
                            .onSuccess { drawable ->
                                yield() // Last chance to cancel
                                // Fade in snapshot after it has been generated
                                view.showSnapshotWithTransition(drawable)
                                view.stateTag = MapSnapshotViewState.Success(spec)
                            }
                            .onFailure { e ->
                                yield() // Last chance to cancel
                                view.stateTag = MapSnapshotViewState.Failed(spec)
                                view.showErrorState()
                                if (e !is CancellationException) {
                                    Timber.w(e, "bindMapSnapshot failed")
                                } else {
                                    mapSnapshotter.cancelPendingRequest(spec)
                                    throw e
                                }
                            }
                    }

                    view.stateTag = MapSnapshotViewState.Loading(spec, job)
                }
            }
        }

        data class Detach(
            override val viewRef: WeakReference<ImageView>,
        ) : SnapshotBindTask(viewRef) {
            override suspend fun execute(scope: CoroutineScope) {
                viewRef.get()?.let { imageView ->
                    imageView.stateTag.spec?.let { spec ->
                        // No longer visible -> reduce priority
                        MapSnapshotter.instance?.demoteToBackgroundPriority(spec)
                    }
                    // Cancel any job handling snapshot request
                    imageView.stateTag.job?.cancelAndJoin()
                }
            }
        }

        data class Clear(
            override val viewRef: WeakReference<ImageView>,
        ) : SnapshotBindTask(viewRef) {
            override suspend fun execute(scope: CoroutineScope) {
                viewRef.get()?.let { imageView ->
                    imageView.stateTag.spec?.let { spec ->
                        // No longer visible -> reduce priority
                        MapSnapshotter.instance?.demoteToBackgroundPriority(spec)
                    }
                    // Cancel any job handling snapshot request
                    imageView.stateTag.job?.cancelAndJoin()

                    // Clear existing background from view
                    imageView.stateTag = MapSnapshotViewState.Idle
                    imageView.clearSnapshot()
                }
            }
        }
    }

    private val bindTasks: MutableSet<SnapshotBindTask> = mutableSetOf()
    private val requestAdded = ConflatedEventBus<Unit>()

    @MainThread
    fun bindMapSnapshot(spec: MapSnapshotSpec, view: ImageView) {
        removeExistingTasksForView(view)
        bindTasks += SnapshotBindTask.Bind(WeakReference(view), spec)
        requestAdded.send(Unit)
    }

    @MainThread
    fun detachMapSnapshot(view: ImageView) {
        removeExistingTasksForView(view)
        bindTasks += SnapshotBindTask.Detach(WeakReference(view))
        requestAdded.send(Unit)
    }

    @MainThread
    fun clearMapSnapshot(view: ImageView) {
        removeExistingTasksForView(view)
        bindTasks += SnapshotBindTask.Clear(WeakReference(view))
        requestAdded.send(Unit)
    }

    @MainThread
    private fun removeExistingTasksForView(view: ImageView) {
        bindTasks.removeAll {
            // Remove all tasks for given view. Also remove obsolete tasks where viewRef is null.
            it.viewRef.get() == null || it.viewRef.get() == view
        }
    }

    @MainThread
    private tailrec suspend fun getNextTask(): SnapshotBindTask {
        val task = bindTasks.firstOrNull()
        return if (task != null) {
            bindTasks.remove(task)
            task
        } else {
            requestAdded.awaitNext()
            getNextTask()
        }
    }

    /**
     * Execute tasks related to binding/unbinding map snapshots to [ImageView] instances. This
     * method never completes and is designed to be cancelled when the coroutine scope is cancelled.
     *
     * This is called from [MapSnapshotter.runSnapshotterEngine] and should not be called from
     * elsewhere.
     */
    @MainThread
    suspend fun runBinderExecutor() = supervisorScope {
        while (true) {
            runSuspendCatching {
                val task = getNextTask()
                task.execute(scope = this)
            }.onFailure { e ->
                Timber.w(e, "Failed to execute map snapshot binding operation")
            }
        }
    }
}

sealed class MapSnapshotViewState(
    open val spec: MapSnapshotSpec?
) {
    data object Idle : MapSnapshotViewState(null)
    data class Loading(override val spec: MapSnapshotSpec, val myJob: Job) :
        MapSnapshotViewState(spec)

    data class Failed(override val spec: MapSnapshotSpec) : MapSnapshotViewState(spec)
    data class Success(override val spec: MapSnapshotSpec) : MapSnapshotViewState(spec)

    val isLoading: Boolean
        get() = this is Loading && job?.isActive == true

    val isSuccess: Boolean
        get() = this is Success

    val job: Job?
        get() = (this as? Loading)?.myJob
}

@set:MainThread
@get:MainThread
var ImageView.stateTag: MapSnapshotViewState
    get() = getTag(R.id.map_snapshotter_state_tag) as? MapSnapshotViewState
        ?: MapSnapshotViewState.Idle
    set(value) {
        setTag(R.id.map_snapshotter_state_tag, value)
    }

@MainThread
private fun ImageView.clearSnapshot() {
    setBackgroundColor(ThemeColors.resolveColor(context, R.attr.suuntoItemBackgroundColor))
    setImageDrawable(null)
}

@MainThread
private fun ImageView.showErrorState() {
    setBackgroundColor(context.getColor(CR.color.very_light_gray))
    setImageDrawable(null)
}

@MainThread
private fun ImageView.showSnapshotWithTransition(drawable: Drawable) {
    val transitionDrawable = TransitionDrawable(
        arrayOf(
            ThemeColors.resolveColor(context, R.attr.suuntoItemBackgroundColor).toDrawable(),
            drawable
        )
    )

    setImageDrawable(transitionDrawable)
    transitionDrawable.startTransition(250)
}
