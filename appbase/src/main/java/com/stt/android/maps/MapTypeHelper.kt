package com.stt.android.maps

import android.content.Context
import android.content.SharedPreferences
import android.text.format.DateUtils
import androidx.annotation.DrawableRes
import androidx.core.content.edit
import com.stt.android.R
import com.stt.android.utils.STTConstants
import java.net.URLDecoder
import java.net.URLEncoder

object MapTypeHelper {
    private const val CURRENT_MAP_PROVIDER_VERSION: Int = 2

    private var dynamicMaps: List<MapType> = emptyList()
    private val dynamicMapsLock: Any = Any()

    val DEFAULT_MAP_TYPE: MapType = requireNotNull(BuiltInMapTypes.find(MAP_TYPE_DEFAULT))

    fun find(mapTypeName: String): MapType? =
        BuiltInMapTypes.find(mapTypeName)
            ?: synchronized(dynamicMapsLock) { dynamicMaps.find { it.name == mapTypeName } }

    fun getOrDefault(
        mapTypeName: String,
        defaultValue: MapType = DEFAULT_MAP_TYPE,
    ): MapType = find(mapTypeName) ?: defaultValue

    fun getBuiltInMaps(): List<MapType> = BuiltInMapTypes.getAll()

    fun getDynamicMaps(): List<MapType> = synchronized(dynamicMapsLock) { dynamicMaps }

    fun isCacheValid(context: Context): Boolean {
        val preferences = context.getMapPreferences()
        val mapProviderVersion = preferences.getInt(STTConstants.MapPreferences.KEY_MAP_PROVIDER_VERSION, 0)
        if (mapProviderVersion < CURRENT_MAP_PROVIDER_VERSION) {
            return false
        }

        val lastFetchedTime = preferences.getLong(STTConstants.MapPreferences.KEY_MAP_PROVIDER_LAST_FETCHED, 0)
        return System.currentTimeMillis() - lastFetchedTime < DateUtils.HOUR_IN_MILLIS
    }

    fun loadDynamicMapsFromCache(context: Context) {
        val all = context.getMapPreferences().all
        val dynamicMaps = ArrayList<MapType>(all.size)
        for ((key, value) in all) {
            if (key == STTConstants.MapPreferences.KEY_MAP_PROVIDER_LAST_FETCHED ||
                key == STTConstants.MapPreferences.KEY_MAP_PROVIDER_VERSION) {
                continue
            }

            val fields = (value as String).split("::")
            val requiresPremium = fields[0].toBoolean()
            // fields[1] was googleMapType
            val title = extractString(fields[2])
            val provider = extractString(fields[3])
            val credit = extractString(fields[4])
            // fields[5] was tileUrlTemplate
            // fields[6] was tileUrlHdpiTemplate
            val availableCountries = extractString(fields[7])
            val requiresFieldTester = fields.getOrNull(8)?.toBoolean() ?: false
            val iconUrl = fields.getOrNull(9)?.let(::decodeUrl).orEmpty()
            val styleUrl = fields.getOrNull(10)?.let(::decodeUrl).orEmpty()
            dynamicMaps.add(
                MapType(
                    name = key,
                    requiresPremium = requiresPremium,
                    titleText = title,
                    provider = provider,
                    iconUrl = iconUrl,
                    styleUrl = styleUrl,
                    credit = credit,
                    availableCountries = availableCountries?.split("-"),
                    requiresFieldTester = requiresFieldTester,
                )
            )
        }

        setDynamicMaps(dynamicMaps)
    }

    private fun extractString(string: String): String? = string.takeUnless { it == "null" }

    fun saveDynamicMaps(context: Context, dynamicMaps: List<MapType>) {
        context.getMapPreferences().edit {
            clear()

            for (mapType in dynamicMaps) {
                val value = mapType.requiresPremium.toString() + "::" +
                    "::" + // was: mapType.getBaseMapType()
                    mapType.titleText + "::" +
                    mapType.provider + "::" +
                    mapType.credit + "::" +
                    "::" + // was: mapType.tileUrlTemplate
                    "::" + // was: mapType.tileUrlHdpiTemplate
                    mapType.availableCountries
                        ?.takeUnless(List<*>::isEmpty)
                        ?.joinToString("-") + "::" +
                    mapType.requiresFieldTester + "::" +
                    encodeUrl(mapType.iconUrl) + "::" +
                    encodeUrl(mapType.styleUrl)
                putString(mapType.name, value)
            }
            putLong(STTConstants.MapPreferences.KEY_MAP_PROVIDER_LAST_FETCHED, System.currentTimeMillis())
            putInt(STTConstants.MapPreferences.KEY_MAP_PROVIDER_VERSION, CURRENT_MAP_PROVIDER_VERSION)
        }

        setDynamicMaps(dynamicMaps)
    }

    private fun setDynamicMaps(dynamicMaps: List<MapType>) {
        synchronized(dynamicMapsLock) {
            this.dynamicMaps = dynamicMaps.map { mapType ->
                // Set dynamic map icon locally because backend does not provide icons.
                mapType.copy(iconResource = getDynamicMapIconResource(mapType.name))
            }
        }
    }

    @DrawableRes
    private fun getDynamicMapIconResource(mapTypeName: String): Int = when (mapTypeName) {
        MAP_TYPE_FINLAND_BELECTRO_RASTER_TERRAIN_CONTOUR -> R.drawable.map_type_finland_terrain
        MAP_TYPE_MAPBOX_OPEN_STREET_MAP -> R.drawable.map_type_osm_normal
        MAP_TYPE_MAPBOX_OPEN_CYCLE_MAP -> R.drawable.map_type_osm_cycling
        MAP_TYPE_MAPBOX_OUTDOORS_MAP -> R.drawable.map_type_osm_outdoors
        MAP_TYPE_MAPBOX_LANDSCAPE_MAP -> R.drawable.map_type_osm_landscape
        MAP_TYPE_MAPBOX_NORWAY_KARTVERKET -> R.drawable.map_type_norway_terrain
        MAP_TYPE_MAPBOX_NORWAY_SEA_AND_LAKE -> R.drawable.map_type_norway_nautical
        MAP_TYPE_MAPBOX_SPAIN_INSTITUTO_GEOGRAFICO -> R.drawable.map_type_spain_terrain
        else -> R.drawable.map_type_generic
    }

    private fun encodeUrl(url: String): String = runCatching {
        URLEncoder.encode(url, "UTF-8")
    }.getOrNull().orEmpty()

    private fun decodeUrl(encodedUrl: String): String = runCatching {
        URLDecoder.decode(encodedUrl, "UTF-8")
    }.getOrNull().orEmpty()

    private fun Context.getMapPreferences(): SharedPreferences = getSharedPreferences(
        STTConstants.MapPreferences.MAP_PROVIDER_PREFS_NAME,
        Context.MODE_PRIVATE,
    )
}
