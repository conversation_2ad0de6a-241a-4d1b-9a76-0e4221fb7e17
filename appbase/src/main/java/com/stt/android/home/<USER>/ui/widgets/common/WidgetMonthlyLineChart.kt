package com.stt.android.home.dashboardv2.ui.widgets.common

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import java.time.LocalDate

@Composable
fun WidgetMonthlyLine<PERSON>hart(
    color: Color,
    beginDate: LocalDate,
    endDate: LocalDate,
    progresses: List<Float>,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current.density
    val textGray = MaterialTheme.colorScheme.mediumGrey
    val markMaxHeight = 36.dp
    val markMaxHeightFloat = LocalDensity.current.run { markMaxHeight.toPx() }
    val labelMargin = MaterialTheme.spacing.xxsmall
    val textMeasurer = rememberTextMeasurer()
    val textStyle = MaterialTheme.typography.bodySmall.merge(color = textGray)

    val beginDateLabel = beginDate.monthDayLabel
    val endDayLabel = endDate.monthDayLabel

    val labelHeight = remember(beginDateLabel, endDayLabel, textStyle, density) {
        (textMeasurer.measure(endDayLabel, textStyle).size.height / density).dp
    }

    WidgetLineChart(
        color = color,
        progresses = progresses,
        modifier = modifier
            .fillMaxWidth()
            .height(markMaxHeight + labelMargin + labelHeight),
        drawDashLine = { index -> progresses.size <= 7 || index % 2 == 0 }
    ) {
        val width = this.size.width

        if (beginDate != endDate) {
            drawText(
                textMeasurer = textMeasurer,
                text = beginDateLabel,
                style = textStyle,
                topLeft = Offset(0f, markMaxHeightFloat + labelMargin.toPx())
            )
        }

        val textWidth = textMeasurer.measure(endDayLabel, textStyle).size.width
        drawText(
            textMeasurer = textMeasurer,
            text = endDayLabel,
            style = textStyle,
            topLeft = Offset(width - textWidth, markMaxHeightFloat + labelMargin.toPx())
        )
    }
}

@Preview
@Composable
private fun WidgetMonthlyLineChartPreview(
    @PreviewParameter(WidgetMonthlyLineChartPreviewParameterProvider::class) data: Triple<List<Float>, LocalDate, LocalDate>
) {
    val (progresses, beginDate, endDate) = data

    WidgetMonthlyLineChart(
        color = Color.Red,
        beginDate = beginDate,
        endDate = endDate,
        progresses = progresses,
        modifier = Modifier.width(138.dp),
    )
}

private class WidgetMonthlyLineChartPreviewParameterProvider :
    PreviewParameterProvider<Triple<List<Float>, LocalDate, LocalDate>> {
    override val values: Sequence<Triple<List<Float>, LocalDate, LocalDate>> = sequenceOf(
        Triple(
            (1..30).map {
                if (it % 5 == 0) 1f else (Math.random() - 0.2).toFloat()
            },
            LocalDate.now().minusDays(29),
            LocalDate.now()
        ),

        Triple(
            (1..7).map {
                if (it % 5 == 0) 1f else (Math.random() - 0.2).toFloat()
            },
            LocalDate.now().minusDays(6),
            LocalDate.now()
        ),

        Triple(
            (0..6).map { -1f },
            LocalDate.now().minusDays(6),
            LocalDate.now()
        ),

        Triple(
            (0..1).map { -1f },
            LocalDate.now().atStartOfDay().toLocalDate(),
            LocalDate.now().atStartOfDay().toLocalDate()
        ),

        Triple(
            listOf(0f, 0.5f),
            LocalDate.now().atStartOfDay().toLocalDate(),
            LocalDate.now().atStartOfDay().toLocalDate()
        )
    )
}
