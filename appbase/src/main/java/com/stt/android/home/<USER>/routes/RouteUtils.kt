package com.stt.android.home.explore.routes

import android.content.Context
import android.text.SpannableStringBuilder
import com.github.mikephil.charting.data.Entry
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.SphericalUtil
import com.stt.android.R
import com.stt.android.domain.Point
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.RouteSegment
import com.stt.android.domain.routes.RouteVerticalDeltaCalc
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.routes.toLatLng
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CoordinateUtils
import com.stt.android.utils.Utf8ByteLengthInputFilter
import com.stt.android.utils.splitByCompareLast
import io.reactivex.Flowable
import timber.log.Timber
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.roundToInt

object RouteUtils {
    private const val DISTANCE_NOT_SET = -1.0

    // Actual range will be 20% more due to space reserved so that waypoints are not clipped
    private const val MIN_CHART_Y_RANGE_METERS = 50.0f
    private const val MIN_CHART_Y_RANGE_FEET = 164.0f
    private const val ALTITUDE_CHART_DATA_POINT_COUNT = 1024

    fun buildRouteSubtitle(
        distanceFromCurrentLocation: Double?,
        producerName: String?,
        measurementUnit: MeasurementUnit,
        context: Context
    ): String {
        val distanceText = distanceFromCurrentLocation?.let {
            formatRouteDistanceTo(
                distance = it,
                measurementUnit = measurementUnit,
                context = context
            )
        }
        val producerText = producerName?.takeIf { it.isNotBlank() }
        return listOfNotNull(distanceText, producerText).joinToString(" - ")
    }

    private fun formatRouteDistanceTo(
        distance: Double?,
        measurementUnit: MeasurementUnit,
        context: Context
    ): String = if (distance != null) {
        val convertedDistance = measurementUnit.toDistanceUnit(distance)
        val unit = context.getString(measurementUnit.distanceUnit)
        val formattedDistance = TextFormatter.formatDistance(convertedDistance)

        context.getString(
            R.string.route_distance_to,
            "$formattedDistance $unit"
        )
    } else {
        ""
    }

    @JvmStatic
    fun getRouteBounds(segments: List<RouteSegment>): LatLngBounds {
        val routePoints = ArrayList<LatLng>()
        for (segment in segments) {
            routePoints.addAll(routePointsToLatLngList(segment.routePoints))
        }
        val boundsBuilder = LatLngBounds.builder()
        for (routePoint in routePoints) {
            boundsBuilder.include(routePoint)
        }
        return boundsBuilder.build()
    }

    @JvmStatic
    fun calculateDistance(segments: List<RouteSegment>): Double {
        var distance = 0.0
        for ((_, _, _, routePoints) in segments) {
            distance += SphericalUtil.computeLength(routePointsToLatLngList(routePoints))
        }
        // Sometimes due to rounding errors we get negative values. That should not happen.
        return distance.coerceAtLeast(0.0)
    }

    @JvmStatic
    fun calculateDistanceByPoints(routePoints: List<Point>): Double {
        val distance = SphericalUtil.computeLength(routePointsToLatLngList(routePoints))
        return distance.coerceAtLeast(0.0)
    }

    @JvmStatic
    fun calculateDistance(startPoint: Point, endPoint: Point): Double {
        return routePointsToLatLngList(listOf(startPoint, endPoint)).let {
            SphericalUtil.computeDistanceBetween(it[0], it[1])
        }
    }

    @JvmStatic
    fun routePointsToLatLngList(routePoints: List<Point>): List<LatLng> {
        return routePoints.map { LatLng(it.latitude, it.longitude) }
    }

    @JvmStatic
    fun findRoutesWithDistance(
        routesFlowable: Flowable<List<Route>>,
        startPosition: Point?
    ): Flowable<List<Pair<Route, Double>>> {
        return routesFlowable
            .map { routes ->
                routes.map { route ->
                    val targetStartPoint = route.startPoint
                    val distance: Double = if (startPosition != null) {
                        CoordinateUtils.distanceBetween(
                            startPosition.latitude,
                            startPosition.longitude,
                            targetStartPoint.latitude,
                            targetStartPoint.longitude
                        )
                    } else {
                        DISTANCE_NOT_SET
                    }
                    route to distance
                }
            }
    }

    @JvmStatic
    fun findTopRoutesWithDistance(
        routesFlowable: Flowable<List<TopRoute>>,
        startPosition: Point?
    ): Flowable<List<Pair<TopRoute, Double>>> {
        return routesFlowable
            .map { routes ->
                routes.map { route ->
                    val targetStartPoint = route.startPoint
                    val distance: Double = if (startPosition != null) {
                        CoordinateUtils.distanceBetween(
                            startPosition.latitude,
                            startPosition.longitude,
                            targetStartPoint.latitude,
                            targetStartPoint.longitude
                        )
                    } else {
                        DISTANCE_NOT_SET
                    }
                    route to distance
                }
            }
    }

    /**
     * Calculate data points for route altitude graph. This function iterates through all the
     * segments and points in the route and reduces the number of points to a maximum of
     * [ALTITUDE_CHART_DATA_POINT_COUNT]. The X axis is based on distance so that the graph looks
     * correct even if the original route points are not evenly spread.
     *
     * Returns a [RouteAltitudeChartData] object that can be passed on to the route altitude graph
     * view.
     */
    @JvmStatic
    fun calculateAltitudeChartData(
        segments: List<RouteSegment>,
        measurementUnit: MeasurementUnit
    ): RouteAltitudeChartData {
        val totalDistance = calculateDistance(segments)
        val entries = mutableMapOf<Int, Float>() // Data point index to altitude value
        val indexList = mutableMapOf<Int, Int>() // Data point index to index in the original list
        val waypointEntries = mutableMapOf<Int, Float>() // Data point index to altitude value

        var altitudeValueCount = 0
        var cumulativeDistance = 0.0
        var previousPoint: Point? = null
        var min: Float? = null
        var max: Float? = null
        var index = 0
        for (segment in segments) {
            for (point in segment.routePoints) {
                previousPoint?.let {
                    cumulativeDistance += SphericalUtil.computeLength(
                        routePointsToLatLngList(listOf(it, point))
                    )
                }

                point.altitude?.let {
                    // Route point has altitude information. Calculate at which index it falls onto
                    // in the chart data.
                    val dataPointIndex: Int = getDataPointIndex(cumulativeDistance, totalDistance)
                    val altitude = measurementUnit.toAltitudeUnit(it).toFloat()
                    // Keep track of highest and lowest point in the original data here in order
                    // to avoid iterating through the data multiple times. Ignore 0 values here.
                    // minValue and maxValue will be handled as 0 if null below.
                    if (altitude != 0f) {
                        if (min == null || altitude < min!!) {
                            min = altitude
                        }

                        if (max == null || altitude > max!!) {
                            max = altitude
                        }
                    }

                    // Any potential existing values with the same index is overwritten, with first
                    // item as the exception so that we can make sure the first point is kept.
                    val shouldOverwrite = dataPointIndex != 0 || !entries.containsKey(dataPointIndex)
                    if (shouldOverwrite) {
                        entries[dataPointIndex] = altitude
                        indexList[dataPointIndex] = index

                        // If it's a waypoint, but not turn-by-turn waypoint
                        if (point.type != null && point.type !in 48..63) {
                            waypointEntries[dataPointIndex] = altitude
                        }

                        altitudeValueCount++
                    }
                }
                previousPoint = point
                index++
            }
        }

        if (altitudeValueCount < 2) {
            // Require at least two altitude points to show any data
            entries.clear()
        }

        // Make sure the reduced data are properly sorted
        val reducedEntries = entries.toSortedMap()
            .map { (dataPointIndex, altitude) ->
                Entry(dataPointIndex.toFloat(), altitude)
            }
        val reducedIndexList = indexList.values.sorted()
        val reducedWaypointEntries = waypointEntries.toSortedMap()
            .map { (dataPointIndex, altitude) ->
                Entry(dataPointIndex.toFloat(), altitude)
            }

        val minYRange = if (measurementUnit == MeasurementUnit.IMPERIAL) {
            MIN_CHART_Y_RANGE_FEET
        } else {
            MIN_CHART_Y_RANGE_METERS
        }

        // Enforce at least minYRange vertical range by increasing the maximum
        val minValue = floor(min ?: 0.0f)
        val maxValue = ceil(maxOf(max ?: 0.0f, minValue + minYRange))

        return RouteAltitudeChartData(
            reducedEntries,
            reducedIndexList,
            minValue,
            maxValue,
            measurementUnit,
            reducedWaypointEntries
        )
    }

    private fun getDataPointIndex(
        cumulativeDistance: Double,
        totalDistance: Double
    ): Int {
        return if (cumulativeDistance > 0) {
            (cumulativeDistance * ALTITUDE_CHART_DATA_POINT_COUNT / totalDistance)
                .roundToInt()
                .coerceIn(0 until ALTITUDE_CHART_DATA_POINT_COUNT)
        } else {
            0
        }
    }

    @JvmStatic
    fun getActivityIdsFromActivityTypes(activityTypes: List<ActivityType>) =
        activityTypes.map { it.id }

    @JvmStatic
    fun newStraightSegment(start: LatLng, end: LatLng, position: Int): RouteSegment {
        val startPoint = Point(start.longitude, start.latitude, 0.0)
        val endPoint = Point(end.longitude, end.latitude, 0.0)
        return RouteSegment(
            startPoint = startPoint,
            endPoint = endPoint,
            position = position,
            routePoints = listOf(startPoint, endPoint)
        )
    }

    @JvmStatic
    fun activityIdsToActivityTypeList(route: Route): List<ActivityType> =
        route.activityIds.map { ActivityType.valueOf(it) }

    @JvmStatic
    fun getActivitiesSimpleNames(route: Route): List<String> =
        route.activityIds.map { ActivityType.valueOf(it).simpleName }

    @JvmStatic
    fun splitAtWaypointsIfNeeded(segments: List<RouteSegment>): List<RouteSegment> {
        val res = mutableListOf<RouteSegment>()
        var position = segments.firstOrNull()?.position ?: 0

        var previousEndPoint: Point? = null
        var previousWaypoint: Point? = null
        for (segment in segments) {
            val points =
                if (previousWaypoint != null && previousWaypoint == segment.routePoints.firstOrNull()) {
                    // The first point in this segment is a duplicated waypoint from previous segment.
                    // Keep the point, but remove name and type. This ensures route is continuous
                    // but there are no duplicated waypoints.
                    segment.routePoints.mapIndexed { index, point ->
                        if (index == 0) {
                            point.copy(name = null, type = null)
                        } else {
                            point
                        }
                    }
                } else {
                    segment.routePoints
                }

            if (points.size > 1 && points.subList(0, points.size - 1).any { it.isWaypoint }) {
                // This segment has a waypoint that is not the last point -> split into multiple
                // segments
                points
                    .splitByCompareLast { current, _ -> current.isWaypoint }
                    .mapTo(res) { splitPoints ->
                        val startPoint = splitPoints.first().copy(name = null, type = null)
                        val endPoint = splitPoints.last().copy(name = null, type = null)

                        val routePoints = if (previousEndPoint != startPoint) {
                            // Ensure that the route is continuous by starting the segment with the
                            // end point of the previous segment
                            listOfNotNull(previousEndPoint) + splitPoints
                        } else {
                            // Previous end point is equal to this segments start point. No need
                            // to duplicate it
                            splitPoints
                        }

                        previousEndPoint = endPoint
                        val verticalDelta =
                            RouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(routePoints)
                        RouteSegment(
                            startPoint = startPoint,
                            endPoint = endPoint,
                            position = position++,
                            routePoints = routePoints,
                            ascent = verticalDelta?.ascent,
                            descent = verticalDelta?.descent
                        )
                    }
            } else {
                // No need to split
                res.add(segment.copy(position = position++))
            }

            previousEndPoint = res.last().endPoint
            previousWaypoint = res.last().routePoints.last().takeIf { it.isWaypoint }
        }

        return res
    }

    @JvmStatic
    fun connectSegmentsIfNeeded(segments: List<RouteSegment>): List<RouteSegment> {
        return segments.runningReduce { prev, cur ->
            val prevEnd = prev.routePoints.lastOrNull()
            val curStart = cur.routePoints.firstOrNull()

            if (prevEnd != null &&
                curStart != null &&
                (prevEnd.latitude != curStart.latitude || prevEnd.longitude != curStart.longitude)
            ) {
                val connectedRoutePoints =
                    listOf(prevEnd.copy(name = null, type = null)) + cur.routePoints
                cur.copy(routePoints = connectedRoutePoints)
            } else {
                cur
            }
        }
    }

    private val Point.isWaypoint: Boolean
        get() = type != null

    @JvmStatic
    fun calculateWaypointHeading(route: List<Point>): Float {
        if (route.size <= 1) return 0f
        val (from, to) = route.takeLast(2).map(Point::toLatLng)
        // Heading range is [-180, 180]. Convert to degrees.
        return 360 + SphericalUtil.computeHeading(from, to).toFloat()
    }

    @JvmStatic
    fun calculateWaypointRotation(heading: Float, mapBearing: Float): Float {
        val correction = 360 - mapBearing // Take map bearing into account
        return heading + correction
    }

    @JvmStatic
    fun filterRouteName(routeName: String?): String = SpannableStringBuilder().apply {
        filters += Utf8ByteLengthInputFilter(Route.ROUTE_NAME_MAX_BYTE_LENGTH)
    }.insert(0, routeName ?: "").toString()

    /**
     * Make sure segments have incrementing position values starting from 0. This is done either
     * by adjusting the position values or by reordering the segments to match position values.
     */
    @JvmStatic
    fun sanitizeRouteSegmentPositionValues(
        routeKey: String,
        segments: List<RouteSegment>
    ): List<RouteSegment> {
        val actualPositionValues = segments.map { it.position }
        val expectedPositionValues = segments.indices.toList()
        return when {
            actualPositionValues == expectedPositionValues -> {
                // Segment position values match indices (start from 0 and increment by one per segment)
                // No need to change anything
                segments
            }

            actualPositionValues.sorted() != expectedPositionValues -> {
                // Position values are messed up (don't start from 0, duplicates, missing values, or
                // non-incremental values). Assume segments are in proper order and fix position values.

                // Log to Crashlytics
                logRoutePositionValuesToCrashlytics(routeKey, segments)
                Timber.w(InvalidRouteSegmentPositions("Position values contain out-of-bounds or duplicate values"))

                // Return segments and replace position values
                segments.mapIndexed { index, routeSegment -> routeSegment.copy(position = index) }
            }

            else -> {
                // Position values look legit but could be shuffled. Use simple heuristic to figure out
                // whether to trust segment order or position values.
                val sortedByPosition = segments.sortedBy { it.position }
                val connectionsInSegmentOrder = countSeamlesslyConnectingSegments(segments)
                val connectionsInPositionOrder = countSeamlesslyConnectingSegments(sortedByPosition)

                // Log to Crashlytics
                logRoutePositionValuesToCrashlytics(routeKey, segments)
                Timber.w(
                    InvalidRouteSegmentPositions("Position values out-of-order"),
                    "connectionsInSegmentOrder=$connectionsInSegmentOrder connectionsInPositionOrder=$connectionsInPositionOrder"
                )

                // Return reordered segments or segments with replaced position values based on
                // which one looks more contiguous
                if (connectionsInPositionOrder > connectionsInSegmentOrder) {
                    sortedByPosition
                } else {
                    segments
                }.mapIndexed { index, routeSegment -> routeSegment.copy(position = index) }
            }
        }
    }

    // Count how many segments start at the end point of the previous segment
    private fun countSeamlesslyConnectingSegments(segments: List<RouteSegment>): Int =
        segments.windowed(2) { (prev, next) ->
            prev.routePoints.size > 1 && next.routePoints.size > 1 &&
                abs(prev.routePoints.last().latitude - next.routePoints.first().latitude) < 0.00001 &&
                abs(prev.routePoints.last().longitude - next.routePoints.first().longitude) < 0.00001
        }.count { it }

    private fun logRoutePositionValuesToCrashlytics(
        routeKey: String,
        segments: List<RouteSegment>
    ) {
        Timber.w("Route key $routeKey")
        Timber.w("${segments.size} segment(s), ${segments.sumOf { it.routePoints.size }} points")
        for ((index, segment) in segments.withIndex()) {
            Timber.w("index $index: position=${segment.position} points=${segment.routePoints.size} WPT=${segment.waypointType()}")
            Timber.w("index $index: start=${segment.startPoint} end=${segment.endPoint}")
        }
    }
}

fun LatLng.toPoint() = Point(longitude, latitude, 0.0)

private class InvalidRouteSegmentPositions(message: String) : IllegalStateException(message)
