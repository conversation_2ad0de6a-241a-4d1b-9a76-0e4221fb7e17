package com.stt.android.home.diary.diarycalendar

import android.os.Bundle
import androidx.lifecycle.ViewModel
import com.stt.android.utils.CalendarProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.time.LocalDate
import java.time.MonthDay
import java.time.Year
import java.time.YearMonth
import javax.inject.Inject

@HiltViewModel
class CalendarContainerViewModel @Inject constructor(
    private val calendarProvider: CalendarProvider
) : ViewModel() {
    var selectedYearMonth: YearMonth = YearMonth.now()
    var selectedYear: Year = Year.now()
    var startOfSelectedWeek: LocalDate = LocalDate.now()

    private val _selectedTab = MutableStateFlow<Pair<Int, Bundle?>?>(null)
    val selectedTab = _selectedTab.asStateFlow()
    var selectedTabPosition: Int?
        get() = _selectedTab.value?.first
        set(value) {
            _selectedTab.value = value?.let { Pair(it, null) }
        }

    // Navigating from month calendar to week calendar
    fun getStartOfWeekFromMonthToWeek(): LocalDate {
        val dayOfWeek = calendarProvider.getDayOfWeekField()

        return if (YearMonth.now() == selectedYearMonth) {
            // Current month -> switch to current week
            LocalDate.now().with(dayOfWeek, 1)
        } else {
            // Any other month -> switch to the week that includes the first day-of-month
            selectedYearMonth.atDay(1).with(dayOfWeek, 1)
        }
    }

    // Navigating from year calendar to week calendar
    fun getStartOfWeekFromYearToWeek(): LocalDate {
        val dayOfWeek = calendarProvider.getDayOfWeekField()

        return if (selectedYear == Year.now()) {
            // Current year -> switch to current week
            LocalDate.now().with(dayOfWeek, 1)
        } else {
            // Any other month -> switch to the week that includes the first day-of-year
            selectedYear.atDay(1).with(dayOfWeek, 1)
        }
    }

    // Navigating from last 30 days calendar to week calendar
    fun getStartOfWeekFromLast30DaysToWeek(): LocalDate {
        val dayOfWeek = calendarProvider.getDayOfWeekField()
        return LocalDate.now().with(dayOfWeek, 1)
    }

    // Navigating from week calendar to month calendar
    fun getStartOfMonthFromWeekToMonth(): LocalDate {
        val lastDayOfWeek = startOfSelectedWeek.plusDays(6)
        return lastDayOfWeek.withDayOfMonth(1)
    }

    // Navigating from year calendar to month calendar
    fun getStartOfMonthFromYearToMonth(): LocalDate {
        return if (selectedYear == Year.now()) {
            selectedYear.atMonthDay(MonthDay.now())
        } else {
            selectedYear.atDay(1)
        }
    }

    // Navigating from month calendar to year calendar
    fun getYearFromMonthToYear() = selectedYearMonth.year

    // Navigating from week calendar to year calendar
    fun getYearFromWeekToYear() = startOfSelectedWeek.plusDays(6).year

    fun initPickerDate(): LocalDate? {
        val selectedTabPosition = selectedTabPosition
        val today = LocalDate.now()
        return when (selectedTabPosition) {
            WEEK_TAB_POSITION -> {
                val endOfWeek = startOfSelectedWeek.plusDays(6)
                if (endOfWeek.isAfter(today)) today else startOfSelectedWeek
            }

            MONTH_TAB_POSITION -> if (selectedYearMonth >= YearMonth.of(today.year, today.month)) {
                today
            } else {
                selectedYearMonth.atDay(1)
            }

            YEAR_TAB_POSITION -> if (selectedYear >= Year.of(today.year)) {
                today
            } else {
                selectedYear.atDay(1)
            }

            LAST_30_DAYS_TAB_POSITION -> today
            else -> null
        }
    }

    fun selectTab(tabPosition: Int, args: Bundle? = null) {
        _selectedTab.value = Pair(tabPosition, args)
    }

    companion object {
        const val WEEK_TAB_POSITION = 0
        const val MONTH_TAB_POSITION = 1
        const val YEAR_TAB_POSITION = 2
        const val LAST_30_DAYS_TAB_POSITION = 3
    }
}
