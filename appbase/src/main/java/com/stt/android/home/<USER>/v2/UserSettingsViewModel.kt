package com.stt.android.home.settings.v2

import android.content.Context
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.compose.base.BaseViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.userSettings
import com.stt.android.data.usersettings.UserSettingsSyncMonitor
import com.stt.android.di.IsSuuntoFlavor
import com.stt.android.domain.STTErrorCodes
import com.stt.android.domain.user.Sex
import com.stt.android.exceptions.BackendException
import com.stt.android.exceptions.remote.ServerError
import com.stt.android.home.settings.v2.location.LocationSelectionActivity
import com.stt.android.home.settings.v2.usecase.UpdateBirthDateUseCase
import com.stt.android.home.settings.v2.usecase.UpdateGenderUseCase
import com.stt.android.home.settings.v2.usecase.UpdateHeightUseCase
import com.stt.android.home.settings.v2.usecase.UpdateWeightUseCase
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.ui.utils.MeasurementUnitAware
import com.stt.android.social.userprofileV2.usecase.UpdateAvatarUseCase
import com.stt.android.social.userprofileV2.usecase.UpdateCoverUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import javax.inject.Inject

@HiltViewModel
class UserSettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    @IsSuuntoFlavor val isSuuntoFlavor: Boolean,
    private val userSettingsController: UserSettingsController,
    private val currentUserController: CurrentUserController,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val updateAvatarUseCase: UpdateAvatarUseCase,
    private val updateCoverUseCase: UpdateCoverUseCase,
    private val userSettingsSyncMonitor: UserSettingsSyncMonitor,
    private val updateWeightUseCase: UpdateWeightUseCase,
    private val updateHeightUseCase: UpdateHeightUseCase,
    private val updateGenderUseCase: UpdateGenderUseCase,
    private val updateBirthDateUseCase: UpdateBirthDateUseCase,
) : BaseViewModel() {
    val showHeightAndHeartRateSettings: Boolean
        get() = isSuuntoFlavor

    val tempProfilePictureFile: File
        get() = updateAvatarUseCase.tempProfilePictureFile

    val tempCoverPictureFile: File
        get() = updateCoverUseCase.tempProfilePictureFile

    private val _uploadImageStatus = MutableStateFlow(UploadImageUIState())
    val uploadImageStatus: StateFlow<UploadImageUIState> = _uploadImageStatus

    val userSettingsViewData = combine(
        userSettingsController.userSettings(),
        currentUserController.currentUserFlow,
    ) { userSettings, user ->
        UserSettingsViewData.fromUserSettings(context, userSettings, user)
    }.stateIn(
        viewModelScope,
        SharingStarted.WhileSubscribed(5_000),
        UserSettingsViewData.fromUserSettings(
            context,
            userSettingsController.settings,
            currentUserController.currentUser,
        )
    )

    private val _event = Channel<UserSettingsEvent>(Channel.BUFFERED)
    val event = _event.receiveAsFlow()

    private var bioBeforeUpdate: String? = null
    private var realNameBeforeUpdate: String? = null

    init {
        monitorSettingsSync()
    }

    fun updateUserProfileDescription(newDescription: String) {
        bioBeforeUpdate = userSettingsController.settings.description
        sendDescriptionAnalytics(hasDescription = !userSettingsController.settings.description.isNullOrBlank())
        userSettingsController.storeSettings(
            userSettingsController.settings.setProfileDescription(newDescription)
        )
        currentUserController.store(currentUserController.currentUser.copy(description = newDescription))
    }

    fun resetUploadImageStatus() {
        _uploadImageStatus.update { it.copy(isLoading = false, errorCode = null) }
    }

    fun updateProfilePicture(
        data: Intent,
        isCoverPhoto: Boolean = false
    ) {
        val flow = if (isCoverPhoto) {
            updateCoverUseCase(data, context)
        } else {
            updateAvatarUseCase(data, context)
        }
        viewModelScope.launch {
            flow.collect { state ->
                _uploadImageStatus.update {
                    it.copy(
                        isLoading = state.isLoading,
                        errorCode = state.error?.let { e ->
                            if (e is BackendException) e.error.code else STTErrorCodes.UNKNOWN.code
                        }
                    )
                }
            }
        }
    }

    fun updateUserProfileShowLocale(newShowLocale: Boolean) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setShowLocale(newShowLocale)
        )
    }

    fun updateUserProfileRealName(newRealName: String) {
        realNameBeforeUpdate = userSettingsController.settings.realName
        userSettingsController.storeSettings(
            userSettingsController.settings.setRealName(newRealName)
        )
        currentUserController.store(currentUserController.currentUser.copy(realName = newRealName))
    }

    fun updateWeight(weightString: String) {
        updateWeightUseCase(weightString)
    }

    fun updateHeight(heightInCentimeters: Int) {
        updateHeightUseCase(heightInCentimeters)
    }

    fun updateGender(gender: Sex) {
        updateGenderUseCase(gender)
    }

    fun updateBirthDate(birthDateMillis: Long) {
        updateBirthDateUseCase(birthDateMillis)
    }

    private fun sendDescriptionAnalytics(hasDescription: Boolean) {
        if (hasDescription) {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_EDIT
            )
        } else {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.PROFILE_DESCRIPTION_ADD
            )
        }
    }

    fun openLocationSelection(context: Context) {
        val intent = LocationSelectionActivity.newStartIntent(context)
        context.startActivity(intent)
    }

    private fun monitorSettingsSync() {
        viewModelScope.launch {
            userSettingsSyncMonitor.syncState
                // Skip the first event (which might be a cached/replayed event) and wait for new events
                .drop(1)
                .collect { event ->
                    if (bioBeforeUpdate == null && realNameBeforeUpdate == null) return@collect
                    when (event) {
                        is UserSettingsSyncMonitor.SyncEvent.Started -> {}

                        is UserSettingsSyncMonitor.SyncEvent.Failed -> {
                            Timber.w(event.throwable, "User settings sync failed")
                            if (event.throwable is ServerError.SensitiveWords) {
                                _event.send(UserSettingsEvent.SensitiveWordsError)
                                restoreRealNameOrBio()
                            } else {
                                onRealNameOrBioConfirmed()
                            }
                        }

                        is UserSettingsSyncMonitor.SyncEvent.Success -> {
                            onRealNameOrBioConfirmed()
                        }
                    }
                }
        }
    }

    private fun restoreRealNameOrBio() {
        bioBeforeUpdate?.let {
            userSettingsController.storeSettings(
                userSettingsController.settings.setProfileDescription(it)
            )
            currentUserController.store(currentUserController.currentUser.copy(description = it))
        }
        realNameBeforeUpdate?.let {
            userSettingsController.storeSettings(
                userSettingsController.settings.setRealName(it)
            )
            currentUserController.store(currentUserController.currentUser.copy(realName = it))
        }
        bioBeforeUpdate = null
        realNameBeforeUpdate = null
    }

    private fun onRealNameOrBioConfirmed() {
        realNameBeforeUpdate = null
        bioBeforeUpdate = null
    }
}

data class UploadImageUIState(
    val isLoading: Boolean = false,
    val errorCode: Int? = null
)

sealed class UserSettingsEvent {
    data object SensitiveWordsError : UserSettingsEvent()
}

