package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.home.diary.diarycalendar.planner.models.AnsweredQuestion
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGridItem

@Composable
fun PersonalizedDetails(
    answeredQuestions: List<AnsweredQuestion>,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        ListItem(
            headlineContent = {
                Text(
                    text = stringResource(R.string.workout_planner_personalized_details),
                    style = MaterialTheme.typography.bodyXLargeBold,
                )
            },
        )
        answeredQuestions.forEach { answer ->
            WorkoutValuesGridItem(
                workoutValueGridItem = WorkoutValuesGridItemData(
                    name = answer.question,
                    value = answer.answer,
                ),
                onValueClicked = {  },
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PersonalizedDetailsPreview() {
    M3AppTheme {
        PersonalizedDetails(
            answeredQuestions = listOf(
                AnsweredQuestion(
                    question = "How much are you ready to train in maximum in a week?",
                    answer = "10-15 hours"
                ),
                AnsweredQuestion(
                    question = "Which days are your preferred rest days?",
                    answer = "Monday, Wednesday, Saturday"
                ),
                AnsweredQuestion(
                    question = "Tuesday, Sunday",
                    answer = "10-15 hours"
                ),
                AnsweredQuestion(
                    question = "End date",
                    answer = "25.6.2024"
                ),
            )
        )
    }
}
