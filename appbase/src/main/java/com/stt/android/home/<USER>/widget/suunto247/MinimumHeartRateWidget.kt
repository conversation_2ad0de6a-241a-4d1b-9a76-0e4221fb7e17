package com.stt.android.home.dashboard.widget.suunto247

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.databinding.DashboardWidgetMinimumHeartRateBinding
import com.stt.android.home.dashboard.widget.DashboardWidget
import com.stt.android.home.dashboard.widget.DashboardWidgetDelegate
import com.stt.android.home.dashboard.widget.DisableableDashboardWidgetView
import com.stt.android.home.dashboard.widget.WidgetHeaderData
import com.stt.android.home.dashboard.widget.boldAsNoProgressBarPrimaryText
import com.stt.android.home.dashboard.widget.boldPrimaryTextSection
import com.stt.android.home.dashboard.widget.setHeightKeepingChartProportionalHeight

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class MinimumHeartRateWidget @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr),
    DisableableDashboardWidgetView,
    DashboardWidget by DashboardWidgetDelegate() {
    private val mainColorRes: Int = R.color.dashboard_widget_minimum_heart_rate
    private val binding =
        DashboardWidgetMinimumHeartRateBinding.inflate(LayoutInflater.from(context), this, true)

    override val clickContainer: View
        get() = binding.dashboardWidgetHrContainer

    override val removeButton: View
        get() = binding.dashboardWidgetHrRemoveButton

    init {
        binding.hideDescriptionText = false
        binding.hideChart = false
        (binding.dashboardWidgetHrHeaderImage.background.mutate() as GradientDrawable).color =
            ContextCompat.getColorStateList(context, mainColorRes)
    }

    @set:[ModelProp]
    var data: MinimumHeartRateWidgetData? = null

    @AfterPropsSet
    fun bindData() {
        data?.also { data ->
            val (primaryText, secondaryText) = if (data.todayMinHeartRateBpm != MinimumHeartRateWidgetData.NO_HR_VALUE) {
                val primaryTextSpannable =
                    SpannableString("${data.todayMinHeartRateBpm} ${context.getString(com.stt.android.core.R.string.bpm)}")
                val endBoldIndex = primaryTextSpannable.indexOf(" ")
                boldPrimaryTextSection(context, primaryTextSpannable, 0, endBoldIndex)

                primaryTextSpannable to context.getString(R.string.dashboard_widget_minimum_heart_rate_min_heart_rate)
            } else {
                getEmptyTitle(context) to null
            }

            setHeaderData(WidgetHeaderData(primaryText, secondaryText, null))
            binding.descriptionText = if (data.daysWithData > 1) {
                context.resources.getQuantityString(
                    R.plurals.dashboard_widget_minimum_heart_rate_n_day_average,
                    data.daysWithData,
                    data.daysWithData,
                    data.average
                )
            } else if (data.daysWithData == 1) {
                ""
            } else {
                context.getString(R.string.dashboard_widget_no_data_from_last_7_days)
            }
        } ?: run {
            setHeaderData(
                WidgetHeaderData(
                    primaryText = getEmptyTitle(context),
                    secondaryText = null,
                    progressPercent = null
                )
            )
            binding.descriptionText = context.getString(R.string.dashboard_widget_no_data_from_last_7_days)
        }

        binding.dashboardWidgetHeartRateChart.setDailyMinHeartRates(
            data?.pastWeekDailyMinHeartRatesBpm ?: emptyList(),
            today
        )

        bindDashboardWidgetView(this)
    }

    private fun getEmptyTitle(context: Context): CharSequence {
        val spannable =
            SpannableString(context.getString(R.string.dashboard_widget_minimum_heart_rate_title))
        boldAsNoProgressBarPrimaryText(context, spannable)
        return spannable
    }

    private fun setHeaderData(data: WidgetHeaderData?) {
        binding.dashboardWidgetHrHeader.progressColorRes = mainColorRes
        binding.dashboardWidgetHrHeader.data = data
    }

    override fun bindDisplayedAsEnabled(enabled: Boolean) {
        binding.descriptionTextColor = if (enabled) {
            ThemeColors.primaryTextColor(context)
        } else {
            ContextCompat.getColor(context, R.color.light_gray_font_color)
        }
        binding.dashboardWidgetHeartRateChart.widgetDisplaysAsEnabled = enabled
    }

    fun setHideDescriptionText(hide: Boolean) {
        binding.hideDescriptionText = hide
    }

    fun setHideChart(hide: Boolean) {
        binding.hideChart = hide
    }

    /**
     * Sets height of the widget in a way that keeps the amount of vertical space
     * the chart takes proportionally the same as originally
     */
    fun setHeightKeepingChartProportionalHeight(heightPixels: Int) {
        setHeightKeepingChartProportionalHeight(
            heightPixels,
            context,
            binding.dashboardWidgetHrContainer,
            binding.dashboardWidgetHeartRateChart
        )
    }

    fun setWidth(widthPixels: Int) {
        binding.dashboardWidgetHrContainer.updateLayoutParams {
            width = widthPixels
        }
    }

    fun executePendingBindings() {
        binding.executePendingBindings()
        binding.dashboardWidgetHrHeader.executePendingBindings()
        binding.dashboardWidgetHeartRateChart.executePendingBindings()
    }
}

data class MinimumHeartRateWidgetData(
    /**
     * 7 days worth of daily minimum recorded heart rates, from oldest to newest, in bpm.
     * [NO_HR_VALUE] constant indicates no data for the day
     */
    val pastWeekDailyMinHeartRatesBpm: List<Int>,
    /**
     * Today's minimum recorded heart rate, in bpm.
     * [NO_HR_VALUE] constant indicates no data.
     *
     */
    val todayMinHeartRateBpm: Int,
    val average: Int,
    val daysWithData: Int
) {
    companion object {
        const val NO_HR_VALUE = -1
    }
}
