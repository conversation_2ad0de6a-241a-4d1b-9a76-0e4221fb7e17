package com.stt.android.home.settings.v2.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.domain.user.Sex
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

@Composable
fun WeightInputDialog(
    currentWeight: String,
    onWeightChanged: (String) -> Unit,
    onDismiss: () -> Unit,
) {
    var weightText by remember { mutableStateOf(currentWeight) }
    var isError by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.dialog_title_settings_general_user_settings_weight),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = weightText,
                    onValueChange = { newValue ->
                        weightText = newValue
                        isError = false
                    },
                    label = { Text(stringResource(R.string.settings_general_user_settings_weight)) },
                    suffix = { Text("kg") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    isError = isError,
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                
                if (isError) {
                    Text(
                        text = "Invalid weight",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val weight = weightText.toFloatOrNull()
                    if (weight != null && weight in 20f..300f) {
                        onWeightChanged(weightText)
                        onDismiss()
                    } else {
                        isError = true
                    }
                }
            ) {
                Text(stringResource(android.R.string.ok))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(android.R.string.cancel))
            }
        }
    )
}


@Composable
fun HeightInputDialog(
    currentHeightInCentimeters: Int,
    onHeightChanged: (Int) -> Unit,
    onDismiss: () -> Unit,
) {
    var heightText by remember { mutableStateOf(currentHeightInCentimeters.toString()) }
    var isError by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.dialog_title_settings_general_user_settings_height),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = heightText,
                    onValueChange = { newValue ->
                        heightText = newValue
                        isError = false
                    },
                    label = { Text(stringResource(R.string.settings_general_user_settings_height)) },
                    suffix = { Text("kg") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    isError = isError,
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                if (isError) {
                    Text(
                        text = "Invalid height",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val height = heightText.toIntOrNull()
                    if (height != null && height in 20..300) {
                        onHeightChanged(height)
                        onDismiss()
                    } else {
                        isError = true
                    }
                }
            ) {
                Text(stringResource(android.R.string.ok))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(android.R.string.cancel))
            }
        }
    )
}

@Composable
fun GenderSelectionDialog(
    currentGender: Sex,
    onGenderChanged: (Sex) -> Unit,
    onDismiss: () -> Unit,
) {
    val genderOptions = listOf(
        Sex.MALE to stringResource(R.string.settings_general_user_settings_gender_male),
        Sex.FEMALE to stringResource(R.string.settings_general_user_settings_gender_female)
    )
    
    var selectedGender by remember { mutableStateOf(currentGender) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.settings_general_user_settings_gender),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column {
                genderOptions.forEach { (value, label) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedGender == value,
                                onClick = { selectedGender = value }
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedGender == value,
                            onClick = { selectedGender = value }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = label,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onGenderChanged(selectedGender)
                    onDismiss()
                }
            ) {
                Text(stringResource(android.R.string.ok))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(android.R.string.cancel))
            }
        }
    )
}

@Composable
fun BirthDatePickerDialog(
    currentBirthDate: Long?,
    onBirthDateChanged: (Long) -> Unit,
    onDismiss: () -> Unit,
) {
    val context = LocalContext.current
    
    // Convert current birth date to LocalDate
    val currentDate = currentBirthDate?.let {
        java.time.Instant.ofEpochMilli(it)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()
    } ?: LocalDate.now().minusYears(25)
    
    var selectedYear by remember { mutableStateOf(currentDate.year) }
    var selectedMonth by remember { mutableStateOf(currentDate.monthValue) }
    var selectedDay by remember { mutableStateOf(currentDate.dayOfMonth) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.dialog_title_settings_general_user_settings_age),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Year selection
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "year",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        TextButton(
                            onClick = { if (selectedYear > 1900) selectedYear-- }
                        ) {
                            Text("-")
                        }
                        
                        Text(
                            text = selectedYear.toString(),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                        
                        TextButton(
                            onClick = { if (selectedYear < LocalDate.now().year) selectedYear++ }
                        ) {
                            Text("+")
                        }
                    }
                }
                
                // Month selection
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "month",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        TextButton(
                            onClick = { if (selectedMonth > 1) selectedMonth-- }
                        ) {
                            Text("-")
                        }
                        
                        Text(
                            text = getMonthName(selectedMonth),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                        
                        TextButton(
                            onClick = { if (selectedMonth < 12) selectedMonth++ }
                        ) {
                            Text("+")
                        }
                    }
                }
                
                // Day selection
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "day",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        TextButton(
                            onClick = { if (selectedDay > 1) selectedDay-- }
                        ) {
                            Text("-")
                        }
                        
                        Text(
                            text = selectedDay.toString(),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                        
                        TextButton(
                            onClick = { 
                                val maxDay = getMaxDayOfMonth(selectedYear, selectedMonth)
                                if (selectedDay < maxDay) selectedDay++
                            }
                        ) {
                            Text("+")
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val calendar = Calendar.getInstance().apply {
                        set(selectedYear, selectedMonth - 1, selectedDay, 0, 0, 0)
                        set(Calendar.MILLISECOND, 0)
                    }
                    onBirthDateChanged(calendar.timeInMillis)
                    onDismiss()
                }
            ) {
                Text(stringResource(android.R.string.ok))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(android.R.string.cancel))
            }
        }
    )
}

@Composable
private fun getMonthName(month: Int): String {
    val monthNames = listOf(
        stringResource(R.string.january_abbreviated),
        stringResource(R.string.february_abbreviated),
        stringResource(R.string.march_abbreviated),
        stringResource(R.string.april_abbreviated),
        stringResource(R.string.may_abbreviated),
        stringResource(R.string.june_abbreviated),
        stringResource(R.string.july_abbreviated),
        stringResource(R.string.august_abbreviated),
        stringResource(R.string.september_abbreviated),
        stringResource(R.string.october_abbreviated),
        stringResource(R.string.november_abbreviated),
        stringResource(R.string.december_abbreviated)
    )
    return monthNames.getOrNull(month - 1) ?: month.toString()
}

private fun getMaxDayOfMonth(year: Int, month: Int): Int {
    val calendar = Calendar.getInstance()
    calendar.set(year, month - 1, 1)
    return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
}
