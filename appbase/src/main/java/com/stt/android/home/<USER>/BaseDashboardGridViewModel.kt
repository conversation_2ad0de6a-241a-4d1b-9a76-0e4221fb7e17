package com.stt.android.home.dashboard

import android.content.SharedPreferences
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import androidx.core.content.edit
import com.gojuno.koptional.None
import com.gojuno.koptional.Optional
import com.gojuno.koptional.Some
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboard.widget.LoadedWidgetData
import com.stt.android.home.dashboard.widget.LoadedWidgetData.WidgetShowType
import com.stt.android.home.dashboard.widget.WidgetDataFetcher
import com.stt.android.home.dashboard.widget.WidgetType
import com.stt.android.home.dashboard.widget.customization.CheckPremiumSubscriptionForWidgetTypeUseCase
import com.stt.android.home.dashboard.widget.customization.CheckWidgetPlacementAllowedUseCase
import com.stt.android.home.dashboard.widget.customization.SelectedDashboardWidgetsRepository
import com.stt.android.home.dashboardnew.customization.guidance.DashboardCustomizationGuidanceTrigger
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleUpdateUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.ui.map.selection.MyTracksGranularity.Companion.SUPPORTED_WIDGET_GRANULARITY_TYPES
import com.stt.android.ui.map.selection.toDiaryCalendarListContainerGranularity
import com.stt.android.ui.map.selection.toGetWorkoutStatisticsWithSummaryUseCaseParams
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.CalendarUtils.buildDayOfWeekLabels
import com.stt.android.utils.STTConstants.Dashboard.WIDGETS_PER_PAGE
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import com.stt.android.utils.STTConstants.MapPreferences.KEY_ANIMATE_MY_TRACKS_OVERRIDE
import com.stt.android.utils.iterator
import com.stt.android.utils.traceSuspend
import io.reactivex.Scheduler
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDate
import java.time.YearMonth

abstract class BaseDashboardGridViewModel(
    private val currentUserController: CurrentUserController,
    private val getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase,
    private val diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder,
    private val mapSelectionModel: MapSelectionModel,
    private val dashboardAnalytics: DashboardAnalytics,
    private val locationSource: SuuntoLocationSource,
    private val exploreMapPreferences: SharedPreferences,
    private val widgetDataFetcher: WidgetDataFetcher,
    private val calendarProvider: CalendarProvider,
    private val selectedDashboardWidgetsRepository: SelectedDashboardWidgetsRepository,
    private val checkWidgetPlacementAllowedUseCase: CheckWidgetPlacementAllowedUseCase,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val checkPremiumSubscriptionForWidgetTypeUseCase: CheckPremiumSubscriptionForWidgetTypeUseCase,
    private val customizationGuidanceTrigger: DashboardCustomizationGuidanceTrigger,
    private val observableMenstrualCycleUpdateUseCase: ObservableMenstrualCycleUpdateUseCase,
    private val sharedPreferences: SharedPreferences,
    ioThread: Scheduler,
    mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : LoadingStateViewModel<DashboardGridContainer>(ioThread, mainThread, coroutinesDispatchers) {

    val trainingClickedEvent = SingleLiveEvent<Any>()
    val progressClickedEvent = SingleLiveEvent<Any>()
    val resourcesClickedEvent = SingleLiveEvent<Any>()
    val sleepClickedEvent = SingleLiveEvent<Any>()
    val sleepHrvClickedEvent = SingleLiveEvent<Any>()
    val stepsClickedEvent = SingleLiveEvent<Any>()
    val caloriesClickedEvent = SingleLiveEvent<Any>()
    val goalClickedEvent = SingleLiveEvent<Any>()
    val commuteClickedEvent = SingleLiveEvent<Any>()
    val ascentClickedEvent = SingleLiveEvent<Any>()
    val minimumHeartRateClickedEvent = SingleLiveEvent<Any>()

    val totalDurationClickedEvent = SingleLiveEvent<DiaryCalendarListContainer.Granularity>()
    val mapClickedEvent = SingleLiveEvent<Any>()
    val calendarClickedEvent = SingleLiveEvent<DiaryCalendarListContainer.Granularity>()
    val chartClickedEvent = SingleLiveEvent<DiaryCalendarListContainer.Granularity>()

    val showCustomizationGuidanceEvent = SingleLiveEvent<Boolean>()

    /**
     * event value is analytics source
     */
    val openPremiumFeaturePromotionEvent = SingleLiveEvent<String>()

    /**
     * The pair's first is the page where the new widget should be placed, and second
     * is the position in that page
     */
    val openNewWidgetSelectionEvent = SingleLiveEvent<Pair<Int, Int>>()

    val onPageSelectedEvent = SingleLiveEvent<Int>()
    private val gridContainerFlow = MutableStateFlow<Optional<DashboardGridContainer>>(None)
    private val widgetDataFlow = MutableStateFlow<Optional<ShownWidgetData>>(None)
    private val dashboardCustomizationEnabledFlow = MutableStateFlow(false)
    private var widgetDataUpdateJob: Job? = null

    private val sharedPreferenceChangeListener = OnSharedPreferenceChangeListener { _, key ->
        if (key == KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS) {
            loadData()
        }
    }

    init {
        initDataLoadCombining()
        sharedPreferences.registerOnSharedPreferenceChangeListener(sharedPreferenceChangeListener)
        launch { observableMenstrualCycleUpdateUseCase().collectLatest { loadData() } }
    }

    private fun initDataLoadCombining() {
        launch {
            combine(
                gridContainerFlow,
                widgetDataFlow,
                dashboardCustomizationEnabledFlow,
                isSubscribedToPremiumUseCase.invoke() // Not used directly, but triggers rechecking types blocked by premium requirement
            ) { gridContainer, widgetData, customizationEnabled, _ ->
                if (gridContainer is Some && widgetData is Some) {
                    val (widgetDataWithCustomization, widgetRemovalEnabled) = if (customizationEnabled) {
                        val widgetDataWithAddNews = widgetData.value.copy(
                            widgetsPerPage = addAddNewWidgetsIfNeeded(widgetData.value.widgetsPerPage)
                        )
                        val removalEnabled = checkWidgetPlacementAllowedUseCase.isRemovalAllowed(
                            widgetData.value.widgetsPerPage
                        )
                        widgetDataWithAddNews to removalEnabled
                    } else {
                        widgetData.value to false
                    }

                    Some(
                        gridContainer.value.copy(
                            widgetData = widgetDataWithCustomization.copy(
                                typesBlockedByPremiumRequirement = checkPremiumSubscriptionForWidgetTypeUseCase.getWidgetTypesBlockedByPremiumRequirement()
                            ),
                            widgetCustomizationModeEnabled = customizationEnabled,
                            widgetRemovalEnabled = widgetRemovalEnabled,
                            onWidgetLongClicked = gridContainer.value.onWidgetLongClicked.takeIf { !customizationEnabled }
                        )
                    )
                } else {
                    None
                }
            }.collect {
                if (it is Some) {
                    notifyDataLoaded(it.value)
                    checkCustomizationGuidanceTrigger()
                }
            }
        }
    }

    fun loadData() {
        launch(io) {
            traceSuspend("loadDashboardGridData") {
                runSuspendCatching {
                    val username = currentUserController.username
                    val showPredictions = sharedPreferences.getBoolean(
                        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
                        KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
                    )
                    val paramsForAllPeriods = SUPPORTED_WIDGET_GRANULARITY_TYPES.associateWith {
                        it.toGetWorkoutStatisticsWithSummaryUseCaseParams(username, showPredictions, calendarProvider)
                    }

                    val summaries = getWorkoutStatisticsWithSummaryUseCase
                        .runWithMultipleTimePeriods(paramsForAllPeriods.values.toList())

                    val dataForAllGranularities = paramsForAllPeriods.keys.zip(summaries).toMap()

                    val dashboardChartsPerGranularity =
                        dataForAllGranularities.mapValues { (_, value) ->
                            createDashboardChartContainer(value)
                        }

                    val weekDayLabels = buildDayOfWeekLabels(
                        LocalDate.now(),
                        calendarProvider.getDayOfWeekField()
                    )

                    val calendarDataPerGranularity = paramsForAllPeriods.mapNotNull {
                        val dataForGranularity = dataForAllGranularities[it.key]
                            ?: return@mapNotNull null
                        Triple(it.key, it.value, dataForGranularity)
                    }.associate { (granularity, params, data) ->
                        granularity to createDiaryBubbleData(
                            data = data,
                            startDate = params.firstDay,
                            endDate = params.endDay,
                            weekDayLabels = weekDayLabels
                        )
                    }

                    val mapDataPerGranularity = dataForAllGranularities.mapValues {
                        DashboardMapViewData(it.value.locations)
                    }

                    gridContainerFlow.emit(
                        Some(
                            DashboardGridContainer(
                                dailyWorkoutsPerGranularity = dataForAllGranularities,
                                calendarEntriesPerGranularity = calendarDataPerGranularity,
                                dashboardChartsPerGranularity = dashboardChartsPerGranularity,
                                suuntoLocationSource = locationSource,
                                mapDataPerGranularity = mapDataPerGranularity,
                                onItemClicked = ::handleItemClick,
                                onMapClicked = ::handleMapClick,
                                onDurationClicked = ::handleDurationClick,
                                onCalendarClicked = ::handleCalendarClick,
                                onChartClicked = ::handleChartClick,
                                onWidgetLongClicked = ::handleWidgetLongClick,
                                removeWidgetClicked = ::removeWidget,
                                onChangeWidgetClicked = ::openChangeWidgetDialog,
                                onBuyPremiumWidgetClicked = ::handleBuyPremiumWidgetClick,
                                widgetData = ShownWidgetData(
                                    widgetsPerPage = emptyList(),
                                    typesBlockedByPremiumRequirement = emptySet(),
                                    today = LocalDate.now()
                                ),
                                widgetCustomizationModeEnabled = false,
                                widgetRemovalEnabled = false,
                                isHrvSupported = isHrvSupported()
                            )
                        )
                    )
                }.onFailure { e ->
                    Timber.w(e, "Failed to get diary data")
                }
            }
        }
    }

    abstract suspend fun isHrvSupported(): Boolean

    fun setWidgetType(page: Int, indexInPage: Int, widgetType: WidgetType) {
        val currentData = viewState.value?.data ?: return
        launch {
            if (!checkPremiumSubscriptionForWidgetTypeUseCase.hasValidSubscription(widgetType)) {
                openPremiumFeaturePromotionEvent.postValue(PremiumPurchaseFlowScreenSource.DASHBOARD_WIDGET_LIST)
                return@launch
            }

            val widgetsPerPage = removeAddNewWidgetsAndMakeMutable(
                currentData.widgetData.widgetsPerPage
            )

            if (!checkWidgetPlacementAllowedUseCase.isPlacementAllowed(
                    widgetsPerPage,
                    widgetType,
                    page,
                    indexInPage
                )
            ) {
                return@launch
            }

            val isReplace = page in widgetsPerPage.indices &&
                indexInPage in widgetsPerPage[page].indices

            val pageToModify = widgetsPerPage.getOrElse(page) {
                val newPage = mutableListOf<WidgetType>()
                widgetsPerPage.add(newPage)
                newPage
            }

            if (indexInPage in pageToModify.indices) {
                pageToModify[indexInPage] = widgetType
            } else {
                pageToModify.add(widgetType)
            }

            saveWidgets(widgetsPerPage)

            val totalWidgetCount = widgetsPerPage.flatten().size
            if (isReplace) {
                dashboardAnalytics.trackReplaceWidget(widgetType, totalWidgetCount, page)
            } else {
                dashboardAnalytics.trackAddWidget(widgetType, totalWidgetCount, page)
            }
        }
    }

    fun removeWidget(page: Int, indexInPage: Int) {
        val currentData = viewState.value?.data ?: return
        val widgetsPerPage = removeAddNewWidgetsAndMakeMutable(
            currentData.widgetData.widgetsPerPage
        )

        if (!checkWidgetPlacementAllowedUseCase.isRemovalAllowed(widgetsPerPage, page, indexInPage)) return

        val removedWidget = widgetsPerPage[page].removeAt(indexInPage)
        if (widgetsPerPage[page].isEmpty()) {
            widgetsPerPage.removeAt(page)
        }

        saveWidgets(widgetsPerPage)

        dashboardAnalytics.trackRemoveWidget(removedWidget, widgetsPerPage.flatten().size, page)
    }

    private fun saveWidgets(widgetsPerPage: List<List<WidgetType>>) {
        launch {
            selectedDashboardWidgetsRepository.saveSelectedDashboardWidgets(widgetsPerPage)
        }
        customizationGuidanceTrigger.disableTrigger()
    }

    private fun handleWidgetLongClick(
        page: Int,
        indexInPage: Int
    ) {
        openDashboardCustomizationMode()
    }

    fun openDashboardCustomizationMode() {
        showCustomizationGuidanceEvent.value = false
        dashboardCustomizationEnabledFlow.tryEmit(true)

        val currentData = viewState.value?.data ?: return
        val currentWidgets = currentData.widgetData.widgetsPerPage.flatten()
        dashboardAnalytics.trackOpenDashboardWidgetCustomizationMode(currentWidgets)
        selectedDashboardWidgetsRepository.disallowAutoUpdatingWidgets()
    }

    fun closeDashboardCustomizationMode() {
        dashboardCustomizationEnabledFlow.tryEmit(false)
    }

    fun stopAutoUpdatingWidgets() {
        selectedDashboardWidgetsRepository.disallowAutoUpdatingWidgets()
    }

    fun setPageSelected(position: Int) {
        onPageSelectedEvent.postValue(position)
    }

    /**
     * If adding or replacing widgets is allowed,
     * adds the customization mode's temporary 'add new widget' widget to all incomplete pages
     * and adds a new page with just the 'add new widget'.
     */
    private fun addAddNewWidgetsIfNeeded(widgetsPerPage: List<List<WidgetType>>): List<List<WidgetType>> {
        if (!checkWidgetPlacementAllowedUseCase.isAddingAnyWidgetTypeAllowed(widgetsPerPage)) {
            return widgetsPerPage
        }

        return widgetsPerPage.map { page ->
            if (page.size < WIDGETS_PER_PAGE) {
                page + WidgetType.ADD_NEW_WIDGET
            } else {
                page
            }
        } + listOf(listOf(WidgetType.ADD_NEW_WIDGET))
    }

    private fun removeAddNewWidgetsAndMakeMutable(
        widgetsPerPage: List<List<WidgetType>>
    ): MutableList<MutableList<WidgetType>> = widgetsPerPage
        .filterNot { it == listOf(WidgetType.ADD_NEW_WIDGET) }
        .map { page ->
            page
                .filterNot { it == WidgetType.ADD_NEW_WIDGET }
                .toMutableList()
        }
        .toMutableList()

    private fun openChangeWidgetDialog(page: Int, indexInPage: Int) {
        openNewWidgetSelectionEvent.postValue(page to indexInPage)
    }

    private fun handleBuyPremiumWidgetClick(originalType: WidgetType) {
        val analyticsSource =
            PremiumPurchaseFlowScreenSource.DASHBOARD_WIDGET_TYPE_PREFIX +
                (originalType.analyticsName ?: AnalyticsPropertyValue.UNKNOWN)
        openPremiumFeaturePromotionEvent.postValue(analyticsSource)
    }

    private fun createDashboardChartContainer(data: DailyWorkoutStatisticsWithSummary): DashboardChartContainer {
        val activityStatsWithTotals =
            diaryCalendarListContainerBuilder.getActivityStatsWithTotals(data.totalValuesByActivityType)

        // Summary by activity type
        val maxDurationForSingleActivity =
            diaryCalendarListContainerBuilder.getMaxDurationForSingleActivityClippedAtTop3(
                activityStatsWithTotals
            )

        return DashboardChartContainer(
            maxDuration = maxDurationForSingleActivity,
            activityStatsWithTotals = activityStatsWithTotals
        )
    }

    private fun createDiaryBubbleData(
        data: DailyWorkoutStatisticsWithSummary,
        startDate: LocalDate,
        endDate: LocalDate,
        weekDayLabels: List<String>
    ) = DiaryBubbleData(
        startDate = startDate,
        endDate = endDate,
        bubbles = createCalendarData(data, startDate, endDate),
        weekDayLabels = weekDayLabels,
        onMonthClicked = ::handleMonthClick
    )

    private fun createCalendarData(
        data: DailyWorkoutStatisticsWithSummary,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<DiaryBubbleContainer> {
        val bubbleList = mutableListOf<DiaryBubbleContainer>()
        for (day in startDate..endDate) {
            val diaryCalendarDailyData = data.dailyData[day] ?: DiaryCalendarDailyData.EMPTY
            bubbleList.add(
                DiaryBubbleContainer(
                    id = day.toString(),
                    startDate = day,
                    bubbleType = diaryCalendarListContainerBuilder.getBubbleType(
                        day,
                        diaryCalendarDailyData
                    ),
                    dayData = diaryCalendarDailyData,
                    onDateClicked = ::handleCellClick
                )
            )
        }
        return bubbleList
    }

    @Suppress("UNUSED_PARAMETER")
    private fun handleItemClick(item: Int) {
        // handle item click
    }

    @Suppress("UNUSED_PARAMETER")
    private fun handleCellClick(item: LocalDate) {
        // Not implemented
    }

    private fun handleCalendarClick(
        granularityType: MyTracksGranularity.Type,
        widgetType: WidgetType
    ) {
        calendarClickedEvent.value = granularityType.toDiaryCalendarListContainerGranularity()
        onWidgetClick(widgetType)
    }

    private fun handleChartClick(
        granularityType: MyTracksGranularity.Type,
        widgetType: WidgetType
    ) {
        chartClickedEvent.value = granularityType.toDiaryCalendarListContainerGranularity()
        onWidgetClick(widgetType)
    }

    private fun handleMapClick(granularityType: MyTracksGranularity.Type, widgetType: WidgetType) {
        val granularity = if (granularityType in SUPPORTED_WIDGET_GRANULARITY_TYPES) {
            MyTracksGranularity(granularityType)
        } else {
            throw IllegalArgumentException("Map widget click with unsupported granularity type $granularityType")
        }
        mapSelectionModel.selectedMyTracksGranularity = granularity
        mapSelectionModel.selectedHeatmap = null
        // Set override pref to make ExploreMapFragment to animate my tracks.
        exploreMapPreferences.edit {
            putBoolean(KEY_ANIMATE_MY_TRACKS_OVERRIDE, true)
        }
        mapClickedEvent.call()
        onWidgetClick(widgetType)
    }

    private fun handleDurationClick(
        granularityType: MyTracksGranularity.Type,
        widgetType: WidgetType
    ) {
        totalDurationClickedEvent.value = granularityType.toDiaryCalendarListContainerGranularity()
        onWidgetClick(widgetType)
    }

    @Suppress("UNUSED_PARAMETER")
    private fun handleMonthClick(yearMonth: YearMonth) {
        // Not implemented
    }

    private fun onWidgetClick(widgetType: WidgetType) {
        showCustomizationGuidanceEvent.value = false
        dashboardAnalytics.sendHomeScreenDashboardNavigationEvent(widgetType)
        selectedDashboardWidgetsRepository.disallowAutoUpdatingWidgets()
    }

    fun trackHomeScreenAnalytics(
        zeroBasedInitialDashboardPage: Int,
        deviceCategory: DashboardAnalytics.DeviceCategory
    ) {
        launch(main) {
            widgetDataFlow
                .mapNotNull { (it as? Some)?.value }
                .combine(useNoWatchPairedInWidgetAnalytics()) { data, useNoWatch -> data to useNoWatch }
                .take(1)
                .collect { (data, useNoWatchPaired) ->
                    // Expects that updateDashboardStatusToAnalytics has been called before
                    // widgetDataFlow emits, which always happens as it gets called
                    // as part of transforming data from widgetDataFetcher to widgetDataFlow

                    // watch pairing state is updated also here as when dashboard is re-entered
                    // the status might've changed and not be updated by widget refresh yet
                    dashboardAnalytics.updateUseNoWatchPairedStatus(useNoWatchPaired)

                    val progressData = data.progressWidgetData
                    dashboardAnalytics.trackHomeScreenAnalytics(
                        zeroBasedDashboardPageEntered = zeroBasedInitialDashboardPage,
                        currentFitnessValue = progressData?.fitnessValue,
                        currentFatigueValue = progressData?.fatigueValue,
                        currentFormValue = progressData?.formValue,
                        deviceCategory = deviceCategory,
                        currentWidgets = data.widgetsPerPage.flatten()
                    )
                }
        }
    }

    private fun updateDashboardStatusToAnalytics(
        loadedData: LoadedWidgetData,
        numPages: Int,
        useNoWatchPaired: Boolean
    ) {
        dashboardAnalytics.updateUseNoWatchPairedStatus(useNoWatchPaired)
        with(loadedData) {
            dashboardAnalytics.updateDashboardStatus(
                DashboardStatus(
                    numberOfPages = numPages,
                    caloriesShowType = caloriesWidgetShowType,
                    stepsShowType = stepsWidgetShowType,
                    sleepShowType = sleepWidgetShowType,
                    resourcesShowType = resourcesWidgetShowType,
                    trainingShowType = trainingWidgetShowType,
                    progressShowType = progressWidgetShowType
                )
            )
        }
    }

    override fun retryLoading() {}

    override fun onViewStateActive() {
        customizationGuidanceTrigger.checkConditionsAndCountdownTriggerIfNeeded()

        if (viewState.value?.isLoaded() == true) {
            checkCustomizationGuidanceTrigger()
        }

        startObservingWidgetData()
    }

    override fun onViewStateInactive() {
        stopObservingWidgetData()
    }

    override fun onCleared() {
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(sharedPreferenceChangeListener)
        super.onCleared()
        stopObservingWidgetData()
    }

    private fun startObservingWidgetData() {
        widgetDataUpdateJob = launch(io) {
            combine(
                widgetDataFetcher.widgetDataFlow(),
                selectedDashboardWidgetsRepository.getSelectedDashboardWidgets(),
                useNoWatchPairedInWidgetAnalytics(),
                ::Triple
            ).collect { (loadedData, widgetsPerPage, useNoWatchPaired) ->
                val numPages = widgetsPerPage.size
                updateDashboardStatusToAnalytics(
                    loadedData,
                    numPages,
                    useNoWatchPaired
                )

                widgetDataFlow.emit(
                    Some(
                        ShownWidgetData(
                            widgetsPerPage = widgetsPerPage,
                            typesBlockedByPremiumRequirement = emptySet(),
                            today = LocalDate.now(),
                            trainingWidgetData = loadedData.trainingWidgetData,
                            onTrainingWidgetClicked = ::handleTrainingWidgetClick.takeIf { loadedData.trainingWidgetShowType == WidgetShowType.NORMAL },
                            progressWidgetData = loadedData.progressWidgetData,
                            onProgressWidgetClicked = ::handleProgressWidgetClick.takeIf { loadedData.progressWidgetShowType == WidgetShowType.NORMAL },
                            resourcesWidgetData = loadedData.resourcesWidgetData,
                            onResourcesWidgetClicked = ::handleResourcesWidgetClick.takeIf { loadedData.resourcesWidgetShowType == WidgetShowType.NORMAL },
                            sleepWidgetData = loadedData.sleepWidgetData,
                            onSleepWidgetClicked = ::handleSleepWidgetClick.takeIf { loadedData.sleepWidgetShowType == WidgetShowType.NORMAL },
                            sleepHrvWidgetData = loadedData.sleepHrvWidgetData,
                            onSleepHrvWidgetClicked = ::handleSleepHrvWidgetClick.takeIf { loadedData.sleepHrvWidgetShowType == WidgetShowType.NORMAL },
                            stepsWidgetData = loadedData.stepsWidgetData,
                            onStepsWidgetClicked = ::handleStepsWidgetClick.takeIf { loadedData.stepsWidgetShowType == WidgetShowType.NORMAL },
                            caloriesWidgetData = loadedData.caloriesWidgetData,
                            onCaloriesWidgetClicked = ::handleCaloriesWidgetClick.takeIf { loadedData.caloriesWidgetShowType == WidgetShowType.NORMAL },
                            goalWidgetData = loadedData.goalWidgetData,
                            onGoalWidgetClicked = ::handleGoalWidgetClick.takeIf { loadedData.goalWidgetShowType != WidgetShowType.NOT_CLICKABLE },
                            commuteWidgetData = loadedData.commuteWidgetData,
                            onCommuteWidgetClicked = ::handleCommuteWidgetClick.takeIf { loadedData.commuteWidgetShowType != WidgetShowType.NOT_CLICKABLE },
                            ascentWidgetData = loadedData.ascentWidgetData,
                            onAscentWidgetClicked = ::handleAscentWidgetClick.takeIf { loadedData.ascentWidgetShowType == WidgetShowType.NORMAL },
                            minimumHeartRateWidgetData = loadedData.minimumHeartRateWidgetData,
                            onMinimumHeartRateWidgetClicked = ::handleMinimumHeartRateWidgetClick.takeIf { loadedData.minimumHeartRateWidgetShowType == WidgetShowType.NORMAL }
                        )
                    )
                )
            }
        }
    }

    private fun stopObservingWidgetData() {
        widgetDataUpdateJob?.cancel()
        widgetDataUpdateJob = null
    }

    private fun handleTrainingWidgetClick() {
        onWidgetClick(WidgetType.TRAINING)
        trainingClickedEvent.call()
    }

    private fun handleProgressWidgetClick() {
        onWidgetClick(WidgetType.PROGRESS)
        progressClickedEvent.call()
    }

    private fun handleResourcesWidgetClick() {
        onWidgetClick(WidgetType.RESOURCES)
        resourcesClickedEvent.call()
    }

    private fun handleSleepWidgetClick() {
        onWidgetClick(WidgetType.SLEEP)
        sleepClickedEvent.call()
    }

    private fun handleSleepHrvWidgetClick() {
        onWidgetClick(WidgetType.SLEEP_HRV)
        sleepHrvClickedEvent.call()
    }

    private fun handleStepsWidgetClick() {
        onWidgetClick(WidgetType.STEPS)
        stepsClickedEvent.call()
    }

    private fun handleCaloriesWidgetClick() {
        onWidgetClick(WidgetType.CALORIES)
        caloriesClickedEvent.call()
    }

    private fun handleGoalWidgetClick() {
        goalClickedEvent.call()
    }

    private fun handleCommuteWidgetClick() {
        onWidgetClick(WidgetType.COMMUTE_THIS_MONTH)
        commuteClickedEvent.call()
    }

    private fun handleAscentWidgetClick() {
        onWidgetClick(WidgetType.ASCENT)
        ascentClickedEvent.call()
    }

    private fun handleMinimumHeartRateWidgetClick() {
        onWidgetClick(WidgetType.MINIMUM_HEART_RATE)
        minimumHeartRateClickedEvent.call()
    }

    private fun checkCustomizationGuidanceTrigger() {
        launch {
            delay(CUSTOMIZATION_GUIDANCE_DELAY)
            if (customizationGuidanceTrigger.shouldShowCustomizationGuidance()) {
                showCustomizationGuidanceEvent.value = true
            }
        }
    }

    // Default to false so ST won't try to handle Suunto's special analytics cases
    protected open fun useNoWatchPairedInWidgetAnalytics(): Flow<Boolean> = flowOf(false)

    private fun MyTracksGranularity.Type.nextGranularityType(): MyTracksGranularity.Type =
        when (this) {
            MyTracksGranularity.Type.THIS_WEEK -> MyTracksGranularity.Type.THIS_MONTH
            MyTracksGranularity.Type.LAST_30_DAYS -> MyTracksGranularity.Type.THIS_WEEK
            MyTracksGranularity.Type.THIS_MONTH -> MyTracksGranularity.Type.LAST_30_DAYS
            else -> throw IllegalArgumentException("Can't get next granularity type for $this")
        }

    companion object {
        // Time we wait for the widgets to appear before showing guidance
        private const val CUSTOMIZATION_GUIDANCE_DELAY = 300L
    }
}
