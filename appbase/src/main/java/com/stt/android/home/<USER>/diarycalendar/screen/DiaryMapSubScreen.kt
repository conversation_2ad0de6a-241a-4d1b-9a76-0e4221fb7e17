package com.stt.android.home.diary.diarycalendar.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.platform.LocalWindowInfo
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarEmptyState
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarMap
import com.stt.android.home.diary.diarycalendar.components.DiaryMapActivityFilterTags
import com.stt.android.home.diary.diarycalendar.components.DiaryMapActivitySummary
import com.stt.android.home.diary.diarycalendar.components.DiaryMapNavigation
import com.stt.android.home.diary.diarycalendar.screen.ext.dateRange
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.models.MapSelectionModel

@Composable
internal fun DiaryMapSubScreen(
    data: DiaryCalendarListContainer,
    mapSelectionModel: MapSelectionModel,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    myTracksUtils: MyTracksUtils,
    previousPageEnabled: Boolean,
    nextPageEnabled: Boolean,
    onPreviousPage: () -> Unit,
    onNextPage: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val windowInfo = LocalWindowInfo.current

    val (startDate, endDate) = data.dateRange

    // avoid multiple mapviews issue
    var visible by remember { mutableStateOf(false) }

    var selectedActivityType by remember { mutableStateOf<ActivityType?>(null) }

    Column(
        modifier = modifier
            .onLayoutRectChanged(debounceMillis = 0L) {
                val startX = it.positionInWindow.x
                val endX = it.positionInWindow.x + it.width
                visible = (0..windowInfo.containerSize.width).intersect(startX..endX).isNotEmpty()
            },
    ) {
        DiaryMapNavigation(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small),
            granularity = data.granularity,
            startDate = startDate,
            endDate = endDate,
            previousPageEnabled = previousPageEnabled,
            nextPageEnabled = nextPageEnabled,
            onPreviousPage = onPreviousPage,
            onNextPage = onNextPage,
        )
        DiaryMapActivitySummary(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium),
            activityCount = data.locations.size,
        )
        val showEmptyView = data.locations.isEmpty() && data.loadingComplete
        AnimatedVisibility(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            visible = visible && !showEmptyView,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Box(modifier = Modifier.fillMaxSize()) {
                val locations = if (selectedActivityType != null) {
                    data.locations.filter { it.activityType == selectedActivityType?.id }
                } else data.locations
                val routes = if (selectedActivityType != null) {
                    data.routes.filter { it.activityType == selectedActivityType?.id }
                } else data.routes
                val bounds = if (selectedActivityType != null) {
                    data.activityBounds[selectedActivityType?.id]
                } else data.bounds
                DiaryCalendarMap(
                    modifier = Modifier.fillMaxSize(),
                    roundedCorners = false,
                    gestureEnabled = true,
                    locations = locations,
                    routes = routes,
                    bounds = bounds,
                    granularity = data.granularity,
                    mapSelectionModel = mapSelectionModel,
                    bubbleData = data.bubbleData,
                    myTracksUtils = myTracksUtils,
                    tracker = amplitudeAnalyticsTracker,
                )
                DiaryMapActivityFilterTags(
                    modifier = Modifier.fillMaxWidth(),
                    data = data,
                    selectedActivityType = selectedActivityType,
                    onActivityTypeSelected = {
                        selectedActivityType = it
                    },
                )
            }
        }
        if (showEmptyView) {
            DiaryCalendarEmptyState(modifier = Modifier.fillMaxWidth())
        }
    }
}
