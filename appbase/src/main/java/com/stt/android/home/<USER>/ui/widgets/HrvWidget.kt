package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWeeklyLineChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.FeatureNotSupportedWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.HrvWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period

@Composable
internal fun HrvWidget(
    widgetInfo: HrvWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    if (!widgetInfo.supported) {
        FeatureNotSupportedWidget(
            editMode = editMode,
            headerRes = R.string.sleep_hrv_title,
            subheaderText = stringResource(R.string.today),
            colorRes = R.color.dashboard_widget_hrv,
            iconRes = R.drawable.hrv_fill,
            onRemoveClick = onRemoveClick,
            modifier = modifier,
        )
    } else {
        CommonWeeklyLineChartWidget(
            editMode = editMode,
            headerRes = R.string.sleep_hrv_title,
            subheaderText = stringResource(R.string.today),
            colorRes = R.color.dashboard_widget_hrv,
            iconRes = R.drawable.hrv_fill,
            titleText = widgetInfo.title,
            subtitleText = widgetInfo.subtitle,
            subtitleIconRes = widgetInfo.subtitleIconRes,
            period = widgetInfo.period,
            progresses = widgetInfo.progresses,
            onClick = onClick,
            onLongClick = onLongClick,
            onRemoveClick = onRemoveClick,
            modifier = modifier,
        )
    }
}

@Preview
@Composable
private fun HrvWidgetPreview(
    @PreviewParameter(HrvWidgetInfoProvider::class) widgetInfo: HrvWidgetInfo
) {
    M3AppTheme {
        HrvWidget(
            widgetInfo = widgetInfo,
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {},
            modifier = Modifier.size(170.dp),
        )
    }
}

private class HrvWidgetInfoProvider : PreviewParameterProvider<HrvWidgetInfo> {
    override val values: Sequence<HrvWidgetInfo> = sequenceOf(
        HrvWidgetInfo(
            supported = true,
            period = Period.Last7Days,
            progresses = listOf(0.1f, 0.2f, 0.3f, 0.0f, -1f, 1.0f, 0.2f),
            title = generateWidgetTitle("56","ms"),
            subtitle = "Normal",
            subtitleIconRes = R.drawable.widget_normal
        ),
        HrvWidgetInfo(
            supported = true,
            period = Period.Last7Days,
            progresses = (0..6).map { -1f },
            title = AnnotatedString("--"),
            subtitle = "No data"
        ),
        HrvWidgetInfo(
            supported = false,
            period = Period.Last7Days,
            progresses = emptyList(),
            title = AnnotatedString(""),
            subtitle = ""
        )
    )
}
