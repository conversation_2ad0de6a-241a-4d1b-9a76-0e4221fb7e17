package com.stt.android.home.settings;

import android.content.Context;
import android.util.AttributeSet;
import com.stt.android.R;

public abstract class BaseGenderPreference extends TitleListPreference {

    public BaseGenderPreference(Context context) {
        this(context, null);
    }

    public BaseGenderPreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.preferenceStyle);
    }

    public BaseGenderPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, defStyleAttr);
    }

    public BaseGenderPreference(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }
}
