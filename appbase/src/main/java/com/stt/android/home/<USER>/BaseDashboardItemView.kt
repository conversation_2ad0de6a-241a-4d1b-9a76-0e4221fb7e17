package com.stt.android.home.dashboard

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner

open class BaseDashboardItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver {
    private var lifecycle: Lifecycle? = null

    fun setup(lifecycle: Lifecycle): <PERSON><PERSON>an {
        if (this.lifecycle == null) {
            this.lifecycle = lifecycle
            this.lifecycle?.addObserver(this)
            return true
        }
        return false
    }

    open fun onBind() {
    }

    open fun onUnbind() {
    }

    override fun onDestroy(owner: LifecycleOwner) {
        lifecycle?.removeObserver(this)
    }
}
