package com.stt.android.home.dashboardnew.customization

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.home.dashboard.widget.WidgetType

data class DashboardWidgetInfo(
    val widgetType: WidgetType,
    @StringRes val nameRes: Int,
    @StringRes val descriptionRes: Int,
    @DrawableRes val previewImageRes: Int,
    val availableWithAllDevices: <PERSON><PERSON><PERSON>,
    val showPremiumRequired: <PERSON><PERSON>an
)
