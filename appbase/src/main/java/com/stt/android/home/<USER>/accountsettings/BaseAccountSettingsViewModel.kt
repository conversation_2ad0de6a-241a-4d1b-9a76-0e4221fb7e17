package com.stt.android.home.settings.accountsettings

import androidx.core.util.PatternsCompat
import androidx.lifecycle.LiveData
import com.stt.android.FeatureFlags
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.home.settings.PhoneNumberUtil.maskPhoneNumberWithRegionCode
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.FlavorUtils
import io.reactivex.Scheduler

data class AccountSettingsViewState(
    val email: String?,
    val phone: String?,
    val newEmail: String?,
    val userName: String,
    val isDeleteAccountEnabled: <PERSON>olean,
    val emailFieldEnabled: <PERSON><PERSON>an,
    val phoneFieldEnabled: <PERSON><PERSON>an,
    val exportMyDataEnabled: <PERSON><PERSON>an,
) {
    val emailLabelResId: Int = if (email.isNullOrEmpty()) {
        R.string.tap_to_add_your_email_address
    } else {
        R.string.account_settings_email_address
    }

    val phoneLabelResId: Int = R.string.phone
}

abstract class BaseAccountSettingsViewModel(
    currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    featureFlags: FeatureFlags,
    private val settingsAnalyticsTracker: SettingsAnalyticsTracker,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : LoadingStateViewModel<AccountSettingsViewState>(ioThread, mainThread) {

    init {
        notifyDataLoaded(
            AccountSettingsViewState(
                userSettingsController.settings.email,
                maskPhoneNumberWithRegionCode(userSettingsController.settings.phoneNumber),
                null,
                currentUserController.username,
                featureFlags.isDeleteAccountEnabled,
                true,
                FlavorUtils.isSuuntoAppChina,
                !FlavorUtils.isSuuntoAppChina,
            )
        )
    }

    private val _onResetPasswordClick = SingleLiveEvent<Any>()
    val onResetPasswordClick: LiveData<Any>
        get() = _onResetPasswordClick

    private val _onDeleteAccountClick = SingleLiveEvent<Any>()
    val onDeleteAccountClick: LiveData<Any>
        get() = _onDeleteAccountClick

    private val _onChangeEmailClicked = SingleLiveEvent<String>()
    val onChangeEmailClicked: LiveData<String>
        get() = _onChangeEmailClicked

    private val _onChangePhoneClicked = SingleLiveEvent<String>()
    val onChangePhoneClicked: LiveData<String>
        get() = _onChangePhoneClicked

    private val _onExportMyDataClicked = SingleLiveEvent<Any>()
    val onExportMyDataClicked: LiveData<Any>
        get() = _onExportMyDataClicked

    fun onResetPasswordClicked() {
        _onResetPasswordClick.call()
    }

    fun onDeleteAccountClicked() {
        _onDeleteAccountClick.call()
    }

    fun onChangeEmailClicked() {
        _onChangeEmailClicked.value = userSettingsController.settings.email
        settingsAnalyticsTracker.trackEvent(AnalyticsEvent.CHANGE_EMAIL_SCREEN)
    }

    fun onChangePhoneClicked() {
        _onChangePhoneClicked.value = userSettingsController.settings.phoneNumber
        settingsAnalyticsTracker.trackEvent(AnalyticsEvent.CHANGE_PHONE_SCREEN)
    }

    fun onExportMyDataClicked() {
        _onExportMyDataClicked.call()
    }

    fun changePhoneSuccess() {
        notifyDataLoaded(
            viewState.value?.data?.copy(
                phone = userSettingsController.settings.phoneNumber
            )
        )
        settingsAnalyticsTracker.trackEvent(AnalyticsEvent.CHANGE_PHONE_SAVE)
    }

    fun changeEmailSuccess() {
        notifyDataLoaded(
            viewState.value?.data?.copy(
                email = userSettingsController.settings.email,
                newEmail = null,
                emailFieldEnabled = true
            )
        )
        settingsAnalyticsTracker.trackEvent(AnalyticsEvent.CHANGE_EMAIL_SAVE)
    }

    fun isEmailAddressValid(email: String) = PatternsCompat.EMAIL_ADDRESS.matcher(email).matches()
}
