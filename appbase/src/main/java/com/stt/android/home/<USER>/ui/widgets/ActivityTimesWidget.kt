package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect.Companion.dashPathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.ui.widgets.common.weekdayLabel
import com.stt.android.home.dashboardv2.widgets.ActivityTimesWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import java.time.DayOfWeek
import java.time.LocalDate

@Composable
internal fun ActivityTimesWidget(
    widgetInfo: ActivityTimesWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    CommonChartWidget(
        editMode = editMode,
        headerRes = R.string.frequency,
        subheaderText = stringResource(R.string.this_week),
        colorRes = widgetInfo.representativeActivityType.colorId,
        iconRes = widgetInfo.representativeActivityType.iconId,
        titleText = widgetInfo.title,
        subtitleText = widgetInfo.subtitle,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
        iconCircleBg = true,
        subtitleIconRes = widgetInfo.subtitleIconRes,
    ) {
        WeeklyActivityTimes(
            color = colorResource(widgetInfo.representativeActivityType.colorId),
            timesList = widgetInfo.timesList,
            endDate = widgetInfo.period.endDate,
        )
    }
}

@Composable
private fun WeeklyActivityTimes(
    color: Color,
    timesList: List<Int>,
    modifier: Modifier = Modifier,
    endDate: LocalDate = LocalDate.now().atStartOfDay().toLocalDate(),
) {
    val gray = MaterialTheme.colorScheme.mediumGrey
    val lightGray = MaterialTheme.colorScheme.lightGrey
    val blankColor = MaterialTheme.colorScheme.surface
    val markMaxHeight = 36.dp
    val markCount = timesList.size
    val strokeWidthFloat = LocalDensity.current.run { 2.dp.toPx() }
    val radiusFloat = LocalDensity.current.run { 3.dp.toPx() }
    val maxCircleSpacingFloat = LocalDensity.current.run { 9.dp.toPx() }
    val dashPathEffect = dashPathEffect(floatArrayOf(strokeWidthFloat, strokeWidthFloat))
    val textMeasurer = rememberTextMeasurer()
    val textMargin = MaterialTheme.spacing.xxxsmall
    val textStyle = MaterialTheme.typography.bodySmall.merge(color = gray)
    val boldTextStyle = MaterialTheme.typography.bodySmallBold
    val density = LocalDensity.current.density

    val selectedDayIndex = remember { mutableIntStateOf(0) }

    val weekdayLabels = remember(endDate) {
        val today = LocalDate.now().atStartOfDay().toLocalDate()
        List(timesList.size) { index ->
            val date = endDate.minusDays((markCount - 1 - index).toLong())
            if (date == today) {
                selectedDayIndex.intValue = index
            }
            date.weekdayLabel
        }
    }

    val maxLabelHeight = remember {
        weekdayLabels.maxOf { weekdayText ->
            val weekdayTextHeight = textMeasurer.measure(weekdayText, textStyle).size.height
            (weekdayTextHeight / density).dp
        }
    }

    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(markMaxHeight + maxLabelHeight + textMargin)
    ) {
        val markMaxHeightFloat = markMaxHeight.toPx()
        val width = this.size.width
        val markWidthFloat = width / markCount

        timesList.forEachIndexed { index, times ->
            val offset = Offset(
                x = markWidthFloat * index + markWidthFloat / 2,
                y = 0f
            )
            // Dash line
            drawLine(
                color = lightGray,
                start = offset,
                end = offset.copy(y = markMaxHeightFloat),
                strokeWidth = 1.dp.toPx(),
                pathEffect = dashPathEffect
            )
            // Circle
            val circleYRange =
                (radiusFloat + strokeWidthFloat)..(markMaxHeightFloat - (radiusFloat + strokeWidthFloat))
            val circleSpacing =
                ((circleYRange.endInclusive - circleYRange.start) / (times - 1).coerceAtLeast(1))
                    .coerceAtMost(maxCircleSpacingFloat)
            for (count in 0 until times) {
                val center = offset.plus(
                    Offset(
                        0f,
                        circleYRange.endInclusive - count * circleSpacing
                    )
                )
                drawCircle(
                    color = blankColor,
                    radius = radiusFloat,
                    center = center,
                )
                drawCircle(
                    color = color,
                    radius = radiusFloat,
                    center = center,
                    style = Stroke(width = strokeWidthFloat)
                )
            }
            // Weekday label
            val weekdayTextStyle = if (index == selectedDayIndex.intValue) {
                boldTextStyle
            } else {
                textStyle
            }
            val weekText = weekdayLabels[index]
            val weekTextWidthFloat = textMeasurer.measure(weekText, weekdayTextStyle).size.width
            drawText(
                textMeasurer = textMeasurer,
                text = weekText,
                style = weekdayTextStyle,
                topLeft = offset.plus(
                    Offset(
                        -weekTextWidthFloat / 2f,
                        markMaxHeightFloat + textMargin.toPx()
                    )
                )
            )
        }
    }
}

@Preview
@Composable
private fun ActivityTimesWidgetPreview(
    @PreviewParameter(ActivityTimesWidgetInfoProvider::class) widgetInfo: ActivityTimesWidgetInfo
) {
    M3AppTheme {
        ActivityTimesWidget(
            widgetInfo = widgetInfo,
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {},
            modifier = Modifier.size(170.dp),
        )
    }
}

private class ActivityTimesWidgetInfoProvider :
    PreviewParameterProvider<ActivityTimesWidgetInfo> {
    override val values: Sequence<ActivityTimesWidgetInfo> = sequenceOf(
        ActivityTimesWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.MONDAY),
            representativeActivityType = ActivityType.SNORKELING,
            timesList = listOf(1, 2, 3, 4, 5, 36, 17),
            title = generateWidgetTitle("34", "times"),
            subtitle = "2 times",
            subtitleIconRes = R.drawable.widget_up_arrow
        ),
        ActivityTimesWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.MONDAY),
            representativeActivityType = ActivityType.SNORKELING,
            timesList = (0..6).map { 0 },
            title = generateWidgetTitle("0", "time"),
            subtitle = "No data"
        ),
    )
}
