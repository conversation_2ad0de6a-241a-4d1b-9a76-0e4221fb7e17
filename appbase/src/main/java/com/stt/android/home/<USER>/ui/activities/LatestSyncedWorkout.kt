package com.stt.android.home.dashboardv2.ui.activities

import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.ui.components.workout.WorkoutShareInfo
import kotlinx.coroutines.launch
import kotlin.math.abs

@Composable
internal fun LatestSyncedWorkout(
    workoutCardInfo: WorkoutCardInfo,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onReactionClick: (WorkoutHeader) -> Unit,
    onShareClick: (WorkoutHeader, WorkoutShareInfo) -> Unit,
    onDismissClick: () -> Unit,
    onShowMoreClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var screenWidth by remember { mutableFloatStateOf(1000f) }
    val offsetX = remember { Animatable(0f) }
    val alpha by remember {
        derivedStateOf { 1.0F - (offsetX.value / screenWidth).coerceIn(0F, 1.0F) }
    }
    val coroutineScope = rememberCoroutineScope()
    val threshold = with(LocalDensity.current) { 100.dp.toPx() }
    var touchedCover by remember { mutableIntStateOf(-1) }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .onGloballyPositioned { layoutCoordinates ->
                screenWidth = layoutCoordinates.size.width.toFloat()
            }
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.medium
                ),
        ) {
            Text(
                text = stringResource(R.string.dashboard_latest_activity),
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier.weight(1f)
            )
            SuuntoIconButton(
                icon = SuuntoIcons.ActionClose,
                onClick = onDismissClick,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }

        Spacer(Modifier.height(MaterialTheme.spacing.small))

        Box(
            modifier = Modifier
                .offset { IntOffset(offsetX.value.toInt(), 0) }
                .alpha(alpha)
                .pointerInput(Unit) {
                    awaitPointerEventScope {
                        while (true) {
                            var accumulatedDragX = 0.0F
                            var isHorizontalDrag = false
                            var isHorizontalDragTowardsRight = false
                            var isLastHorizontalDragTowardRight = false

                            do {
                                val event = awaitPointerEvent()
                                val change = event.changes.firstOrNull() ?: break
                                val dragAmountX = change.positionChange().x
                                val dragAmountY = change.positionChange().y

                                if (!isHorizontalDrag) {
                                    isHorizontalDrag = (touchedCover == 0 || touchedCover == -1) &&
                                        abs(dragAmountX) > abs(dragAmountY)
                                    isHorizontalDragTowardsRight = dragAmountX > 0.0F
                                }
                                if (!isHorizontalDrag) {
                                    continue
                                }

                                if (change.pressed) {
                                    isLastHorizontalDragTowardRight = dragAmountX > 0.0F
                                }

                                if (isHorizontalDragTowardsRight) {
                                    accumulatedDragX += dragAmountX
                                    val newOffsetX =
                                        (offsetX.value + dragAmountX).coerceIn(0f, screenWidth)
                                    coroutineScope.launch { offsetX.snapTo(newOffsetX) }

                                    change.consume()
                                } else {
                                    break
                                }
                            } while (change.pressed)

                            if (isHorizontalDrag) {
                                coroutineScope.launch {
                                    if (isLastHorizontalDragTowardRight && accumulatedDragX > threshold) {
                                        offsetX.animateTo(screenWidth)
                                        onDismissClick()
                                    } else {
                                        offsetX.animateTo(0f)
                                    }
                                }
                            }
                        }
                    }
                }
        ) {
            Workout(
                workoutCard = workoutCardInfo,
                viewEvent = viewEvent,
                onReactionClick = onReactionClick,
                onShareClick = onShareClick,
                onCoverTouchEvent = { index -> touchedCover = index}
            )
        }

        TextButton(
            onClick = onShowMoreClick
        ) {
            Text(
                text = stringResource(R.string.dashboard_show_more),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}
