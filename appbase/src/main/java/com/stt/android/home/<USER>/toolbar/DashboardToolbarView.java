package com.stt.android.home.dashboard.toolbar;

import android.graphics.Bitmap;

import com.stt.android.domain.NotificationState;
import com.stt.android.domain.user.User;
import com.stt.android.views.MVPView;

public interface DashboardToolbarView extends MVPView {
    void displayUserProfileImage(Bitmap bitmap);

    void displayDefaultUserProfileImage();

    void displaySignIn();

    void showProfile(User user);

    void setNotificationsCount(NotificationState notificationState);

    void openFeed(boolean openMarketingInbox);
}
