package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.TSSWidgetInfo
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import com.stt.android.utils.sumByFloat
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject
import kotlin.math.abs

internal class TSSWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val infoModelFormatter: InfoModelFormatter,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<TSSWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<TSSWidgetInfo> =
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(
                workoutHeaderController.currentUserWorkoutUpdated
                    .onStart { emit(Unit) },
                isSubscribedToPremiumUseCase(),
            ) { _, subscribedToPremium ->
                loadWidgetInfo(subscribedToPremium)
            }.flowOn(coroutinesDispatchers.io),
        )

    private fun loadWidgetInfo(subscribedToPremium: Boolean): TSSWidgetInfo {
        val period = Period.ThisWeek(userSettingsController)
        val workouts = if (subscribedToPremium) {
            val username = currentUserController.username
            val startMillis = period.beginDate.atStartOfDay().toEpochMilli()
            val endMillis = period.endDate.atStartOfDay().toEpochMilli()
            fetchWorkoutsByPeriod(workoutHeaderController, username, startMillis, endMillis)
        } else {
            emptyList()
        }

        val currentPeriodWorkoutsByDay = workouts.groupBy { it.startTime.toLocalDate() }
        val dailyWorkoutTsses = mutableListOf<Float>()
        (period.beginDate..period.endDate).iterator().forEach { day ->
            val workoutsForDay = currentPeriodWorkoutsByDay[day] ?: emptyList()
            dailyWorkoutTsses.add(workoutsForDay.sumByFloat {
                it.tss?.trainingStressScore ?: 0.0F
            })
        }
        val currentPeriodTotalWorkoutTss = dailyWorkoutTsses.sum()

        val progresses = generateDailyBarProgresses(dailyWorkoutTsses.map { it.toDouble() })
        val (subtitle, subtitleIconRes) =
            formatPeriodChangeSubtitle(subscribedToPremium, period, currentPeriodTotalWorkoutTss)
        val title = currentPeriodTotalWorkoutTss.coerceAtLeast(0.0F).formatTSSTitle()
        return TSSWidgetInfo(
            premiumRequired = !subscribedToPremium,
            period = period,
            progresses = progresses,
            title = title,
            subtitle = subtitle,
            subtitleIconRes = subtitleIconRes,
        )
    }

    private fun formatPeriodChangeSubtitle(
        premiumSubscribed: Boolean,
        period: Period,
        currentPeriodTotalWorkoutTss: Float,
    ): Pair<String, Int?> {
        val (previousPeriodStartMillis, previousPeriodEndMillis) = period.previousPeriod
        val previousPeriodWorkouts = if (premiumSubscribed) {
            fetchWorkoutsByPeriod(
                workoutHeaderController,
                currentUserController.username,
                previousPeriodStartMillis,
                previousPeriodEndMillis,
            )
        } else {
            emptyList()
        }
        val previousPeriodTotalWorkoutTss =
            previousPeriodWorkouts.sumByFloat { it.tss?.trainingStressScore ?: 0.0F }

        val changeSinceLastPeriod = if (previousPeriodTotalWorkoutTss > 0.0F) {
            currentPeriodTotalWorkoutTss - previousPeriodTotalWorkoutTss
        } else if (currentPeriodTotalWorkoutTss > 0) {
            currentPeriodTotalWorkoutTss
        } else {
            null
        }

        val formatedTss = changeSinceLastPeriod?.let {
            abs(it).formatTSS()
        } ?: return context.getString(R.string.widget_no_data_subtitle) to null
        val arrowRes =
            if (changeSinceLastPeriod >= 0) R.drawable.widget_up_arrow else R.drawable.widget_down_arrow
        return formatedTss to arrowRes
    }

    private fun Float.formatTSS(): String =
        "${infoModelFormatter.formatTssValue(this)} ${context.getString(R.string.workout_values_headline_tss)}"

    private fun Float.formatTSSTitle(): AnnotatedString =
        generateWidgetTitle(
            infoModelFormatter.formatTssValue(this),
            context.getString(R.string.workout_values_headline_tss)
        )

    private companion object {
        fun InfoModelFormatter.formatTssValue(tss: Float): String = formatTss(tss).orEmpty()
    }
}
