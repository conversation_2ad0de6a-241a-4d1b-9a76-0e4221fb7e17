package com.stt.android.home.settings.v2

import android.content.Context
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.Sex
import com.stt.android.domain.user.User
import com.stt.android.domain.user.UserSettings
import com.stt.android.utils.LocaleUtils
import java.math.RoundingMode
import java.util.Locale
import kotlin.math.roundToInt

data class UserSettingsViewData(
    val username: String,
    val name: String,
    val bio: String,
    val avatar: String,
    val cover: String,
    val location: String,
    val showLocation: Boolean,
    val weight: String,
    val height: String,
    val gender: Sex,
    val birthDate: Long,
    // Raw data for dialogs
    val weightInGrams: Int,
    val heightInCentimeters: Int,
    val measurementUnit: MeasurementUnit,
    // Heart rate settings
    val maxHeartRate: Int,
    val restHeartRate: Int,
) {
    companion object {
        private const val HEIGHT_METRIC_UNIT = " cm"
        private const val HEIGHT_FEET_UNIT = "'"
        private const val HEIGHT_INCH_UNIT = "''"

        fun fromUserSettings(context: Context, userSettings: UserSettings, user: User): UserSettingsViewData {
            val weight = buildString {
                append(
                    userSettings.measurementUnit
                        .toWeightUnit(userSettings.weight)
                        .toBigDecimal()
                        .setScale(0, RoundingMode.HALF_UP)
                        .stripTrailingZeros()
                        .toPlainString()
                )
                append(" ")
                append(context.getString(userSettings.measurementUnit.weightUnit))
            }
            val height = if (userSettings.measurementUnit == MeasurementUnit.METRIC) {
                "${userSettings.height}$HEIGHT_METRIC_UNIT"
            } else {
                val inches = (userSettings.height / 2.54).roundToInt()
                val feet = inches / 12
                val remainingInches = inches % 12
                "${feet}$HEIGHT_FEET_UNIT${remainingInches}$HEIGHT_INCH_UNIT"
            }
            return UserSettingsViewData(
                username = user.username,
                name = userSettings.realName.orEmpty(),
                bio = userSettings.description.orEmpty(),
                avatar = user.profileImageUrl.orEmpty(),
                cover = user.coverImageUrl.orEmpty(),
                location = getSelectedLocationDisplayName(context, userSettings),
                showLocation = userSettings.isShowLocale,
                weight = weight,
                height = height,
                gender = userSettings.gender,
                birthDate = userSettings.birthDate,
                // Raw data for dialogs
                weightInGrams = userSettings.weight ?: 0,
                heightInCentimeters = userSettings.height ?: 0,
                measurementUnit = userSettings.measurementUnit,
                // Heart rate settings
                maxHeartRate = userSettings.hrMaximum ?: 0,
                restHeartRate = userSettings.hrRest ?: 0,
            )
        }

        private fun getSelectedLocationDisplayName(context: Context, settings: UserSettings): String {
            val currentCountryCode = settings.country
            if (currentCountryCode.isNullOrBlank()) return ""

            val countryName = LocaleUtils.getDisplayCountry(currentCountryCode, context)
            val stateName = getSelectedStateDisplayName(settings)

            return if (stateName.isNotBlank()) {
                "$countryName\n$stateName"
            } else {
                countryName
            }
        }

        private fun getSelectedStateDisplayName(settings: UserSettings): String {
            val countryCode = settings.country
            val countrySubdivision = settings.countrySubdivision
            return if (countryCode == Locale.US.country && countrySubdivision.isNotEmpty()) {
                LocaleUtils.getUSStateFromIso3CountrySubdivisionCode(countrySubdivision) ?: ""
            } else ""
        }
    }
}
