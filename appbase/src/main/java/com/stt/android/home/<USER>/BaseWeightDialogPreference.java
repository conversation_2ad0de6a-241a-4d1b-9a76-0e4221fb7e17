package com.stt.android.home.settings;

import android.content.Context;
import android.text.InputType;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.UserSettings;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Locale;

/*
 * BigDecimal is used to limit the precision to one decimal. If the precision is not limited,
 * the unit conversion might return undesirably long Strings. Also, now the String form of the
 * weight will match the weight that was input, so if you set your weight as 70, you will see
 * 70, when you go modify it. Also, if you set it to 70.1, you will see that when you return
 * to the weight setting. If you set it to 70.15, it will be rounded up to 70.2.
 */
public class BaseWeightDialogPreference extends MeasurementUnitAwareTitleDialogPreference {

    /**
     * Maximum accepted value in grams
     */
    private static final int MAX_VALUE = 250000;
    /**
     * Minimum accepted value in grams
     */
    private static final int MIN_VALUE = 1000;

    public BaseWeightDialogPreference(Context context) {
        this(context, null);
    }

    public BaseWeightDialogPreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.preferenceStyle);
    }

    public BaseWeightDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, defStyleAttr);
    }

    public BaseWeightDialogPreference(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected void onSetInitialValue(boolean restorePersistedValue, Object defaultValue) {
        super.onSetInitialValue(restorePersistedValue, defaultValue);
        setSummary(
            getWeightWithUnitSummary(
                getPersistedString(String.valueOf(UserSettings.DEFAULT_WEIGHT_GRAMS))
            ));
    }

    protected String getWeightWithUnitSummary(String weightKg) {
        return String.format(Locale.getDefault(), "%s %s", weightKg,
            getContext().getString(getMeasurementUnit().getWeightUnit()));
    }

    /**
     * @param value User entered value (can be null)
     * @return the value in grams of the user entered value or default value if the user provided
     * string is invalid.
     */
    private int convertToGrams(String value) {
        MeasurementUnit unit = getMeasurementUnit();
        BigDecimal bd;
        try {
            bd = new BigDecimal(value);
        } catch (NumberFormatException e) {
            // value might be null, just the '.' or some other invalid string.
            // We set a default value for all those cases.
            if (unit == MeasurementUnit.IMPERIAL) {
                bd = new BigDecimal(UserSettings.DEFAULT_WEIGHT_POUNDS);
            } else {
                bd = new BigDecimal(UserSettings.DEFAULT_WEIGHT_KILOGRAMS);
            }
        }
        bd = bd.setScale(1, RoundingMode.HALF_UP);
        return unit.fromWeightUnit(bd.doubleValue());
    }

    protected float convertToKilograms(String value) {
        return convertToGrams(value) / 1000f;
    }

    @Nullable
    private String convertToWeightUnit(@Nullable String grams) {
        if (TextUtils.isEmpty(grams) || grams.trim().length() == 0) {
            return null;
        }

        MeasurementUnit unit = getMeasurementUnit();
        int valueInGrams = Integer.parseInt(grams);
        double valueInWeightUnit = unit.toWeightUnit(valueInGrams);
        BigDecimal bd = new BigDecimal(String.valueOf(valueInWeightUnit));
        bd = bd.setScale(0, RoundingMode.HALF_UP);
        return bd.stripTrailingZeros().toPlainString();
    }

    @Override
    protected void onInitDialogView(View dialogView) {
        TextView view = (TextView) dialogView.findViewById(R.id.numberPicker);
        view.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
    }

    @Override
    protected void onDialogOpened(View rootView) {
        TextView mTextViewDialog = ((EditText) rootView.findViewById(R.id.numberPicker));
        mTextViewDialog.setText(
            getPersistedString(String.valueOf(UserSettings.DEFAULT_WEIGHT_GRAMS)));
        super.onDialogOpened(rootView);
    }

    @Override
    protected String getPersistedString(String defaultReturnValue) {
        return convertToWeightUnit(super.getPersistedString(defaultReturnValue));
    }

    @Override
    protected boolean persistString(String value) {
        setSummary(getWeightWithUnitSummary(value));
        return super.persistString((TextUtils.isEmpty(value) || value.trim().length() == 0) ? null
            : String.valueOf(convertToGrams(value)));
    }

    @Override
    protected String AcceptedValue(String valueStr) {
        int valueInGrams = convertToGrams(valueStr);
        if (valueInGrams > MAX_VALUE) {
            valueInGrams = MAX_VALUE;
        }
        if (valueInGrams < MIN_VALUE) {
            valueInGrams = MIN_VALUE;
        }
        return convertToWeightUnit(String.valueOf(valueInGrams));
    }
}
