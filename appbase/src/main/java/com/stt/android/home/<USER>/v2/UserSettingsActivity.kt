package com.stt.android.home.settings.v2

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.home.settings.v2.ui.UserSettingsScreen
import com.stt.android.home.settings.v2.ui.SettingItemType
import com.stt.android.usecases.startup.LowPriorityStartupUseCase.Companion.STORE_NAME_CHINA
import dagger.hilt.android.AndroidEntryPoint

@OptIn(ExperimentalMaterial3Api::class)
@AndroidEntryPoint
class UserSettingsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            val snackbarHostState = remember { SnackbarHostState() }
            Scaffold(
                topBar = {
                    TopAppBar(
                        title = { Text(text = stringResource(R.string.settings_general_user_settings).uppercase()) },
                        navigationIcon = {
                            SuuntoIconButton(
                                icon = SuuntoIcons.ActionBack,
                                onClick = { finish() },
                                contentDescription = stringResource(R.string.back),
                            )
                        },
                    )
                },
                snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
            ) { paddingValues ->
                Surface(
                    modifier = Modifier
                        .padding(paddingValues)
                        .narrowContentWithBgColors(
                            backgroundColor = MaterialTheme.colorScheme.surface,
                            outerBackgroundColor = MaterialTheme.colorScheme.background
                        )
                ) {
                    UserSettingsScreen(
                        viewModel = hiltViewModel(),
                        getSettingTypes(),
                        snackbarHostState = snackbarHostState,
                    )
                }
            }
        }
    }

    private fun getSettingTypes(): List<SettingItemType> {
        val commonItems = listOf(
            SettingItemType.REAL_NAME,
            SettingItemType.USER_NAME,
            SettingItemType.LOCATION,
            // Hide location switch for now
//            SettingItemType.LOCATION_SWITCH,
            SettingItemType.BIO,
            SettingItemType.COVER_PHOTO
        )
        if (BuildConfig.FLAVOR_store.equals(STORE_NAME_CHINA)) {
            return commonItems.toMutableList().apply {
                remove(SettingItemType.LOCATION)
                remove(SettingItemType.LOCATION_SWITCH)
            }
        }
        return commonItems
    }

    companion object {
        fun newStartIntent(context: Context): Intent {
            return Intent(context, UserSettingsActivity::class.java)
        }
    }
}
