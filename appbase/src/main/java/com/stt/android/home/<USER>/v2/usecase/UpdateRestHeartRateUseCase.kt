package com.stt.android.home.settings.v2.usecase

import com.stt.android.controllers.UserSettingsController
import javax.inject.Inject

class UpdateRestHeartRateUseCase @Inject constructor(
    private val userSettingsController: UserSettingsController,
) : SetUserBaseInfoUseCase() {
    operator fun invoke(restHeartRate: Int) {
        val currentSettings = userSettingsController.settings
        val updatedSettings = currentSettings.setHrRest(restHeartRate)
        userSettingsController.storeSettings(updatedSettings)
    }
}
