package com.stt.android.home.settings.v2.usecase

import com.stt.android.controllers.UserSettingsController
import javax.inject.Inject

class UpdateMaxHeartRateUseCase @Inject constructor(
    private val userSettingsController: UserSettingsController,
) : SetUserBaseInfoUseCase() {
    operator fun invoke(maxHeartRate: Int) {
        val currentSettings = userSettingsController.settings
        val updatedSettings = currentSettings.setHrMaximum(maxHeartRate)
        userSettingsController.storeSettings(updatedSettings)
    }
}
