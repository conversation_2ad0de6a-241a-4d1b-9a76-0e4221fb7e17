package com.stt.android.home.people;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.snackbar.Snackbar;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.databinding.ActivityFacebookFriendsBinding;
import com.stt.android.domain.STTErrorCodes;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.social.userprofile.UserProfileActivity;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;

@AndroidEntryPoint
public class FindFbFriendsActivity extends AppCompatActivity
    implements FindFbFriendsView, View.OnClickListener {

    private static final String KEY_FB_FRIENDS = "com.stt.android.KEY_FB_FRIENDS";
    private static final String KEY_SOURCE = "com.stt.android.KEY_SOURCE";

    private ActivityFacebookFriendsBinding binding;

    @Inject
    FindFbFriendsPresenter findFbFriendsPresenter;

    private FbUserFollowStatusAdapter fbUserFollowStatusAdapter;

    //Let's set default just in case. We don't want to crash for analytics
    private Source source = Source.DEFAULT;

    public static Intent newStartIntent(Context context, Source source) {
        return new Intent(context, FindFbFriendsActivity.class).putExtra(KEY_SOURCE,
            source);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFacebookFriendsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setRecyclerView();

        if (savedInstanceState != null) {
            List<UserFollowStatus> userFollowStatuses =
                savedInstanceState.getParcelableArrayList(KEY_FB_FRIENDS);
            if (userFollowStatuses != null) {
                for (UserFollowStatus userFollowStatus : userFollowStatuses) {
                    fbUserFollowStatusAdapter.updateStatus(userFollowStatus);
                }
            }
        }

        if (getIntent().getSerializableExtra(KEY_SOURCE) != null) {
            source = (Source) getIntent().getSerializableExtra(KEY_SOURCE);
        }

        setSupportActionBar(binding.facebookFriendsToolbar);
        getSupportActionBar().setTitle(R.string.find_facebook_friends);

        //show up arrow if not coming somewhere else than fb login
        if (!source.equals(Source.LOGIN)) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        } else {
            binding.facebookFriendsToolbar.setNavigationIcon(null);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        //show done button if user just logged in with fb
        if (source.equals(Source.LOGIN)) {
            getMenuInflater().inflate(R.menu.add_friend, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    protected void onStart() {
        super.onStart();
        findFbFriendsPresenter.takeView(this);
        //if list is empty fetch from backend
        if (fbUserFollowStatusAdapter.getFollowStatuses().isEmpty()) {
            findFbFriendsPresenter.findFacebookFriends();
        } else {
            //we need to update the list if status was changed in user profile view
            findFbFriendsPresenter.updateFaceBookFriendStatuses(
                fbUserFollowStatusAdapter.getFollowStatuses());
            hideLoading();
        }

        findFbFriendsPresenter.trackEvent(
            AnalyticsEvent.FACEBOOK_FRIENDS_SCREEN, "Source", source.name);
    }

    private void setRecyclerView() {
        fbUserFollowStatusAdapter = new FbUserFollowStatusAdapter(findFbFriendsPresenter,
            Collections.<UserFollowStatus>emptyList(), this,
            AnalyticsPropertyValue.FollowSourceProperty.FIND_FB_FRIENDS);
        binding.facebookFriendsRecyclerView.setAdapter(fbUserFollowStatusAdapter);
        LinearLayoutManager lm = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        binding.facebookFriendsRecyclerView.setLayoutManager(lm);
    }

    @Override
    protected void onStop() {
        super.onStop();
        findFbFriendsPresenter.dropView();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            finish();
            return true;
        } else if (itemId == R.id.accept) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onFbFriendsLoaded(List<UserFollowStatus> facebookFriends) {
        binding.facebookFriendsFindPeopleLoadingSpinner.setVisibility(View.GONE);
        binding.facebookFriendsRecyclerView.setVisibility(View.VISIBLE);
        binding.facebookFriendsEmptyStateGroup.setVisibility(View.GONE);
        for (UserFollowStatus userFollowStatus : facebookFriends) {
            fbUserFollowStatusAdapter.updateStatus(userFollowStatus);
        }
        //update the header text
        fbUserFollowStatusAdapter.notifyItemChanged(0);
    }

    @Override
    public void createAddAllSnackBar(int numOfFriendsToAdd, View.OnClickListener undoAction) {
        Snackbar.make(binding.getRoot(),
            getResources().getQuantityString(R.plurals.fb_friends_added, numOfFriendsToAdd,
                numOfFriendsToAdd), Snackbar.LENGTH_SHORT)
            .setAction(getString(R.string.undo), undoAction)
            .show();
    }

    @Override
    public void showLoading() {
        if (binding.facebookFriendsFindPeopleLoadingSpinner.getVisibility() == View.GONE) {
            binding.facebookFriendsFindPeopleLoadingSpinner.setVisibility(View.VISIBLE);
            binding.facebookFriendsRecyclerView.setVisibility(View.GONE);
        }
    }

    @Override
    public void hideLoading() {
        if (binding.facebookFriendsFindPeopleLoadingSpinner.getVisibility() == View.VISIBLE) {
            binding.facebookFriendsFindPeopleLoadingSpinner.setVisibility(View.GONE);
            binding.facebookFriendsRecyclerView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Add all is clicked
     */
    @Override
    public void onClick(View view) {
        findFbFriendsPresenter.followAllFacebookFriendsWithDelay(
            fbUserFollowStatusAdapter.getUnFollowedUserFollowStatuses());
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (fbUserFollowStatusAdapter != null) {
            outState.putParcelableArrayList(KEY_FB_FRIENDS,
                new ArrayList<>(fbUserFollowStatusAdapter.getFollowStatuses()));
        }
    }

    @Override
    public void updateStatus(UserFollowStatus userFollowStatus) {
        fbUserFollowStatusAdapter.updateStatus(userFollowStatus);
        hideLoading();
    }

    @Override
    public void showUserProfile(@NonNull String username) {
        startActivity(UserProfileActivity.newStartIntent(this, username, false));
    }

    @Override
    public void showUnfollowDialog(final UserFollowStatus userFollowStatus) {
        FollowActionViewHelper.showUnfollowDialog(this, findFbFriendsPresenter, userFollowStatus);
    }

    @Override
    public void showActionError(final UserFollowStatus userFollowStatus,
        View.OnClickListener tryAgainAction) {
        FollowActionViewHelper.showActionError(this, binding.getRoot(), userFollowStatus, tryAgainAction);
    }

    @Override
    public void showActionError(View.OnClickListener tryAgainAction) {
        hideLoading();
        FollowActionViewHelper.showActionError(this, binding.getRoot(), tryAgainAction);
    }

    @Override
    public void showError(@NonNull STTErrorCodes errorCode) {
        FollowActionViewHelper.showError(binding.getRoot(), errorCode);
    }

    @Override
    public void showFollowActionSpinner(UserFollowStatus userFollowStatus) {
        RecyclerView.ViewHolder viewHolderForItemId =
            binding.facebookFriendsRecyclerView.findViewHolderForItemId(userFollowStatus.getId().hashCode());
        if (viewHolderForItemId != null && viewHolderForItemId instanceof FollowStatusViewHolder) {
            ((FollowStatusViewHolder) viewHolderForItemId).showActionSpinner();
        }
    }

    @Override
    public void showEmptyView() {
        binding.facebookFriendsFindPeopleLoadingSpinner.setVisibility(View.GONE);
        binding.facebookFriendsRecyclerView.setVisibility(View.GONE);
        binding.facebookFriendsEmptyStateGroup.setVisibility(View.VISIBLE);
        binding.facebookFriendsFindPeopleButton.setOnClickListener((view) -> onBackPressed());
    }

    public enum Source {
        LOGIN("StartingFlow"),
        FIND_PEOPLE("FindPeopleTab"),
        FEED("SuggestionInFeed");

        public static final Source DEFAULT = FIND_PEOPLE;

        public final String name;

        Source(String source) {
            this.name = source;
        }
    }
}
