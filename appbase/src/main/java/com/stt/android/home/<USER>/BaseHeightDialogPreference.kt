package com.stt.android.home.settings

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.preference.PreferenceViewHolder
import com.stt.android.R
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserSettings
import com.stt.android.ui.extensions.setGone
import com.stt.android.ui.extensions.setVisible
import com.stt.android.ui.utils.MeasurementUnitAware
import java.util.Locale

abstract class BaseHeightDialogPreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : CustomDialogPreference(context, attrs, defStyleAttr, defStyleRes), MeasurementUnitAware {

    private var mDialogTitleTemplate: CharSequence? = null
    private var mTextViewDialog: EditText? = null
    private var mFeetTextViewDialog: EditText? = null
    private var mInchTextViewDialog: EditText? = null

    override fun onAttached() {
        super.onAttached()
        setPositiveButtonText(R.string.ok)
        setNegativeButtonText(R.string.cancel)
        setNeedInputMethod(true)
        mDialogTitleTemplate = dialogTitle
    }

    override fun onSetInitialValue(defaultValue: Any?) {
        super.onSetInitialValue(defaultValue)
        val defaultHeightCentimeters = UserSettings.DEFAULT_HEIGHT_CENTIMETERS.toString()
        setSummary(
            getHeightWithUnitSummary(
                getPersistedString(defaultHeightCentimeters) ?: defaultHeightCentimeters
            )
        )
    }

    protected fun getHeightWithUnitSummary(heightCentimeters: String): String {
        return if (measurementUnit == MeasurementUnit.METRIC) {
            String.format(Locale.getDefault(), "%s%s", heightCentimeters, HEIGHT_METRIC_UNIT)
        } else {
            val inches = Math.round(heightCentimeters.toFloat() / 2.54)
            val feet = "${(inches / 12).toInt()}${HEIGHT_FEET_UNIT}"
            val remainingInches = "${inches % 12}${HEIGHT_INCH_UNIT}"
            String.format(Locale.getDefault(), "%s%s", feet, remainingInches)
        }
    }

    override fun persistString(value: String): Boolean {
        setSummary(getHeightWithUnitSummary(value))
        return super.persistString(value)
    }

    /**
     * What to do when the preference is being displayed
     */
    override fun onInitPreferenceView(viewHolder: PreferenceViewHolder?) {
    }

    /**
     * What to before preference_dialog is displayed
     */
    override fun onPrepareDialogBuilder(mBuilder: AlertDialog.Builder) {
        if (measurementUnit == MeasurementUnit.IMPERIAL) {
            mBuilder.setTitle("")
        } else {
            mBuilder.setTitle(mDialogTitleTemplate)
        }
    }

    override fun onInitDialogView(dialogView: View) {
        val metricView = dialogView.findViewById<View>(R.id.llMetric)
        val imperialView = dialogView.findViewById<View>(R.id.llImperial)
        if (measurementUnit == MeasurementUnit.METRIC) {
            val numberPickerView = dialogView.findViewById<View>(R.id.numberPicker) as TextView
            numberPickerView.inputType = InputType.TYPE_CLASS_NUMBER
        } else {
            metricView.setGone()
            imperialView.setVisible()

            val feedNumberPickerView =
                dialogView.findViewById<View>(R.id.feedNumberPicker) as TextView
            feedNumberPickerView.inputType = InputType.TYPE_CLASS_NUMBER

            val inchNumberPickerView =
                dialogView.findViewById<View>(R.id.inchNumberPicker) as TextView
            inchNumberPickerView.inputType = InputType.TYPE_CLASS_NUMBER
        }
    }

    /**
     * What to do when the preference_dialog is being displayed
     */
    override fun onDialogOpened(rootView: View) {
        if (measurementUnit == MeasurementUnit.METRIC) {
            mTextViewDialog = (rootView.findViewById<View>(R.id.numberPicker) as EditText)
            mTextViewDialog?.let { editText ->
                editText.addTextChangedListener(InputTextWatcher(editText, HEIGHT_METRIC_UNIT))
                (editText as TextView).text =
                    getPersistedString(UserSettings.DEFAULT_HEIGHT_CENTIMETERS.toString())
            }
        } else {
            val heightCentimeters = getPersistedString(UserSettings.DEFAULT_HEIGHT_CENTIMETERS.toString())
            val inches = Math.round(heightCentimeters.toFloat() / 2.54)
            val feet = "${(inches / 12).toInt()}"
            val remainingInches = "${inches % 12}"
            mFeetTextViewDialog = (rootView.findViewById<View>(R.id.feedNumberPicker) as EditText)
            mFeetTextViewDialog?.let { editText ->
                editText.addTextChangedListener(InputTextWatcher(editText, HEIGHT_FEET_UNIT))
                (editText as TextView).text = feet
            }
            mInchTextViewDialog = (rootView.findViewById<View>(R.id.inchNumberPicker) as EditText)
            mInchTextViewDialog?.let { editText ->
                editText.addTextChangedListener(InputTextWatcher(editText, HEIGHT_INCH_UNIT))
                (editText as TextView).text = remainingInches
            }
        }
    }

    private inner class InputTextWatcher(val editText: EditText, val suffix: String): TextWatcher {
        private var isDeleting = false
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            isDeleting = count > after
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        @SuppressLint("SetTextI18n")
        override fun afterTextChanged(s: Editable?) {
            s?.let { content ->
                val text = content.toString()
                if (isDeleting) {
                    if (!text.endsWith(suffix)) {
                        if (text.length > suffix.length) {
                            editText.setText(text.substring(0, text.length - suffix.length) + suffix)
                        } else {
                            editText.setText("")
                        }
                        editText.setSelection(editText.text.length)
                    }
                } else {
                    if (text.isNotEmpty() && !text.endsWith(suffix)) {
                        editText.setText(text.filter { it.isDigit() } + suffix)
                        editText.setSelection(editText.text.length)
                    }
                }
            }
        }
    }

    /**
     * What to do when the preference_dialog is being dismissed
     */
    override fun onDialogClosed(positiveResult: Boolean) {
        if (positiveResult) {
            if (measurementUnit == MeasurementUnit.METRIC) {
                val heightCentimeters =
                    mTextViewDialog?.text?.toString()?.replace(HEIGHT_METRIC_UNIT, "")
                val height = heightCentimeters?.toIntOrNull()
                    ?.takeIf { it in MIN_HEIGHT_CENTIMETERS..MAX_HEIGHT_CENTIMETERS }
                    ?: UserSettings.DEFAULT_HEIGHT_CENTIMETERS
                persistString(height.toString())
            } else {
                val feet =
                    mFeetTextViewDialog?.text?.toString()?.replace(HEIGHT_FEET_UNIT, "")
                        ?.toIntOrNull()
                        ?.takeIf { it in MIN_HEIGHT_FEET..MAX_HEIGHT_FEET }
                        ?: UserSettings.DEFAULT_HEIGHT_FEET
                val remainInch =
                    mInchTextViewDialog?.text?.toString()?.replace(HEIGHT_INCH_UNIT, "")
                        ?.toIntOrNull()
                        ?.takeIf { it in MIN_HEIGHT_INCH..MAX_HEIGHT_INCH }
                        ?: UserSettings.DEFAULT_HEIGHT_INCH
                val totalInch = feet * 12 + remainInch.toDouble()
                val heightCentimeters = Math.round(totalInch * 2.54).toString()
                persistString(heightCentimeters)
            }
        }
    }

    override fun getMeasurementUnit(): MeasurementUnit {
        val measurementUnitName =
            sharedPreferences!!.getString("measurement_unit", "metric")
        return MeasurementUnit.valueOf(measurementUnitName!!.uppercase())
    }

    companion object {
        private const val MIN_HEIGHT_CENTIMETERS = 91
        private const val MAX_HEIGHT_CENTIMETERS = 241
        private const val MIN_HEIGHT_FEET = 3
        private const val MAX_HEIGHT_FEET = 7
        private const val MIN_HEIGHT_INCH = 0
        private const val MAX_HEIGHT_INCH = 11

        private const val HEIGHT_METRIC_UNIT = " cm"
        private const val HEIGHT_FEET_UNIT = "'"
        private const val HEIGHT_INCH_UNIT = "''"
    }
}
