package com.stt.android.home.settings

import android.content.Context
import android.text.InputType
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.preference.PreferenceViewHolder
import com.stt.android.R
import com.stt.android.databinding.ItemHeartRateZoneBinding
import com.stt.android.hr.HeartRateZone
import java.lang.NumberFormatException

/**
 * Preference to store the maximum heart rate setting. Checks for its validity. Accepted values are
 * from 100 (or the value set by the user as moderate high) to 250.
 */
abstract class BaseMaxHeartRatePreference
@JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : AcceptedValueDialogPreference(context, attrs, defStyleAttr, defStyleRes) {

    abstract val shouldShowZoneInfo: Boolean

    override fun onInitDialogView(dialogView: View) {
        val view = dialogView.findViewById<View>(R.id.numberPicker) as TextView
        view.inputType = InputType.TYPE_CLASS_NUMBER
    }

    override fun acceptValue(newValueStr: String): String {
        val min = Integer.valueOf(ABSOLUTE_MIN)
        val newValue: Int = try {
            Integer.valueOf(newValueStr)
        } catch (e: NumberFormatException) {
            // If the user input is invalid we set it to the minimum
            min
        }.coerceIn(min..MAX_VALUE)
        return newValue.toString()
    }

    override fun onInitPreferenceView(viewHolder: PreferenceViewHolder) {
        super.onInitPreferenceView(viewHolder)
        val zoneDivider = viewHolder.findViewById(R.id.heart_rate_zone_divider)
        val zoneDisclaimer = viewHolder.findViewById(R.id.heart_rate_zones_disclaimer)
        val zoneContainer = viewHolder.findViewById(R.id.heart_rate_zones_container) as ViewGroup
        if (shouldShowZoneInfo) {
            zoneDivider.visibility = View.VISIBLE
            zoneDisclaimer.visibility = View.VISIBLE
            zoneContainer.visibility = View.VISIBLE
            showZoneInfo(
                zoneContainer,
                Integer.valueOf(getPersistedString(ABSOLUTE_MIN)),
                LayoutInflater.from(context)
            )
        } else {
            zoneDivider.visibility = View.GONE
            zoneDisclaimer.visibility = View.GONE
            zoneContainer.visibility = View.GONE
        }
    }

    private fun showZoneInfo(
        zoneContainer: ViewGroup?,
        maxHr: Int,
        layoutInflater: LayoutInflater
    ) {
        if (zoneContainer != null) {
            zoneContainer.removeAllViews()
            for (zone in HeartRateZone.entries) {
                addZoneDescription(zoneContainer, maxHr, zone, layoutInflater)
            }
        }
    }

    private fun addZoneDescription(
        zoneContainer: ViewGroup,
        maxHr: Int,
        zone: HeartRateZone,
        inflater: LayoutInflater
    ) {
        val holder = ZoneDescriptionHolder(inflater, zoneContainer)
        holder.setDescription(zone.description)
        holder.setTitle(zone.title)
        holder.setPercentageRange(zone.lowPercentage, zone.highPercentage)
        holder.setBpmRange(zone.getLowBpm(maxHr), zone.getHighBpm(maxHr))
        holder.setColor(zone.color)
        zoneContainer.addView(holder.binding.root)
    }

    private inner class ZoneDescriptionHolder(inflater: LayoutInflater, parent: ViewGroup) {

        val binding = ItemHeartRateZoneBinding.inflate(inflater, parent, false)

        fun setColor(@ColorRes colorRes: Int) {
            val color = ContextCompat.getColor(context, colorRes)
            binding.heartRateZoneBpm.setTextColor(color)
            binding.heartRateZoneStripe.setBackgroundColor(color)
        }

        fun setTitle(@StringRes titleRes: Int) {
            binding.heartRateZoneTitle.setText(titleRes)
        }

        fun setDescription(@StringRes descriptionRes: Int) {
            binding.heartRateZoneDescription.setText(descriptionRes)
        }

        fun setBpmRange(low: Int, high: Int) {
            if (low > 0) {
                binding.heartRateZoneBpm.text = context.getString(R.string.heart_rate_zone_bpm, low, high)
            } else {
                binding.heartRateZoneBpm.text = context.getString(
                    R.string.heart_rate_zone_bpm_no_low_limit,
                    high
                )
            }
        }

        fun setPercentageRange(low: Int, high: Int) {
            if (low > 0) {
                binding.heartRateZonePercentage.text =
                    context.getString(R.string.heart_rate_zone_percentage, low, high)
            } else {
                binding.heartRateZonePercentage.text =
                    context.getString(R.string.heart_rate_zone_percentage_no_low_limit, high)
            }
        }
    }

    companion object {
        private const val ABSOLUTE_MIN = "100"
        private const val MAX_VALUE = 250
    }
}
