package com.stt.android.home.settings.resetpassword

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.ui.RxViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.session.ResetPasswordUseCase
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.plusAssign
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ResetPasswordViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    userSettingsController: UserSettingsController,
    private val resetPasswordUseCase: ResetPasswordUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val sharedPreferences: SharedPreferences
) : RxViewModel(ioThread, mainThread) {

    val maskedEmail = userSettingsController.settings.email ?: ""

    val callInProgress: LiveData<Boolean>
        get() = _callInProgress

    val emailSent: LiveData<Boolean>
        get() = _emailSent

    val requestPasswordResetError: LiveData<Throwable>
        get() = _requestPasswordResetError

    val beginDefaultTransitionEvent: LiveData<Any>
        get() = _beginDefaultTransitionEvent

    val emailResent: LiveData<Any>
        get() = _emailResent

    val showHelpshift: LiveData<Any>
        get() = _showHelpshift

    private val _emailSent = MutableLiveData<Boolean>().apply { value = false }
    private val _callInProgress = MutableLiveData<Boolean>().apply { value = false }
    private val _requestPasswordResetError = SingleLiveEvent<Throwable>()
    private val _beginDefaultTransitionEvent = SingleLiveEvent<Any>()
    private val _showHelpshift = SingleLiveEvent<Any>()
    private val _emailResent = SingleLiveEvent<Any>()
    private var isRetry = false

    fun onResetPasswordButtonClicked() {
        isRetry = false
        resetPassword()

        emarsysAnalytics.trackEvent(AnalyticsEvent.RESET_PASSWORD_REQUESTED)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.RESET_PASSWORD_REQUESTED)
    }

    fun onTryAgainButtonClicked() {
        isRetry = true
        resetPassword()

        emarsysAnalytics.trackEvent(AnalyticsEvent.RESET_PASSWORD_TRY_AGAIN)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.RESET_PASSWORD_TRY_AGAIN)
    }

    fun onContactSupportButtonClicked() {
        _showHelpshift.call()

        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SOURCE, AnalyticsPropertyValue.ContactSupportSource.RESET_PASSWORD)
        }

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.CONTACT_SUPPORT, properties)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.CONTACT_SUPPORT, properties.map)
    }

    fun sendAnalyticsScreenEvent() {
        emarsysAnalytics.trackEvent(AnalyticsEvent.RESET_PASSWORD_SCREEN)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.RESET_PASSWORD_SCREEN)
    }

    private fun resetPassword() {
        Timber.d("resetPassword")
        _beginDefaultTransitionEvent.call()
        _callInProgress.value = true
        disposables += rxCompletable { resetPasswordUseCase.resetPassword() }
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .subscribe(::handleResetPasswordSuccess, ::handleResetPasswordError)
    }

    private fun handleResetPasswordSuccess() {
        _beginDefaultTransitionEvent.call()
        _callInProgress.value = false
        _emailSent.value = true

        if (isRetry) {
            _emailResent.call()
        }

        sharedPreferences.edit {
            putLong(
                STTConstants.DefaultPreferences.KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT,
                System.currentTimeMillis()
            )
        }
    }

    private fun handleResetPasswordError(throwable: Throwable) {
        Timber.w(throwable, "handleResetPasswordError")
        _beginDefaultTransitionEvent.call()
        _callInProgress.value = false
        _requestPasswordResetError.value = throwable
    }
}
