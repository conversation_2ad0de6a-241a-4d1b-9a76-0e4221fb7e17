package com.stt.android.home.dashboardv2.widgets

import androidx.annotation.DrawableRes
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.domain.workout.ActivityType

internal data class ActivityDistanceWidgetInfo(
    val period: Period,
    val representativeActivityType: ActivityType,
    val progresses: List<Float>,
    val title: AnnotatedString,
    val subtitle: String,
    @DrawableRes val subtitleIconRes: Int? = null,
) : WidgetInfo
