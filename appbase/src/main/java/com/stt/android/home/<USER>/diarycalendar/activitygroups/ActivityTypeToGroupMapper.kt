package com.stt.android.home.diary.diarycalendar.activitygroups

import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import javax.inject.Inject

class ActivityTypeToGroupMapper @Inject constructor() : ActivityGroupMapper {

    /**
     * TODO: Get this stuff from SIM or at least align with SIM
     */
    override fun activityTypeIdToGroup(id: Int): ActivityGroup =
        when (ActivityType.valueOf(id)) {
            ActivityType.RUNNING, ActivityType.TRAIL_RUNNING, ActivityType.TREADMILL, ActivityType.TRACK_RUNNING,
            ActivityType.VERTICAL_RUN
            -> ActivityGroup.Running

            ActivityType.CYCLING, ActivityType.MOUNTAIN_BIKING, ActivityType.INDOOR_CYCLING,
            ActivityType.GRAVEL_CYCLING, ActivityType.E_BIKING, ActivityType.E_MTB,
            ActivityType.CYCLOCROSS
            -> ActivityGroup.Cycling

            ActivityType.ORIENTEERING, ActivityType.TRIATHLON, ActivityType.MULTISPORT,
            ActivityType.ROLLER_SKATING, ActivityType.MOTOR_SPORTS, ActivityType.ROLLER_SKIING,
            ActivityType.CROSSFIT, ActivityType.TRACK_AND_FIELD, ActivityType.SWIMRUN,
            ActivityType.DUATHLON, ActivityType.AQUATHLON, ActivityType.ADVENTURE_RACING,
            ActivityType.OBSTACLE_RACING, ActivityType.PARKOUR, ActivityType.SKATEBOARDING, ActivityType.WHEELCHAIRING,
            ActivityType.HANDCYCLING,
            -> ActivityGroup.Performance

            ActivityType.GYMNASTICS, ActivityType.CROSSTRAINER, ActivityType.INDOOR_ROWING,
            ActivityType.GYM, ActivityType.AEROBICS, ActivityType.DANCING,
            ActivityType.CIRCUIT_TRAINING, ActivityType.YOGA, ActivityType.STRETCHING,
            ActivityType.KETTLEBELL, ActivityType.INDOOR, ActivityType.BOXING,
            ActivityType.COMBAT_SPORTS, ActivityType.FITNESS_CLASS, ActivityType.OUTDOOR_GYM, ActivityType.JUMP_ROPE,
            ActivityType.CALISTHENICS, ActivityType.MEDITATION, ActivityType.PILATES, ActivityType.NEW_YOGA
            -> ActivityGroup.IndoorSports

            ActivityType.TREKKING, ActivityType.HIKING, ActivityType.WALKING,
            ActivityType.NORDIC_WALKING, ActivityType.HORSEBACK_RIDING, ActivityType.HUNTING,
            ActivityType.FISHING, ActivityType.PARAGLIDING, ActivityType.CLIMBING,
            ActivityType.MOUNTAINEERING
            -> ActivityGroup.OutdoorAdventures

            ActivityType.CROSS_COUNTRY_SKIING, ActivityType.ICE_SKATING,
            ActivityType.DOWNHILL_SKIING, ActivityType.SNOWBOARDING, ActivityType.TELEMARKSKIING,
            ActivityType.SKI_TOURING, ActivityType.SNOWSHOEING, ActivityType.BACKCOUNTRY_SKIING,
            ActivityType.SPLITBOARDING, ActivityType.BIATHLON, ActivityType.SKI_MOUNTAINEERING,
            ActivityType.SKATE_SKIING, ActivityType.CLASSIC_SKIING
            -> ActivityGroup.WinterSports

            ActivityType.WINDSURFING, ActivityType.SURFING, ActivityType.KITESURFING_KITING,
            ActivityType.SWIMMING, ActivityType.OPENWATER_SWIMMING, ActivityType.SAILING,
            ActivityType.ROWING, ActivityType.KAYAKING, ActivityType.CANOEING, ActivityType.SUP,
            ActivityType.PADDLING, ActivityType.WATER_SPORTS
            -> ActivityGroup.Watersports

            ActivityType.FREEDIVING, ActivityType.SCUBADIVING, ActivityType.SNORKELING,
            ActivityType.MERMAIDING
            -> ActivityGroup.Diving

            ActivityType.ICE_HOCKEY, ActivityType.FLOORBALL, ActivityType.SOCCER,
            ActivityType.AMERICAN_FOOTBALL, ActivityType.HANDBALL, ActivityType.BASKETBALL,
            ActivityType.VOLLEYBALL, ActivityType.BASEBALL, ActivityType.SOFTBALL,
            ActivityType.CRICKET, ActivityType.BOWLING, ActivityType.RUGBY,
            ActivityType.FRISBEE_GOLF, ActivityType.CHEERLEADING, ActivityType.TENNIS,
            ActivityType.BADMINTON, ActivityType.TABLE_TENNIS, ActivityType.SQUASH,
            ActivityType.RACQUETBALL, ActivityType.GOLF, ActivityType.BALLGAMES,
            ActivityType.FUTSAL, ActivityType.PADEL, ActivityType.FIELD_HOCKEY
            -> ActivityGroup.TeamAndRacketSports

            else -> ActivityGroup.Unspecified
        }
}
