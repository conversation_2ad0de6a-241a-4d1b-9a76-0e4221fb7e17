package com.stt.android.home.settings;

import android.app.DatePickerDialog;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.DatePicker;
import androidx.preference.Preference;
import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.domain.user.UserSettings;
import com.stt.android.utils.CalendarProvider;
import com.stt.android.utils.CalendarProviderKt;
import com.stt.android.utils.DateUtils;
import java.time.DayOfWeek;
import java.util.Calendar;
import javax.inject.Inject;

/**
 * Preference to store the age setting. Checks for its validity.
 */
public abstract class BaseAgePreference extends Preference implements DatePickerDialog
    .OnDateSetListener {

    // Child class is expected to handle injection
    @Inject
    protected CalendarProvider calendarProvider;

    @Inject
    UserSettingsController userSettingsController;

    public BaseAgePreference(Context context) {
        this(context, null);
    }

    public BaseAgePreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.preferenceStyle);
    }

    public BaseAgePreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, defStyleAttr);
    }

    public BaseAgePreference(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected void onSetInitialValue(boolean restorePersistedValue, Object defaultValue) {
        super.onSetInitialValue(restorePersistedValue, defaultValue);
        setSummary(generateSummary());
    }

    @Override
    protected void onClick() {
        Calendar birthDate = calendarProvider.getCalendar();
        String persistedBirthDate = getPersistedString(null);
        if (!TextUtils.isEmpty(persistedBirthDate)) {
            birthDate.setTimeInMillis(Long.parseLong(persistedBirthDate));
        }
        DatePickerDialog datePickerDialog = new DatePickerDialog(getContext(), this, birthDate.get(Calendar.YEAR),
            birthDate.get(Calendar.MONTH), birthDate.get(Calendar.DAY_OF_MONTH));
        DayOfWeek firstDayOfWeek = userSettingsController.getSettings().getFirstDayOfTheWeek();
        DatePicker datePicker = datePickerDialog.getDatePicker();
        datePicker.setFirstDayOfWeek(CalendarProviderKt.toCalendarInt(firstDayOfWeek));
        datePicker.setMaxDate(System.currentTimeMillis());

        datePickerDialog.show();
    }

    @Override
    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
        Calendar birthDate = calendarProvider.getCalendar();
        birthDate.set(year, month, dayOfMonth);
        birthDate.set(Calendar.HOUR_OF_DAY, 0);
        birthDate.set(Calendar.MINUTE, 0);
        birthDate.set(Calendar.SECOND, 0);
        birthDate.set(Calendar.MILLISECOND, 0);
        persistString(Long.toString(birthDate.getTimeInMillis()));
        setSummary(generateSummary());
    }

    protected String generateSummary() {
        return String.valueOf(DateUtils.calculateBirthYear(
            Long.parseLong(getPersistedString(Long.toString(UserSettings.DEFAULT_BIRTH_DATE)))));
    }
}
