package com.stt.android.home.settings.v2.ui

import android.content.Intent
import androidx.appcompat.content.res.AppCompatResources
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.drawable.toDrawable
import coil3.asImage
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.STTErrorCodes
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.Sex
import com.stt.android.home.settings.v2.UserSettingsViewData
import com.stt.android.home.settings.v2.UserSettingsEvent
import com.stt.android.home.settings.v2.UserSettingsViewModel
import com.stt.android.social.userprofileV2.usecase.CropProfilePictureParams
import com.stt.android.social.userprofileV2.usecase.rememberClickProfilePictureHandlerWithSnackbar
import java.io.File
import com.stt.android.core.R as CR

private const val NAME_REGEX = "^[\\p{L}\\p{N}_\\s]+$"

@Composable
fun UserSettingsScreen(
    viewModel: UserSettingsViewModel,
    settingTypes: List<SettingItemType>,
    modifier: Modifier = Modifier,
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
) {
    val context = LocalContext.current
    val userSettingsViewData by viewModel.userSettingsViewData.collectAsState()
    val tempProfilePictureFile = viewModel.tempProfilePictureFile
    val tempCoverPictureFile = viewModel.tempCoverPictureFile
    var editRealNameDialogState by remember { mutableStateOf(false) }
    var editBioDialogState by remember { mutableStateOf(false) }
    val uploadStatus by viewModel.uploadImageStatus.collectAsState()
    val sensitiveWordsTips = stringResource(R.string.user_settings_sensitive_words_error)

    LaunchedEffect(Unit) {
        viewModel.event.collect { event ->
            when (event) {
                is UserSettingsEvent.SensitiveWordsError -> {
                    snackbarHostState.showSnackbar(
                        message = sensitiveWordsTips,
                    )
                }
            }
        }
    }


    UserSettingsContent(
        userSettingsViewData = userSettingsViewData,
        settingTypes = settingTypes,
        tempProfilePictureFile = tempProfilePictureFile,
        tempCoverPictureFile = tempCoverPictureFile,
        showHeightAndHeartRateSettings = viewModel.showHeightAndHeartRateSettings,
        snackbarHostState = snackbarHostState,
        onUpdateProfilePictureClicked = { viewModel.updateProfilePicture(it) },
        onUpdateCoverPhotoClicked = { viewModel.updateProfilePicture(it, isCoverPhoto = true) },
        onClickBioItem = { editBioDialogState = true },
        updateShowLocaleCallback = { viewModel.updateUserProfileShowLocale(it) },
        onClickRealNameItem = { editRealNameDialogState = true },
        onClickLocationItem = { viewModel.openLocationSelection(context) },
        onWeightChanged = { weight -> viewModel.updateWeight(weight) },
        onHeightChanged = { height -> viewModel.updateHeight(height) },
        onGenderChanged = { gender -> viewModel.updateGender(gender) },
        onBirthDateChanged = { birthDate -> viewModel.updateBirthDate(birthDate) },
        onMaxHeartRateChanged = { maxHeartRate -> viewModel.updateMaxHeartRate(maxHeartRate) },
        onRestHeartRateChanged = { restHeartRate -> viewModel.updateRestHeartRate(restHeartRate) },
        modifier = modifier.fillMaxSize(),
    )

    if (editBioDialogState) {
        InputTextDialog(
            range = 0..256,
            title = stringResource(R.string.profileDescription),
            value = userSettingsViewData.bio,
            placeholder = stringResource(R.string.profile_description_default),
            onDismiss = { editBioDialogState = false },
            onSave = {
                viewModel.updateUserProfileDescription(it.trim())
                editBioDialogState = false
            },
        )
    }
    if (editRealNameDialogState) {
        InputTextDialog(
            title = stringResource(R.string.user_settings_edit_name),
            value = userSettingsViewData.name,
            onDismiss = { editRealNameDialogState = false },
            onSave = {
                viewModel.updateUserProfileRealName(it.trim())
                editRealNameDialogState = false
            },
            regexString = NAME_REGEX,
        )
    }

    if (uploadStatus.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            CircularProgressIndicator(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(48.dp),
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
    if (uploadStatus.errorCode != null) {
        val errorMessage = when (uploadStatus.errorCode) {
            STTErrorCodes.PROFILE_IMAGE_AUDIT_FAILED.code -> {
                stringResource(R.string.review_failed)
            }

            STTErrorCodes.FILE_SIZE_EXCEEDS_LIMIT.code -> {
                stringResource(R.string.image_out_of_size)
            }

            else -> {
                stringResource(R.string.error_0)
            }
        }
        LaunchedEffect(uploadStatus.errorCode) {
            snackbarHostState.showSnackbar(errorMessage)
            viewModel.resetUploadImageStatus()
        }
    }
}

@Composable
private fun UserSettingsContent(
    userSettingsViewData: UserSettingsViewData,
    settingTypes: List<SettingItemType>,
    tempCoverPictureFile: File,
    tempProfilePictureFile: File,
    modifier: Modifier = Modifier,
    showHeightAndHeartRateSettings: Boolean = false,
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
    onUpdateProfilePictureClicked: (Intent) -> Unit = {},
    onUpdateCoverPhotoClicked: (Intent) -> Unit = {},
    updateShowLocaleCallback: (Boolean) -> Unit = {},
    onClickLocationItem: () -> Unit = {},
    onClickBioItem: () -> Unit = {},
    onClickRealNameItem: () -> Unit = {},
    onWeightChanged: (String) -> Unit = {},
    onHeightChanged: (Int) -> Unit = {},
    onGenderChanged: (Sex) -> Unit = {},
    onBirthDateChanged: (Long) -> Unit = {},
    onMaxHeartRateChanged: (Int) -> Unit = {},
    onRestHeartRateChanged: (Int) -> Unit = {},
) {
    Column(modifier = modifier) {
        ProfileAvatar(
            userSettingsViewData.avatar,
            tempProfilePictureFile,
            onUpdateProfilePictureClicked,
            snackbarHostState = snackbarHostState,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
        PersonalInfoSection(
            userSettingsViewData = userSettingsViewData,
            tempCoverPictureFile = tempCoverPictureFile,
            settingTypes = settingTypes,
            onUpdateCoverPhotoClicked = onUpdateCoverPhotoClicked,
            onClickBioItem = onClickBioItem,
            updateShowLocaleCallback = updateShowLocaleCallback,
            onClickRealNameItem = onClickRealNameItem,
            onClickLocationItem = onClickLocationItem,
            snackbarHostState = snackbarHostState,
        )

        Text(
            text = stringResource(R.string.settings_user_profile_guide_new),
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier
                .background(MaterialTheme.colorScheme.background)
                .padding(MaterialTheme.spacing.medium),
        )

        WeightSettingItem(
            currentWeight = userSettingsViewData.weight,
            weightInGrams = userSettingsViewData.weightInGrams,
            measurementUnit = userSettingsViewData.measurementUnit,
            onWeightChanged = onWeightChanged,
            modifier = Modifier.fillMaxWidth()
        )

        if (showHeightAndHeartRateSettings) {
            HeightSettingItem(
                currentHeight = userSettingsViewData.height,
                heightInCentimeters = userSettingsViewData.heightInCentimeters,
                measurementUnit = userSettingsViewData.measurementUnit,
                onHeightChanged = onHeightChanged,
                modifier = Modifier.fillMaxWidth()
            )
        }

        GenderSettingItem(
            currentGender = userSettingsViewData.gender,
            onGenderChanged = onGenderChanged,
            modifier = Modifier.fillMaxWidth()
        )

        AgeSettingItem(
            currentBirthDate = userSettingsViewData.birthDate,
            onBirthDateChanged = onBirthDateChanged,
            modifier = Modifier.fillMaxWidth()
        )

        if (showHeightAndHeartRateSettings) {
            MaxHeartRateSettingItem(
                currentMaxHeartRate = userSettingsViewData.maxHeartRate,
                onMaxHeartRateChanged = onMaxHeartRateChanged,
                modifier = Modifier.fillMaxWidth()
            )

            RestHeartRateSettingItem(
                currentRestHeartRate = userSettingsViewData.restHeartRate,
                onRestHeartRateChanged = onRestHeartRateChanged,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun RealNameItem(
    realName: String,
    onClickRealNameItem: () -> Unit,
    modifier: Modifier = Modifier,
) {
    InfoItemText(
        label = stringResource(R.string.profileRealName),
        value = realName,
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = {
            onClickRealNameItem()
        },
        modifier = modifier,
    )
}

@Composable
private fun BioItem(bio: String?, onClickBioItem: () -> Unit, modifier: Modifier = Modifier) {
    InfoItemText(
        label = stringResource(R.string.user_settings_bio),
        value = if (bio.isNullOrEmpty()) stringResource(R.string.profile_description_default) else bio,
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = onClickBioItem,
        modifier = modifier,
    )
}

@Composable
private fun LocationItem(
    locationDisplayName: String,
    onClickLocationItem: () -> Unit,
    modifier: Modifier = Modifier,
) {
    InfoItemText(
        label = stringResource(R.string.location),
        value = locationDisplayName.ifBlank { stringResource(R.string.select_location) },
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = {
            onClickLocationItem()
        },
        modifier = modifier,
    )
}

@Composable
private fun ShowLocationSwitchItem(
    showLocale: Boolean, updateShowLocaleCallback: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    InfoItemSwitch(
        label = stringResource(R.string.user_settings_display_location),
        isChecked = showLocale,
        onCheckedChange = {
            updateShowLocaleCallback.invoke(it)
        },
        modifier = modifier,
    )
}

@Composable
private fun PersonalInfoSection(
    userSettingsViewData: UserSettingsViewData,
    tempCoverPictureFile: File,
    settingTypes: List<SettingItemType>,
    onUpdateCoverPhotoClicked: (Intent) -> Unit,
    onClickBioItem: () -> Unit,
    updateShowLocaleCallback: (Boolean) -> Unit,
    onClickRealNameItem: () -> Unit,
    onClickLocationItem: () -> Unit,
    modifier: Modifier = Modifier,
    snackbarHostState: SnackbarHostState = SnackbarHostState(),
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        settingTypes.forEach {
            when (it) {
                SettingItemType.REAL_NAME -> RealNameItem(
                    userSettingsViewData.name,
                    onClickRealNameItem = onClickRealNameItem
                )

                SettingItemType.USER_NAME -> InfoItemText(
                    isEnabled = false,
                    label = stringResource(R.string.user_settings_username),
                    value = userSettingsViewData.username,
                    valueColor = MaterialTheme.colorScheme.onSurface,
                    onClick = {},
                )

                SettingItemType.LOCATION -> LocationItem(
                    userSettingsViewData.location,
                    onClickLocationItem = onClickLocationItem
                )

                SettingItemType.LOCATION_SWITCH -> if (userSettingsViewData.location.isNotBlank()) {
                    ShowLocationSwitchItem(
                        userSettingsViewData.showLocation,
                        updateShowLocaleCallback,
                    )
                }

                SettingItemType.BIO -> BioItem(userSettingsViewData.bio, onClickBioItem)

                SettingItemType.COVER_PHOTO -> CoverPhotoItem(
                    userSettingsViewData.cover,
                    tempCoverPictureFile,
                    onUpdateCoverPhotoClicked,
                    snackbarHostState = snackbarHostState,
                )

                else -> {}
            }
        }
    }
}

@Composable
private fun InfoItemSwitch(
    label: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium)
                .height(56.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
            // Current UI is still M2 style, so we use M2 Switch for now
            androidx.compose.material.Switch(
                checked = isChecked,
                onCheckedChange = onCheckedChange,
                colors = androidx.compose.material.SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    uncheckedThumbColor = MaterialTheme.colorScheme.lightGrey,
                    uncheckedTrackColor = MaterialTheme.colorScheme.secondary,
                ),
            )
        }
        if (showDivider) {
            HorizontalDivider()
        }
    }
}

@Composable
private fun InfoItemCover(
    label: String,
    imageUrl: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
) {
    InfoItem(
        onClick = onClick,
        modifier = modifier,
        label = label,
        showDivider = false,
        endView = {
            val imageData =
                imageUrl.takeIf { it.isNotBlank() } ?: R.drawable.profile_cove_photo_default
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(imageData)
                    .placeholder(MaterialTheme.colorScheme.lightGrey.toArgb().toDrawable())
                    .crossfade(true)
                    .fallback(
                        AppCompatResources.getDrawable(
                            LocalContext.current,
                            R.drawable.profile_cove_photo_default
                        )?.asImage(),
                    ).build(),
                contentDescription = null,
                modifier = Modifier
                    .size(width = 78.dp, height = 40.dp)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.dividerColor,
                        shape = MaterialTheme.shapes.small,
                    )
                    .clip(
                        MaterialTheme.shapes.small
                    ),
                contentScale = ContentScale.Crop,
            )
        })
}

@Composable
private fun CoverPhotoItem(
    coverPhoto: String?,
    tempCoverPictureFile: File,
    onUpdateCoverPhotoClicked: (Intent) -> Unit,
    modifier: Modifier = Modifier,
    snackbarHostState: SnackbarHostState = SnackbarHostState(),
) {
    val onClick = rememberClickProfilePictureHandlerWithSnackbar(
        onUpdateCoverPhotoClicked,
        tempCoverPictureFile,
        CropProfilePictureParams.CLICK_COVER,
        snackbarHostState,
    )
    InfoItemCover(
        label = stringResource(R.string.user_settings_cover_photo),
        imageUrl = coverPhoto ?: "",
        onClick = onClick,
        modifier = modifier,
    )
}

@Composable
private fun ProfileAvatar(
    profileImageUrl: String?,
    tempProfilePictureFile: File,
    onUpdateProfilePictureClicked: (Intent) -> Unit,
    modifier: Modifier = Modifier,
    snackbarHostState: SnackbarHostState = SnackbarHostState(),
) {
    val onClick = rememberClickProfilePictureHandlerWithSnackbar(
        onUpdateProfilePictureClicked,
        tempProfilePictureFile,
        CropProfilePictureParams.CLICK_AVATAR,
        snackbarHostState,
    )

    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.large),
    ) {
        Box(
            modifier = Modifier
                .size(114.dp)
                .padding(MaterialTheme.spacing.xsmall)
                .clickableThrottleFirst(onClick = onClick),
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(profileImageUrl)
                    .crossfade(true)
                    .placeholderWithFallback(
                        LocalContext.current,
                        CR.drawable.ic_default_profile_image_light,
                    ).transformations(CircleCropTransformation()).build(),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .padding(MaterialTheme.spacing.xsmaller),
                contentScale = ContentScale.Crop,
            )
            Surface(
                shape = CircleShape,
                color = MaterialTheme.colorScheme.surface,
                tonalElevation = 0.dp,
                shadowElevation = 4.dp,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 15.dp, end = MaterialTheme.spacing.xsmall),
            ) {
                IconButton(
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    onClick = onClick,
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_svg_edit),
                        tint = Color.Unspecified,
                        contentDescription = stringResource(R.string.back),
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun ScreenBodyPreview() {
    val commonItems = listOf(
        SettingItemType.REAL_NAME,
        SettingItemType.USER_NAME,
        SettingItemType.LOCATION,
        SettingItemType.LOCATION_SWITCH,
        SettingItemType.BIO,
        SettingItemType.COVER_PHOTO,
    )
    M3AppTheme {
        UserSettingsContent(
            userSettingsViewData = UserSettingsViewData(
                username = "User name",
                name = "Real name",
                bio = "Bio",
                location = "China",
                showLocation = true,
                avatar = "",
                cover = "",
                weight = "66 kg",
                height = "168 cm",
                gender = Sex.FEMALE,
                birthDate = System.currentTimeMillis(),
                weightInGrams = 66000,
                heightInCentimeters = 168,
                measurementUnit = MeasurementUnit.METRIC,
                maxHeartRate = 180,
                restHeartRate = 50
            ),
            settingTypes = commonItems,
            tempCoverPictureFile = File(""),
            tempProfilePictureFile = File(""),
            modifier = Modifier.fillMaxSize(),
        )
    }
}

@Preview
@Composable
private fun ProfileAvatarPreview() {
    M3AppTheme {
        ProfileAvatar("", File(""), {})
    }
}

@Preview
@Composable
private fun BioItemPreview() {
    M3AppTheme {
        Column {
            BioItem("bio", {})
            BioItem(
                "Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, ",
                {})
        }
    }
}


