package com.stt.android.home.marketing

import android.annotation.SuppressLint
import android.view.ViewGroup.LayoutParams
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.stt.android.home.marketing.MarketingH5Activity.Companion.JAVASCRIPT_INTERFACE_NAME
import timber.log.Timber

private const val WEB_VIEW_TAG = "marketing_web_view"

@Composable
fun MarketingH5Screen(
    linkUrl: String,
    jsBridge: Any,
    viewModel: MarketingH5ViewModel,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        contentWindowInsets = WindowInsets.navigationBars
    ) { padding ->
        Column(modifier = Modifier.fillMaxSize().padding(padding)) {
            WebViewComponent(linkUrl, viewModel.getBaseData(), jsBridge)
        }
    }
}

@SuppressLint("SetJavaScriptEnabled", "JavascriptInterface")
@Composable
private fun WebViewComponent(
    linkUrl: String,
    baseDataJson: String,
    jsBridge: Any,
    modifier: Modifier = Modifier
) {
    var isLoading by remember { mutableStateOf(true) }
    var webViewState by remember { mutableStateOf<WebView?>(null) }

    Box(modifier = modifier) {
        AndroidView(factory = { context ->
            // https://wbrawner.com/2024/08/28/android-webview-crash-in-jetpack-compose/
            FrameLayout(context).apply {
                addView(WebView(context).apply {
                    tag = WEB_VIEW_TAG
                    // have to add layout params here, otherwise the webview will not be displayed
                    layoutParams =
                        LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                    webViewClient = object : WebViewClient() {
                        override fun onReceivedError(
                            view: WebView?,
                            request: WebResourceRequest?,
                            error: WebResourceError?
                        ) {
                            super.onReceivedError(view, request, error)
                            isLoading = false
                            Timber.w("load marketing h5 failed: ${error?.description}")
                        }

                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            webViewState?.evaluateJavascript("javascript:getMetadata(\'$baseDataJson\')") {
                                // do nothing
                            }
                        }
                    }
                    webChromeClient = object : WebChromeClient() {
                        override fun onProgressChanged(view: WebView?, newProgress: Int) {
                            super.onProgressChanged(view, newProgress)
                            if (newProgress == 100) {
                                isLoading = false
                            }
                        }
                    }
                    clipToOutline = true
                    settings.cacheMode = WebSettings.LOAD_NO_CACHE
                    settings.domStorageEnabled = true
                    settings.javaScriptEnabled = true
                    settings.setSupportZoom(false)
                    settings.displayZoomControls = false
                }
                )
            }
        }, modifier = Modifier.fillMaxSize()) { webViewLayout ->
            webViewState = webViewLayout.findViewWithTag(WEB_VIEW_TAG)
            webViewState?.loadUrl(linkUrl)
        }

        DisposableEffect(webViewState) {
            webViewState?.addJavascriptInterface(jsBridge, JAVASCRIPT_INTERFACE_NAME)
            onDispose {
                webViewState?.removeJavascriptInterface(JAVASCRIPT_INTERFACE_NAME)
            }
        }

        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}
