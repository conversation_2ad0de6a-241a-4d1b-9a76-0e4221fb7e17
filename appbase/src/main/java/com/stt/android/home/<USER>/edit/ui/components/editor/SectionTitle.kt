package com.stt.android.home.dashboardv2.edit.ui.components.editor

import androidx.annotation.StringRes
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold

@Composable
internal fun SectionTitle(
    @StringRes title: Int,
    modifier: Modifier = Modifier,
) {
    Text(
        text = stringResource(title),
        modifier = modifier,
        style = MaterialTheme.typography.bodyLargeBold,
    )
}

@Composable
internal fun SectionSubtitle(
    @StringRes subtitle: Int,
    modifier: Modifier = Modifier,
) {
    Text(
        text = stringResource(subtitle),
        modifier = modifier,
        color = MaterialTheme.colorScheme.darkGrey,
        style = MaterialTheme.typography.body,
    )
}
