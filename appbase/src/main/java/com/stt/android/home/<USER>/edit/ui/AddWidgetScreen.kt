package com.stt.android.home.dashboardv2.edit.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.home.dashboardv2.edit.AddWidgetViewData
import com.stt.android.home.dashboardv2.edit.AddWidgetViewEvent

@Composable
internal fun AddWidgetScreen(
    viewData: AddWidgetViewData,
    onEvent: (event: AddWidgetViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    when (viewData) {
        is AddWidgetViewData.Loading -> AddWidgetLoadingScreen(modifier)
        is AddWidgetViewData.Loaded -> AddWidgetLoadedScreen(viewData, onEvent, modifier)
    }
}

@Composable
private fun AddWidgetLoadingScreen(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.fillMaxSize()) {
        CircularProgressIndicator(
            modifier = Modifier
                .align(Alignment.Center)
                .size(48.dp),
            color = MaterialTheme.colorScheme.primary,
        )
    }
}
