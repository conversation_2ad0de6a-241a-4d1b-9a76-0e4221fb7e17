package com.stt.android.home.settings.v2.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.Sex
import java.time.Instant

@Composable
fun WeightSettingItem(
    currentWeight: String,
    onWeightChanged: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDialog by remember { mutableStateOf(false) }

    SettingItem(
        title = stringResource(R.string.settings_general_user_settings_weight),
        summary = currentWeight,
        onClick = { showDialog = true },
        modifier = modifier
    )

    if (showDialog) {
        WeightInputDialog(
            currentWeight = currentWeight,
            onWeightChanged = onWeightChanged,
            onDismiss = { showDialog = false }
        )
    }
}

@Composable
fun HeightSettingItem(
    currentHeight: String,
    onHeightChanged: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDialog by remember { mutableStateOf(false) }

    SettingItem(
        title = stringResource(R.string.settings_general_user_settings_height),
        summary = currentHeight,
        onClick = { showDialog = true },
        modifier = modifier,
    )

    if (showDialog) {
        HeightInputDialog(
            currentHeightInCentimeters = 0,
            onHeightChanged = onHeightChanged,
            onDismiss = { showDialog = false },
        )
    }
}

@Composable
fun GenderSettingItem(
    currentGender: Sex,
    onGenderChanged: (Sex) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDialog by remember { mutableStateOf(false) }
    
    val genderDisplayName = when (currentGender) {
        Sex.MALE -> stringResource(R.string.settings_general_user_settings_gender_male)
        Sex.FEMALE -> stringResource(R.string.settings_general_user_settings_gender_female)
    }
    
    SettingItem(
        title = stringResource(R.string.settings_general_user_settings_gender),
        summary = genderDisplayName,
        onClick = { showDialog = true },
        modifier = modifier,
    )
    
    if (showDialog) {
        GenderSelectionDialog(
            currentGender = currentGender,
            onGenderChanged = onGenderChanged,
            onDismiss = { showDialog = false },
        )
    }
}

@Composable
fun AgeSettingItem(
    currentBirthDate: Long?,
    onBirthDateChanged: (Long) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDialog by remember { mutableStateOf(false) }
    
    val ageDisplayText = currentBirthDate?.let {
        Instant.ofEpochMilli(it)
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate()
            .year
            .toString()
    }.orEmpty()
    
    SettingItem(
        title = stringResource(R.string.settings_birth_date),
        summary = ageDisplayText,
        onClick = { showDialog = true },
        modifier = modifier
    )
    
    if (showDialog) {
        BirthDatePickerDialog(
            currentBirthDate = currentBirthDate,
            onBirthDateChanged = onBirthDateChanged,
            onDismiss = { showDialog = false }
        )
    }
}

@Composable
private fun SettingItem(
    title: String,
    summary: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    InfoItemText(
        label = title,
        value = summary,
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = onClick,
        modifier = modifier,
    )
}


@Composable
fun InfoItemText(
    label: String,
    value: String,
    valueColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
    isEnabled: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
) {
    InfoItem(
        onClick = onClick,
        modifier = modifier,
        isEnabled = isEnabled,
        label = label,
        showDivider = showDivider,
        endView = {
            Text(
                maxLines = maxLines,
                overflow = TextOverflow.Ellipsis,
                text = value,
                style = MaterialTheme.typography.bodyLarge,
                color = valueColor,
                textAlign = TextAlign.End,
            )
        })
}

@Composable
fun InfoItem(
    label: String,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
    isEnabled: Boolean = true,
    onClick: (() -> Unit)? = null,
    endView: @Composable (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .widthIn(min = 56.dp)
                .alpha(if (isEnabled) 1f else 0.5f)
                .clickableThrottleFirst(enabled = isEnabled, onClick = onClick ?: {})
                .padding(MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Box(
                modifier = Modifier.fillMaxWidth(0.7f),
                contentAlignment = Alignment.CenterEnd,
            ) {
                endView?.invoke()
            }

        }
        if (showDivider) {
            HorizontalDivider()
        }
    }
}
