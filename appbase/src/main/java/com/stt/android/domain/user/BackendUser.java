package com.stt.android.domain.user;

import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.session.DomainUserSession;
import java.util.List;

public class BackendUser {
    private final User user;

    private BackendUser(String key, String username, String website, String city, String country,
        String profileImageUrl, String profileImageKey, String realName,
        UserSession session, Boolean followModel, String description, Long createdDate, Long lastLogin, List<String> roles, String coverImageUrl) {

        DomainUserSession domainUserSession = session != null ? new DomainUserSession(
            session.getSessionKey(),
            session.getWatchKey(),
            session.isConnectedToFacebook(),
            session.getEmailVerificationState()
        ) : null;

        user = User.loggedIn(key, username, domainUserSession, website, city, country, profileImageUrl,
            profileImageKey, realName, followModel, description, createdDate, lastLogin, roles,
            coverImageUrl);
    }

    public User getUser() {
        return user;
    }

    public static class Builder {
        @SerializedName("key")
        private final String key;
        @SerializedName("username")
        private final String username;
        @SerializedName("website")
        private final String website;
        @SerializedName("city")
        private final String city;
        @SerializedName("country")
        private final String country;
        @SerializedName("profileImageUrl")
        private final String profileImageUrl;
        @SerializedName("imageKey")
        private final String profileImageKey;
        @SerializedName("realName")
        private final String realName;
        @SerializedName("followModel")
        private final Boolean followModel;
        @SerializedName("description")
        private final String description;

        @Nullable
        @SerializedName("createdDate")
        private final Long createdDate;

        @Nullable
        @SerializedName("lastLogin")
        private final Long lastLogin;

        @Nullable
        @SerializedName("roles")
        private final List<String> roles;

        @SerializedName("coverImageUrl")
        private final String coverImageUrl;

        private Builder(String key, String username, String website, String city, String country,
            String profileImageUrl, String profileImageKey, String realName,
            Boolean followModel, String description, Long createdDate, @Nullable Long lastLogin,
            @Nullable List<String> roles, String coverImageUrl) {
            this.key = key;
            this.username = username;
            this.website = website;
            this.city = city;
            this.country = country;
            this.profileImageUrl = profileImageUrl;
            this.profileImageKey = profileImageKey;
            this.realName = realName;
            this.followModel = followModel;
            this.description = description;
            this.createdDate = createdDate;
            this.lastLogin = lastLogin;
            this.roles = roles;
            this.coverImageUrl = coverImageUrl;
        }

        public BackendUser build(UserSession session) {
            return new BackendUser(key, username, website, city, country, profileImageUrl,
                profileImageKey, realName, session, followModel, description, createdDate,
                lastLogin, roles, coverImageUrl);
        }
    }
}
