package com.stt.android.domain.user.subscription

import com.stt.android.controllers.SubscriptionItemControllerDataSource
import com.stt.android.domain.subscriptions.ListSubscriptionsUseCase
import com.stt.android.domain.subscriptions.anySubscriptionHasFreeTrialAvailable
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetCurrentPremiumSubscriptionStatusUseCase @Inject constructor(
    private val subscriptionItemDataSource: SubscriptionItemControllerDataSource,
    private val listSubscriptionsUseCase: ListSubscriptionsUseCase
) {
    operator fun invoke(): Flow<CurrentPremiumSubscriptionStatus> {
        return combine(
            subscriptionItemDataSource.validSubscriptionFlow(),
            flow {
                // To keep loads fast, expect StartupSync to have refreshed the subscriptions
                // from remote recently
                emit(listSubscriptionsUseCase.listSubscriptions(localOnly = true))
            }
        ) { userSubscription, availableSubscriptions ->
            CurrentPremiumSubscriptionStatus(
                userSubscription = userSubscription,
                hasFreeTrialAvailable = availableSubscriptions.anySubscriptionHasFreeTrialAvailable()
            )
        }
    }
}
