package com.stt.android.domain.user.workout;

import com.github.mikephil.charting.data.LineData;
import com.stt.android.domain.workouts.WorkoutHeader;

public class RecentWorkoutTrend {
    public final WorkoutHeader currentWorkout;
    public final WorkoutHeader previousWorkout;
    public final LineData duration;
    public final LineData distance;
    public final LineData speed;
    public final LineData pace;
    public final LineData energy;
    public final LineData averageHeartRate;
    public final LineData averageCadence;

    public RecentWorkoutTrend(WorkoutHeader currentWorkout, WorkoutHeader previousWorkout,
                              LineData duration, LineData distance, LineData speed, LineData pace,
                              LineData energy, LineData averageHeartRate, LineData averageCadence) {
        this.currentWorkout = currentWorkout;
        this.previousWorkout = previousWorkout;
        this.duration = duration;
        this.distance = distance;
        this.speed = speed;
        this.pace = pace;
        this.energy = energy;
        this.averageHeartRate = averageHeartRate;
        this.averageCadence = averageCadence;
    }
}
