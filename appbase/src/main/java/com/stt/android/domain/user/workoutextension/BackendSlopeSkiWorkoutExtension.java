package com.stt.android.domain.user.workoutextension;

import androidx.annotation.NonNull;
import com.google.gson.annotations.SerializedName;
import com.stt.android.ski.SlopeSki;
import java.util.ArrayList;
import java.util.List;

public class BackendSlopeSkiWorkoutExtension extends BackendWorkoutExtension {
    public static final String TYPE = "SkiExtension";

    public static class Statistics {
        @SerializedName("numberOfRuns")
        final int numberOfRuns;

        @SerializedName("descentDurationSeconds")
        final long descentDurationSeconds;

        @SerializedName("descentMeters")
        final double descentMeters;

        @SerializedName("descentDistanceMeters")
        final double descentDistanceMeters;

        @SerializedName("maxSpeed")
        final double maxSpeed;

        public Statistics(int numberOfRuns, long descentDurationSeconds, double descentMeters,
            double descentDistanceMeters, double maxSpeed) {
            this.numberOfRuns = numberOfRuns;
            this.descentDurationSeconds = descentDurationSeconds;
            this.descentMeters = descentMeters;
            this.descentDistanceMeters = descentDistanceMeters;
            this.maxSpeed = maxSpeed;
        }

        //no args constructor for GSON
        public Statistics() {
            this.numberOfRuns = 0;
            this.descentDurationSeconds = 0L;
            this.descentMeters = 0.0;
            this.descentDistanceMeters = 0.0;
            this.maxSpeed = 0.0;
        }
    }

    public static class Run {
        @SerializedName("startTimestamp")
        private final long startTimestamp;

        @SerializedName("descentDurationSeconds")
        private final long descentDurationSeconds;

        @SerializedName("descentMeters")
        private final double descentMeters;

        @SerializedName("descentDistanceMeters")
        private final double descentDistanceMeters;

        public Run(long startTimestamp, long descentDurationSeconds, double descentMeters,
            double descentDistanceMeters) {
            this.startTimestamp = startTimestamp;
            this.descentDurationSeconds = descentDurationSeconds;
            this.descentMeters = descentMeters;
            this.descentDistanceMeters = descentDistanceMeters;
        }

        //no args constructor for GSON
        public Run() {
            this.startTimestamp = 0L;
            this.descentDurationSeconds = 0L;
            this.descentMeters = 0.0;
            this.descentDistanceMeters = 0.0;
        }
    }

    @SerializedName("statistics")
    private final Statistics statistics;

    @SerializedName("runs")
    private final List<Run> runs;

    /**
     * No args constructor to make Gson safe. See:https://stackoverflow
     * .com/questions/18645050/is-default-no-args-constructor-mandatory-for-gson
     */
    public BackendSlopeSkiWorkoutExtension() {
        super(TYPE);
        this.runs = new ArrayList<>();
        this.statistics = new Statistics();
    }

    /**
     * Builds SlopeSkiWorkoutExtension from SlopeSki
     * Currently used in Sport-Tracker
     * @param slopeSki
     */
    public BackendSlopeSkiWorkoutExtension(@NonNull SlopeSki slopeSki) {
        super(TYPE);
        this.statistics =
            new Statistics(slopeSki.getTotalRuns(), Math.round(slopeSki.getTotalDescentTimeInSeconds()),
                slopeSki.getTotalDescents(), slopeSki.getTotalDescentDistance(),
                slopeSki.getMaxSkiRunSpeedMetersPerSecond());

        List<SlopeSki.Run> runs = slopeSki.getRuns();
        int count = runs.size();
        this.runs = new ArrayList<>(count);
        for (int i = 0; i < count; ++i) {
            SlopeSki.Run run = runs.get(i);
            this.runs.add(new Run(Math.round(run.getStartTimeInSeconds()), Math.round(run.duration()),
                run.getDescents(), run.getDistance()));
        }
    }

    @NonNull
    @Override
    public SlopeSkiSummary toWorkoutExtension(int workoutId) {
        // TODO adds supports for runs later, not needed now
        return new SlopeSkiSummary(workoutId, statistics.numberOfRuns,
            statistics.descentDurationSeconds * 1000L, statistics.descentMeters,
            statistics.descentDistanceMeters, statistics.maxSpeed);
    }
}
