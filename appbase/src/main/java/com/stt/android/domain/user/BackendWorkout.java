package com.stt.android.domain.user;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import com.google.gson.annotations.SerializedName;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.data.workout.WorkoutMappersKt;
import com.stt.android.domain.user.workout.tss.BackendTSS;
import com.stt.android.domain.user.workout.tss.TSSMappersKt;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.domain.Point;
import com.stt.android.domain.user.workoutextension.BackendWorkoutExtension;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.tag.SuuntoTag;
import com.stt.android.domain.workouts.tss.TSS;
import com.stt.android.remote.workout.RemoteSuuntoTag;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import timber.log.Timber;

public class BackendWorkout {
    @SerializedName(value = "workoutKey")
    private final String key;
    @SerializedName(value = "totalDistance")
    private final double totalDistance;
    @SerializedName(value = "maxSpeed")
    private final double maxSpeed;
    @SerializedName(value = "activityId")
    private final int activityId;
    @SerializedName(value = "avgSpeed")
    private final double avgSpeed;
    @SerializedName(value = "description")
    private final String description;
    @SerializedName(value = "startPosition")
    private final Point startPosition;
    @SerializedName(value = "stopPosition")
    private final Point stopPosition;
    @SerializedName(value = "centerPosition")
    private final Point centerPosition;
    @SerializedName(value = "startTime")
    private final long startTime;
    @SerializedName(value = "stopTime")
    private final long stopTime;
    @SerializedName(value = "totalTime")
    private final double totalTime;
    @SerializedName(value = "energyConsumption")
    private final double energyConsumption;
    @SerializedName(value = "username")
    private final String username;
    @SerializedName(value = "hrdata")
    private final BackendHeartRateData heartRateInfo;
    @SerializedName(value = "cadence")
    private final BackendCadenceData cadenceData;
    @SerializedName(value = "viewCount")
    private final int viewCount;
    @SerializedName(value = "sharingFlags")
    private final int sharingFlags;
    @SerializedName(value = "stepCount")
    private final int stepCount;
    @SerializedName(value = "manuallyAdded")
    private final boolean manuallyCreated;
    @SerializedName(value = "polyline")
    private final String polyline;
    @SerializedName(value = "comments")
    private final List<BackendWorkoutComment> comments;
    @SerializedName(value = "pictureCount")
    private final int pictureCount;
    @Nullable
    @SerializedName(value = "photos")
    private final List<ImageInformation> pictures;
    @Nullable
    @SerializedName(value = "reactions")
    private final List<BackendReactionSummary> reactions;
    @Nullable
    @SerializedName(value = "videos")
    private final List<BackendVideoInformation> videos;
    @Nullable
    @SerializedName(value = "extensions")
    private final List<BackendWorkoutExtension> extensions;
    @SerializedName(value = "totalAscent")
    private final double totalAscent;
    @SerializedName(value = "totalDescent")
    private final double totalDescent;
    @SerializedName(value = "recoveryTime")
    private final long recoveryTime;
    @SerializedName(value = "maxAltitude")
    private final double maxAltitude;
    @SerializedName(value = "minAltitude")
    private final double minAltitude;
    @SerializedName(value = "tss")
    private final BackendTSS tss;
    @SerializedName(value = "tssList")
    private final List<BackendTSS> tssList;
    @Nullable
    @SerializedName(value = "suuntoTags")
    private final List<RemoteSuuntoTag> suuntoTags;

    private BackendWorkout(String key, double totalDistance, double maxSpeed, int activityId,
        double avgSpeed, String description, Point startPosition, Point stopPosition,
        Point centerPosition, long startTime, long stopTime, double totalTime,
        double energyConsumption, String username, BackendHeartRateData heartRateInfo,
        BackendCadenceData cadenceData, int viewCount, int sharingFlags, int stepCount,
        boolean manuallyCreated, String polyline, List<BackendWorkoutComment> comments,
        int pictureCount, List<ImageInformation> pictures, List<BackendReactionSummary> reactions,
        @Nullable List<BackendVideoInformation> videos, List<BackendWorkoutExtension> extensions,
        double totalAscent, double totalDescent, long recoveryTime, double maxAltitude,
        double minAltitude, @Nullable BackendTSS tss, @Nullable List<BackendTSS> tssList, List<RemoteSuuntoTag> suuntoTags) {
        this.key = key;
        this.totalDistance = totalDistance;
        this.maxSpeed = maxSpeed;
        this.activityId = activityId;
        this.avgSpeed = avgSpeed;
        this.description = description;
        this.startPosition = startPosition;
        this.stopPosition = stopPosition;
        this.centerPosition = centerPosition;
        this.startTime = startTime;
        this.stopTime = stopTime;
        this.totalTime = totalTime;
        this.energyConsumption = energyConsumption;
        this.username = username;
        this.heartRateInfo = heartRateInfo;
        this.cadenceData = cadenceData;
        this.viewCount = viewCount;
        this.sharingFlags = sharingFlags;
        this.stepCount = stepCount;
        this.manuallyCreated = manuallyCreated;
        this.polyline = polyline;
        this.comments = comments;
        this.pictureCount = pictureCount;
        this.pictures = pictures;
        this.reactions = reactions;
        this.videos = videos;
        this.extensions = extensions;
        this.totalAscent = totalAscent;
        this.totalDescent = totalDescent;
        this.recoveryTime = recoveryTime;
        this.maxAltitude = maxAltitude;
        this.minAltitude = minAltitude;
        this.tss = tss;
        this.tssList = tssList;
        this.suuntoTags = suuntoTags;
    }

    /**
     * Maps {@link BackendWorkout} to {@link WorkoutHeader}
     * <p><em>This method must be executed on a worker thread.</em></p>
     */
    @WorkerThread
    public WorkoutHeader getWorkoutHeader(WorkoutHeaderController workoutHeaderController) {
        int heartRateAverage = heartRateInfo == null ? 0 : heartRateInfo.getWorkoutAverage();
        double heartRateAvgPercentage =
            heartRateInfo == null ? 0 : heartRateInfo.getWorkoutAveragePercentage();
        int heartRateMax = heartRateInfo == null ? 0 : heartRateInfo.getWorkoutMaximum();
        double heartRateMaxPercentage =
            heartRateInfo == null ? 0 : heartRateInfo.getWorkoutMaximumPercentage();
        double heartRateUserSetMax = heartRateInfo == null ? 0 : heartRateInfo.getAbsoluteMaximum();
        int averageCadence = cadenceData == null ? 0 : cadenceData.getAverage();
        int maxCadence = cadenceData == null ? 0 : cadenceData.getMax();

        // there are cases when the comment / image count and real comments / images return in
        // header from backend don't match
        // we always use the counts from actual comments / images until it's fixed in backend
        int pictureCount =
            pictures != null ? pictures.size() : this.pictureCount >= 0 ? this.pictureCount : 0;
        int commentCount = comments != null ? comments.size() : 0;
        int reactionCount = 0;
        if (reactions != null) {
            for (BackendReactionSummary reaction : reactions) {
                reactionCount += reaction.getCount();
            }
        }

        List<TSS> mappedTssList = new ArrayList<>();
        if (tssList != null) {
            for (BackendTSS listedTss : tssList) {
                mappedTssList.add(TSSMappersKt.toDomain(listedTss));
            }
        }

        List<SuuntoTag> mappedSuuntoTags = new ArrayList<>();
        if (suuntoTags != null) {
            for (RemoteSuuntoTag remoteSuuntoTag : suuntoTags) {
                mappedSuuntoTags.add(WorkoutMappersKt.toDomain(remoteSuuntoTag));
            }
        }


        return workoutHeaderController.loadExistingLocalWorkoutIdForFetchedWorkout(
            WorkoutHeader.remote(key, totalDistance, maxSpeed, ActivityType.valueOf(activityId),
            avgSpeed, description, startPosition, stopPosition, centerPosition, startTime, stopTime,
            totalTime, energyConsumption, username, heartRateAverage, heartRateAvgPercentage,
            heartRateMax, heartRateMaxPercentage, heartRateUserSetMax, averageCadence, maxCadence,
            pictureCount, viewCount, commentCount, sharingFlags, stepCount, manuallyCreated,
            polyline, reactionCount, totalAscent, totalDescent, recoveryTime, maxAltitude,
            minAltitude, TSSMappersKt.toDomain(tss), mappedTssList, mappedSuuntoTags));
    }

    public List<WorkoutComment> getComments() {
        int commentCount = comments != null ? comments.size() : 0;
        if (commentCount == 0) {
            return Collections.emptyList();
        }

        List<WorkoutComment> workoutComments = new ArrayList<>(commentCount);
        for (int i = 0; i < commentCount; ++i) {
            workoutComments.add(comments.get(i).getWorkoutComment(key));
        }
        return workoutComments;
    }

    public List<ImageInformation> getImages() {
        // just in case backend failed to return consistent data
        int pictureCount = pictures != null ? pictures.size() : 0;
        return pictureCount == 0 ? Collections.<ImageInformation>emptyList() : pictures;
    }

    public List<ReactionSummary> getReactions() {
        int reactionCount = reactions != null ? reactions.size() : 0;
        List<ReactionSummary> reactions = new ArrayList<>(reactionCount);
        for (int i = 0; i < reactionCount; ++i) {
            reactions.add(this.reactions.get(i).toReactionSummary(key));
        }
        return reactions;
    }

    @NonNull
    public List<VideoInformation> getVideos() {
        int videoCount = this.videos != null ? this.videos.size() : 0;
        List<VideoInformation> videos = new ArrayList<>(videoCount);
        for (int i = 0; i < videoCount; ++i) {
            videos.add(this.videos.get(i).toVideoInformation(key));
        }
        return videos;
    }

    @WorkerThread
    @NonNull
    public List<WorkoutExtension> getExtensions(WorkoutHeaderController workoutHeaderController) {
        int count = extensions == null ? 0 : extensions.size();
        if (count == 0) {
            return Collections.emptyList();
        }

        return convertBackendExtensions(getWorkoutHeader(workoutHeaderController).getId(), extensions);
    }

    @Nullable
    public BackendTSS getTss() {
        return tss;
    }

    @NonNull
    public List<BackendTSS> getTssList() {
        return tssList != null ? tssList : Collections.emptyList();
    }

    public static List<WorkoutExtension> convertBackendExtensions(
        int workoutId,
        List<BackendWorkoutExtension> backendExtensions
    ) {
        List<WorkoutExtension> workoutExtensions = new ArrayList<>(backendExtensions.size());
        for (BackendWorkoutExtension backendExtension : backendExtensions) {
            try {
                workoutExtensions.add(backendExtension.toWorkoutExtension(workoutId));
            } catch (BackendWorkoutExtension.UnsupportedExtensionException e) {
                Timber.d("Unsupported extension: %s", e.getMessage());
            }
        }
        return workoutExtensions;
    }

    private static class BackendHeartRateData {
        /**
         * The maximum value the user has set as his/her preference
         */
        @SerializedName(value = "max")
        private final Integer absoluteMaximum;
        /**
         * The maximum value reached during this workout
         */
        @SerializedName(value = "hrmax")
        private final Integer workoutMaximum;
        /**
         * The average value during this workout
         */
        @SerializedName(value = "avg")
        private final Integer workoutAverage;

        private BackendHeartRateData(Integer absoluteMaximum, Integer workoutMaximum,
            Integer workoutAverage) {
            this.absoluteMaximum = absoluteMaximum;
            this.workoutMaximum = workoutMaximum;
            this.workoutAverage = workoutAverage;
        }

        public Integer getWorkoutMaximum() {
            return workoutMaximum;
        }

        public double getWorkoutMaximumPercentage() {
            return absoluteMaximum <= 0 ? 0 : (workoutMaximum / (double) absoluteMaximum) * 100.0;
        }

        public double getWorkoutAveragePercentage() {
            return absoluteMaximum <= 0 ? 0 : (workoutAverage / (double) absoluteMaximum) * 100.0;
        }

        public Integer getWorkoutAverage() {
            return workoutAverage;
        }

        public double getAbsoluteMaximum() {
            return absoluteMaximum;
        }

        @Override
        public String toString() {
            return String.format(Locale.US,
                "[absoluteMaximum=%d; workout maximum=%d; " + "workoutAverage=%d]", absoluteMaximum,
                workoutMaximum, workoutAverage);
        }
    }

    private static class BackendCadenceData {
        @SerializedName(value = "avg")
        private final int average;

        @SerializedName(value = "max")
        private final int max;

        private BackendCadenceData(int average, int max) {
            this.average = average;
            this.max = max;
        }

        public int getAverage() {
            return average;
        }

        public int getMax() {
            return max;
        }
    }
}
