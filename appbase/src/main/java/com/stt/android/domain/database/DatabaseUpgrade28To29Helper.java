package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.workoutextension.FitnessExtension;
import com.stt.android.domain.workouts.extensions.SummaryExtension;
import java.sql.SQLException;

public class DatabaseUpgrade28To29<PERSON><PERSON>per extends DatabaseUpgradeHelper {

    public DatabaseUpgrade28To29Helper(SQLiteDatabase db,
        ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    /**
     * This upgade was made redundant by the next database upgrade. We used to store vo2max to
     * {@link SummaryExtension}. We will store it to {@link FitnessExtension} instead due to backend
     * change.
     */
    @Override
    public void upgrade() throws SQLException {
    }
}
