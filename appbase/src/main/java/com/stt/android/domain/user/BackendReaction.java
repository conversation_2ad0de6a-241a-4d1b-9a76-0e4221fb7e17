package com.stt.android.domain.user;

import androidx.annotation.NonNull;
import com.google.gson.annotations.SerializedName;

public class BackendReaction {
    @SerializedName("id")
    private final String key;

    @SerializedName("utfCode")
    private final String reaction;

    @SerializedName("username")
    private final String userName;

    @SerializedName("realname")
    private final String userRealName;

    @SerializedName("profilePictureUrl")
    private final String userProfilePictureUrl;

    @SerializedName("timestamp")
    private final long timestamp;

    public BackendReaction(String key, String reaction, String userName, String userRealName,
        String userProfilePictureUrl, long timestamp) {
        this.key = key;
        this.reaction = reaction;
        this.userName = userName;
        this.userRealName = userRealName;
        this.userProfilePictureUrl = userProfilePictureUrl;
        this.timestamp = timestamp;
    }

    @NonNull
    public Reaction toReaction(String workoutKey) {
        return Reaction.remote(key, workoutKey, reaction, userName, userRealName,
            userProfilePictureUrl, timestamp);
    }
}
