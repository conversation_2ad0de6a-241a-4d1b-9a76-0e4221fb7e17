package com.stt.android.domain.notifications

import com.stt.android.data.notifications.LocalNotificationsDataSource
import com.stt.android.data.source.local.notifications.LocalNotification
import com.stt.android.remote.notifications.RemoteNotification
import com.stt.android.remote.notifications.RemoteNotificationsDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class NotificationsDataSourceImpl @Inject constructor(
    private val localNotificationsDataSource: LocalNotificationsDataSource,
    private val remoteNotificationsDataSource: RemoteNotificationsDataSource,
) : NotificationsDataSource {
    override suspend fun fetchNotifications(): List<DomainNotification> = remoteNotificationsDataSource
        .fetchNotifications()
        .let(::remoteToDomain)

    override suspend fun replaceNotifications(notifications: List<DomainNotification>) {
        localNotificationsDataSource.replace(
            notifications = notifications.toLocal(),
        )
    }

    override fun readNotifications(): Flow<List<DomainNotification>> =
        localNotificationsDataSource.readAll()
            .map(::localToDomain)

    override fun notificationsCount(): Flow<Int> = localNotificationsDataSource.count()

    override fun unreadNotificationsCount(): Flow<Int> = localNotificationsDataSource.unreadCount()

    override suspend fun markNotificationsAsRead(notifications: List<DomainNotification>) {
        val notificationIds = notifications.mapNotNull { notification ->
            notification.id.takeUnless { notification.read }
        }.takeUnless(List<*>::isEmpty)
            ?: return

        remoteNotificationsDataSource.markNotificationsAsRead(
            notificationIds = notificationIds,
        )

        localNotificationsDataSource.insertOrUpdate(
            notifications = notifications
                .map { it.copy(read = true) }
                .toLocal(),
        )
    }

    private companion object {
        fun remoteToDomain(remote: List<RemoteNotification>): List<DomainNotification> =
            remote.map { it.toDomain() }

        private fun RemoteNotification.toDomain(): DomainNotification = DomainNotification(
            id = id,
            appUrl = appUrl,
            text = text,
            timestamp = timestamp,
            imageUrl = imageUrl,
            read = read,
            type = type,
        )

        fun List<DomainNotification>.toLocal(): List<LocalNotification> = map { it.toLocal() }

        private fun DomainNotification.toLocal(): LocalNotification = LocalNotification(
            id = id,
            appUrl = appUrl,
            text = text,
            timestamp = timestamp,
            imageUrl = imageUrl,
            read = read,
            type = type,
        )

        fun localToDomain(local: List<LocalNotification>): List<DomainNotification> =
            local.map { it.toDomain() }

        private fun LocalNotification.toDomain(): DomainNotification = DomainNotification(
            id = id,
            appUrl = appUrl,
            text = text,
            timestamp = timestamp,
            imageUrl = imageUrl,
            read = read,
            type = type,
        )
    }
}
