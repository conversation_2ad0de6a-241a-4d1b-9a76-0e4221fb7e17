package com.stt.android.domain.database;

import com.j256.ormlite.android.apptools.OrmLiteConfigUtil;

/**
 * How to run this file (http://stackoverflow.com/a/30222996):
 * <ul>
 * <li>In the Android Studio project navigation panel, right-click on the DatabaseConfigUtil.java
 * and select "Run" (the option with the green arrow). If you don't have a Run Configuration
 * created, it will create one for you.</li>
 * <li>edit the configuration (menu Run->Edit Configurations)</li>
 * <li>Configure the Working directory to be "$MODULE_DIR$/src/main"</li>
 * <li>In "Before launch", remove any entry</li>
 * </ul>
 *
 * NOTE: If you see a ClassNotFoundException then you must make the project before (menu Build ->
 * Make Project)
 */
public class DatabaseConfigUtil extends OrmLiteConfigUtil {
    public static void main(String[] args) throws Exception {
        writeConfigFile("ormlite_config.txt");
    }
}