package com.stt.android.domain.user;

public class UserWorkoutSummary {
    private final int totalWorkouts;
    private final double totalDistance;
    private final double totalDuration;
    private final double totalEnergy;

    /**
     * @param totalWorkouts
     * @param totalDistance in meters
     * @param totalDuration in seconds
     * @param totalEnergy
     */
    public UserWorkoutSummary(int totalWorkouts, double totalDistance, double totalDuration, double totalEnergy) {
        this.totalWorkouts = totalWorkouts;
        this.totalEnergy = totalEnergy;
        this.totalDistance = totalDistance;
        this.totalDuration = totalDuration;
    }

    /**
     * @return the totalWorkouts
     */
    public int getTotalWorkouts() {
        return totalWorkouts;
    }

    /**
     * @return the totalDistance in meters
     */
    public double getTotalDistance() {
        return totalDistance;
    }

    /**
     * @return the totalDuration in seconds
     */
    public double getTotalDuration() {
        return totalDuration;
    }

    /**
     * @return the totalEnergy
     */
    public double getTotalEnergyKCal() {
        return totalEnergy;
    }
}
