package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import java.sql.SQLException

class DatabaseUpgrade38To39Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        db.execSQL("DROP TABLE IF EXISTS routes")
        db.execSQL("DROP INDEX IF EXISTS routes_key_idx")
    }
}
