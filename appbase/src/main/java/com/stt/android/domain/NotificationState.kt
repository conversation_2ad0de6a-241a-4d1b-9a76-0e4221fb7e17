package com.stt.android.domain

/**
 * NotificationState contains the current state for notification and marketing inbox feed
 * @param notificationsEmpty Is the notifications list empty
 * @param marketingEmpty Is the marketing inbox empty
 * @param unreadItemCount Combined unread item count for notifications and marketing inbox
 */
data class NotificationState(
    val notificationsEmpty: Boolean = false,
    val marketingEmpty: <PERSON>olean = true,
    val unreadItemCount: Int = 0,
) {
    companion object {
        val DEFAULT = NotificationState()
    }
}
