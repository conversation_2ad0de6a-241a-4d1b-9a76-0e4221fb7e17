<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="com.stt.android.home.settings.deleteaccount.DeleteAccountViewModel"/>
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none"
        android:background="@color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/delete_account_appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="@dimen/elevation_toolbar"
                app:layout_constraintTop_toTopOf="parent">

                <com.stt.android.ui.components.CenteredToolbar
                    android:id="@+id/deleteAccountToolbar"
                    style="@style/Toolbar.Native"
                    app:logo="@drawable/app_logo_small"
                    app:theme="@style/Toolbar.Native" />
            </com.google.android.material.appbar.AppBarLayout>

            <TextView
                android:id="@+id/delete_account_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_icon_large"
                android:gravity="center"
                android:text="@string/account_settings_delete_account_title"
                app:layout_constraintTop_toBottomOf="@id/delete_account_appbar"
                tools:text="@string/account_settings_delete_account_title"
                style="@style/Body.Larger.Bold"/>

            <TextView
                android:id="@+id/delete_account_info_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_large"
                android:layout_marginStart="@dimen/size_spacing_large"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:breakStrategy="balanced"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="@{@string/account_settings_delete_account_intro_new(viewModel.emailOrPhoneNumber)}"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.GONE : View.VISIBLE}"
                app:layout_constraintTop_toBottomOf="@id/delete_account_title"
                tools:text="@string/account_settings_delete_account_intro_new"
                style="@style/Body.Larger"/>

            <TextView
                android:id="@+id/delete_account_info_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_large"
                android:layout_marginStart="@dimen/size_spacing_large"
                android:layout_marginTop="@dimen/size_spacing_large"
                android:gravity="center"
                android:text="@{@string/account_settings_delete_account_tips}"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.GONE : View.VISIBLE}"
                app:layout_constraintTop_toBottomOf="@id/delete_account_info_text"
                tools:text="@string/account_settings_delete_account_tips"
                style="@style/Body.Larger.Bold"/>

            <Button
                android:id="@+id/delete_account_send_link_button"
                android:layout_width="0dp"
                android:layout_height="@dimen/height_button"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_large"
                android:enabled="@{!(safeUnbox(viewModel.callInProgress) || safeUnbox(viewModel.emailSent))}"
                android:onClick="@{() -> viewModel.onSendDeleteLinkButtonClicked()}"
                android:text="@string/account_settings_delete_account_send_link_button"
                android:textAllCaps="true"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.GONE : View.VISIBLE}"
                app:layout_constraintTop_toBottomOf="@id/delete_account_info_tips"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                style="@style/Button.RoundedSecondaryAccent"/>

            <Button
                android:id="@+id/delete_contact_support_button"
                android:layout_width="0dp"
                android:layout_height="@dimen/height_button"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:layout_marginBottom="@dimen/size_spacing_small"
                android:enabled="@{!safeUnbox(viewModel.callInProgress)}"
                android:gravity="center"
                android:onClick="@{() -> viewModel.onContactSupportButtonClicked()}"
                android:text="@string/contact_support"
                android:textAllCaps="true"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.GONE : View.VISIBLE}"
                app:layout_constraintTop_toBottomOf="@+id/delete_account_send_link_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                style="@style/Button.Wide.Inverse"/>

            <TextView
                android:id="@+id/delete_account_confirmation_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_large"
                android:layout_marginStart="@dimen/size_spacing_large"
                android:layout_marginTop="@dimen/size_spacing_large"
                android:breakStrategy="balanced"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="@{@string/account_settings_delete_account_confirmation(viewModel.emailOrPhoneNumber)}"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.VISIBLE : View.GONE}"
                app:layout_constraintTop_toBottomOf="@id/delete_contact_support_button"
                tools:text="@string/account_settings_delete_account_confirmation"
                style="@style/Body.Larger"/>

            <TextView
                android:id="@+id/delete_account_cannot_be_undone_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_large"
                android:layout_marginStart="@dimen/size_spacing_large"
                android:layout_marginTop="@dimen/size_spacing_large"
                android:breakStrategy="balanced"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="@string/account_settings_delete_account_cannot_be_undone"
                android:visibility="@{safeUnbox(viewModel.emailSent) ? View.VISIBLE : View.GONE}"
                app:layout_constraintTop_toBottomOf="@id/delete_account_confirmation_text"
                style="@style/Body.Larger.Black"/>

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="@{safeUnbox(viewModel.callInProgress) ? View.VISIBLE : View.GONE}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/delete_account_info_text"
                app:layout_constraintBottom_toBottomOf="@id/delete_account_info_text"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</layout>
