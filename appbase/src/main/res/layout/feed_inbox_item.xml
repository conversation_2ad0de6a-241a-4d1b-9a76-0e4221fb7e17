<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.stt.android.social.notifications.list.MarketingInboxItem" />
    </data>

    <LinearLayout
        style="@style/ClickableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/spacing_medium"
        android:gravity="center_vertical"
        android:padding="@dimen/size_spacing_medium"
        android:showDividers="middle">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/logo_marketing_inbox_link" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                style="@style/Body.Larger.Bold"
                android:text="@string/marketing_inbox_title" />

            <TextView
                style="@style/Body.Medium"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{item.title}"
                android:visibility="@{item.title != null ? View.VISIBLE : View.GONE}"
                tools:text="Title" />

            <TextView
                style="@style/Body.Medium"
                android:text="@{item.dateTime}"
                android:textColor="@color/dark_gray"
                android:visibility="@{item.dateTime != null ? View.VISIBLE : View.GONE}"
                tools:text="Just now" />

        </LinearLayout>

        <TextView
            style="@style/NotificationCounterCircle"
            android:layout_width="wrap_content"
            android:text="@{Integer.toString(item.unreadCount)}"
            android:visibility="@{item.unreadCount > 0 ? View.VISIBLE : View.GONE}"
            tools:text="2" />

    </LinearLayout>

</layout>
