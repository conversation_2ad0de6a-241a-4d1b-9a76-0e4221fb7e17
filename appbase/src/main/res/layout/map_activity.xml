<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:map="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/map_activity_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:theme="@style/WhiteTheme">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/map"
            android:name="com.stt.android.maps.SuuntoSupportMapFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            map:uiAttribution="@bool/maps_logo_enabled"
            map:uiCompass="true"
            map:uiLogo="@bool/maps_logo_enabled"
            map:uiRotateGestures="true"
            map:uiScrollGestures="true"
            map:uiTiltGestures="true"
            map:uiZoomControls="true"
            map:uiZoomGestures="true" />

        <com.stt.android.ui.components.WorkoutSnapshotView
            android:id="@+id/workoutSnapshotView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:layout_marginEnd="@dimen/size_spacing_xsmall"
            android:layout_marginBottom="@dimen/size_spacing_small"
            android:background="@drawable/rounded_box"
            android:gravity="center_horizontal"
            android:padding="@dimen/size_spacing_small"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/locationBt"
            style="@style/Fab.Small"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:contentDescription="@null"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/workoutSnapshotView"
            app:srcCompat="@drawable/ic_location_arrow"
            tools:visibility="visible" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/mapOptionsBt"
            style="@style/Fab.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:contentDescription="@null"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/locationBt"
            app:srcCompat="@drawable/ic_map_layer" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/avalanche_info"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/locationBt"
            app:layout_constraintTop_toBottomOf="@+id/workoutSnapshotView"
            tools:visibility="visible"/>

        <ProgressBar
            android:id="@+id/loadingSpinner"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/bottomMargin"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/noWorkoutData"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/top_nav_alone_selected"
            android:gravity="center"
            android:padding="@dimen/size_spacing_small"
            android:text="@string/no_workout_data"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/credit"
            style="@style/Body.Small.Gray"
            android:layout_marginStart="@dimen/leftMargin"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
