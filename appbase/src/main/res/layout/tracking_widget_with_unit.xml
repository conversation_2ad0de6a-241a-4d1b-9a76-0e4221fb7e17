<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/background"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:theme="@style/WhiteTheme">

    <TextView
        android:id="@+id/label"
        style="@style/HeaderLabel.Small.Widget"
        android:gravity="center_vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="ALTITUDE" />

    <TextView
        android:id="@+id/value"
        style="@style/Datalabel.Xlarge"
        android:layout_marginBottom="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="@id/label"
        app:layout_constraintTop_toBottomOf="@id/label"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="12.34" />

    <TextView
        android:id="@+id/unit"
        style="@style/Body.Larger"
        android:layout_marginStart="@dimen/d4h"
        android:drawablePadding="@dimen/d4h"
        app:layout_constraintBaseline_toBaselineOf="@id/value"
        app:layout_constraintLeft_toRightOf="@id/value"
        tools:text="m" />

    <View
        android:id="@+id/bottomDivider"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
