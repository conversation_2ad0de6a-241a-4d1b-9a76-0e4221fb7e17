<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraint_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_height="80dp"
    tools:layout_width="100dp">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="@dimen/size_spacing_xxlarge"/>

    <View
        android:id="@+id/vVerticalSeparator"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="@color/very_light_gray"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <View
        android:id="@+id/vHorizontalSeparator"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/very_light_gray"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/label"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/triangleTopRightCorner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/triangle_top_right_blue"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginTop="@dimen/padding"
        android:tint="?android:textColorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:srcCompat="@drawable/ic_feeling_average"
        tools:ignore="ContentDescription"
        tools:visibility="gone"/>

    <TextView
        android:id="@+id/valueAndUnit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_xsmall"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginTop="@dimen/padding"
        android:includeFontPadding="false"
        android:maxLines="3"
        app:layout_constraintBottom_toBottomOf="@id/guideline"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="5.94 km"
        tools:visibility="visible"
        style="@style/WorkoutDetailsGridValue"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:layout_marginStart="@dimen/padding"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:paddingTop="@dimen/size_spacing_xxsmall"
        android:textSize="@dimen/text_size_small_dp"
        app:autoSizeMaxTextSize="@dimen/text_size_small"
        app:autoSizeMinTextSize="@dimen/text_size_xxsmall"
        app:autoSizeStepGranularity="@dimen/text_size_granularity"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline"
        app:layout_constraintVertical_bias="0"
        tools:ignore="SpUsage"
        tools:text="distance"
        style="@style/Body.Medium"/>

</androidx.constraintlayout.widget.ConstraintLayout>
