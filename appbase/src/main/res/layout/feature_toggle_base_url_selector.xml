<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:theme="@style/ThemeOverlay.Material3">

    <!--        -->

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/backend_switcher"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Switch backend"
        style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu"
        tools:ignore="HardcodedText">

        <AutoCompleteTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:focusable="false"
            tools:ignore="LabelFor" />

    </com.google.android.material.textfield.TextInputLayout>

</FrameLayout>
