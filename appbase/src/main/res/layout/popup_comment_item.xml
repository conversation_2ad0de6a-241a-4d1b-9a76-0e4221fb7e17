<?xml version="1.0" encoding="utf-8"?>
<com.stt.android.workoutdetail.comments.PopupWorkoutCommentView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@+id/userImage"
        android:layout_width="@dimen/size_icon_medium"
        android:layout_height="@dimen/size_icon_medium"
        android:layout_marginBottom="@dimen/padding"
        android:layout_marginStart="@dimen/padding"
        android:layout_marginTop="@dimen/padding"
        android:contentDescription="@null"
        tools:src="@drawable/ic_default_profile_image_light"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding"
        android:layout_marginTop="@dimen/padding"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingEnd="@dimen/padding"
        android:paddingStart="@dimen/padding">

        <TextView
            android:id="@+id/userNameAndComment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="2.5dp"
            android:lineSpacingMultiplier="1"
            android:textAppearance="@style/Body3"
            tools:text="Kalle Laine Joku kommentti" />

        <TextView
            android:id="@+id/timestamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textAppearance="@style/Body.Small"
            tools:text="3 päivää sitten" />

    </LinearLayout>
</com.stt.android.workoutdetail.comments.PopupWorkoutCommentView>

