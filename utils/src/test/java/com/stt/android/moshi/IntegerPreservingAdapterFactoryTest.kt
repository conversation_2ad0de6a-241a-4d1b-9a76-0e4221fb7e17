package com.stt.android.moshi

import com.google.common.truth.Truth.assertThat
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonEncodingException
import com.squareup.moshi.Moshi
import net.javacrumbs.jsonunit.assertj.assertThatJson
import org.junit.Assert.assertThrows
import org.junit.Before
import org.junit.Test

class IntegerPreservingAdapterFactoryTest {
    private lateinit var adapter: JsonAdapter<Any>

    @Before
    fun setup() {
        val moshi = Moshi.Builder()
            .add(IntegerPreservingAdapterFactory())
            .build()

        adapter = moshi.adapter(Any::class.java)
    }

    @Test
    fun `should parse positive integer as Int`() {
        assertThat(adapter.fromJson("123")).isInstanceOf(Integer::class.java)
        assertThat(adapter.fromJson("123")).isEqualTo(123)
    }

    @Test
    fun `should parse negative integer as Int`() {
        assertThat(adapter.fromJson("-7")).isInstanceOf(java.lang.Integer::class.java)
        assertThat(adapter.fromJson("-7")).isEqualTo(-7)
    }

    @Test
    fun `should parse number with decimal point as Double`() {
        assertThat(adapter.fromJson("0.0")).isInstanceOf(java.lang.Double::class.java)
        assertThat(adapter.fromJson("0.0")).isEqualTo(0.0)
    }

    @Test
    fun `should parse number using e-notation as Double`() {
        assertThat(adapter.fromJson("1e3")).isInstanceOf(java.lang.Double::class.java)
        assertThat(adapter.fromJson("1e3")).isEqualTo(1e3)
    }

    @Test
    fun `should throw on malformed integers`() {
        assertThrows(JsonEncodingException::class.java) {
            adapter.fromJson("1-2")
        }
    }

    @Test
    fun `should throw on malformed floating point values`() {
        assertThrows(JsonEncodingException::class.java) {
            adapter.fromJson("1.2.3")
        }
    }

    @Test
    fun `parsing and outputting JSON should produce equivalent JSON to input`() {
        assertThatJson(adapter.toJson(adapter.fromJson(EXAMPLE_SETTINGS_JSON))).isEqualTo(EXAMPLE_SETTINGS_JSON)
    }
}

private val EXAMPLE_SETTINGS_JSON = """
{
  "useCount": "3",
  "hydrationAlertFreqMin": "30.0",
  "intensity": "0",
  "internalData": {
    "intensityValues_W": [200, 300, 400]
  },
  "workOut": {
    "AverageSpeed": {
      "goal": 10.0,
      "actual": 9.4
    }  
  },
  "SensorID": "",
  "Marathon": {
    "Cities": ["Helsinki", "Oslo", "Rome"],
    "City": 0
  }
}
""".trimIndent()
