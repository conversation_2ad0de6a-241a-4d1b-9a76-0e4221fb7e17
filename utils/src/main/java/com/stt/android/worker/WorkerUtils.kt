package com.stt.android.worker

import androidx.work.WorkInfo
import androidx.work.WorkManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber

val workerScheduleMutex = Mutex()

/**
 * We check if at most there is 1 job running and 1 enqueued already, otherwise [doSchedule] is executed.
 */
inline fun WorkManager.ifNotAlreadyScheduled(workTag: String, crossinline doSchedule: () -> Unit) {
    CoroutineScope(NonCancellable).launch {
        kotlin.runCatching {
            workerScheduleMutex.withLock {
                val canRun = (getWorkInfosForUniqueWorkFlow(workTag)
                    .firstOrNull()
                    ?.count {
                        it.state == WorkInfo.State.RUNNING ||
                            it.state == WorkInfo.State.ENQUEUED ||
                            it.state == WorkInfo.State.BLOCKED
                    } ?: 0) < 2
                if (canRun) doSchedule()
            }
        }.onFailure {
            Timber.w(it, "Error checking ifNotAlreadyScheduled for work with tag: $workTag")
        }
    }
}
