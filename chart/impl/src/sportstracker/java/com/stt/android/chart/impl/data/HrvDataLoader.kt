package com.stt.android.chart.impl.data

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import javax.inject.Inject

internal class HrvDataLoader @Inject constructor() {
    fun formatHighlightData(value: Float?): String = throw IllegalStateException("Not supported in Sports Tracker")

    fun loadGoalData(): GoalViewData = throw IllegalStateException("Not supported in Sports Tracker")

    fun loadGoalEditorData(): GoalEditorViewData = throw IllegalStateException("Not supported in Sports Tracker")

    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        dateRange: ClosedRange<LocalDate>,
    ): Flow<ChartData> = throw IllegalStateException("Not supported in Sports Tracker")
}
