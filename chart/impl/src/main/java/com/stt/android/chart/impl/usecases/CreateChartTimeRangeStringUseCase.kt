package com.stt.android.chart.impl.usecases

import android.content.Context
import com.stt.android.R
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.ui.utils.DateFormatter
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.LocalDate
import javax.inject.Inject

internal class CreateChartTimeRangeStringUseCase @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val dateFormatter: DateFormatter,
) {
    operator fun invoke(
        chartGranularity: ChartGranularity,
        chartPageIndex: Int,
        chartPageCount: Int,
    ): String  = when (chartGranularity) {
        ChartGranularity.DAILY -> dateFormatter.formatRelativeDate(
            date = LocalDate.now()
                .minusDays(chartPageCount - chartPageIndex - 1L),
        )
        ChartGranularity.WEEKLY -> when (chartPageIndex) {
            chartPageCount - 1 -> appContext.getString(R.string.this_week)
            chartPageCount - 2 -> appContext.getString(R.string.last_week)
            else -> formatChartTimeRangeString(
                chartGranularity = chartGranularity,
                chartPageIndex = chartPageIndex,
                chartPageCount = chartPageCount,
            )
        }
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.SIX_MONTHS -> formatChartTimeRangeString(
            chartGranularity = chartGranularity,
            chartPageIndex = chartPageIndex,
            chartPageCount = chartPageCount,
        )
        ChartGranularity.MONTHLY -> dateFormatter.formatRelativeMonth(
            date = LocalDate.now()
                .minusMonths(chartPageCount - chartPageIndex - 1L),
        )
        ChartGranularity.YEARLY -> dateFormatter.formatRelativeYear(
            date = LocalDate.now()
                .minusYears(chartPageCount - chartPageIndex - 1L),
        )
        ChartGranularity.EIGHT_YEARS -> {
            val range = calculateDateRangeUseCase(
                chartGranularity = chartGranularity,
                chartPageIndex = chartPageIndex,
                chartPageCount = chartPageCount,
            )
            "${range.start.year} - ${range.endInclusive.year}"
        }
    }

    private fun formatChartTimeRangeString(
        chartGranularity: ChartGranularity,
        chartPageIndex: Int,
        chartPageCount: Int,
    ): String {
        val range = calculateDateRangeUseCase(
            chartGranularity = chartGranularity,
            chartPageIndex = chartPageIndex,
            chartPageCount = chartPageCount,
        )

        return dateFormatter.formatDateRange(range.start, range.endInclusive)
    }
}
