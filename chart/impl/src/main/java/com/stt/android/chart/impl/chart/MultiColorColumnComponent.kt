package com.stt.android.chart.impl.chart

import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.DashPathEffect
import com.patrykandpatrick.vico.core.common.DrawingContext
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.Insets
import com.patrykandpatrick.vico.core.common.component.LineComponent
import com.patrykandpatrick.vico.core.common.component.Shadow
import com.patrykandpatrick.vico.core.common.shape.Shape
import com.stt.android.chart.impl.model.ChartData

class MultiColorColumnComponent(
    fill: Fill = Fill.Black,
    val stackEntries: List<ChartData.GroupStackBarEntry>?,
    val xValue: Long, 
    shape: Shape = Shape.Rectangle,
    thicknessDp: Float = DEFAULT_THICKNESS_DP, 
    margins: Insets = Insets.Zero,
    strokeFill: Fill = Fill.Transparent,
    strokeThicknessDp: Float = 0f,
    shadow: Shadow?,
    val multiColorBarStyle: ChartData.GroupStackBarStyle?,
) : LineComponent(
    fill = fill,
    thicknessDp = multiColorBarStyle?.thicknessDp ?: thicknessDp,
    shape = shape,
    margins = margins,
    strokeFill = strokeFill,
    strokeThicknessDp = strokeThicknessDp,
    shadow = shadow
) {
    private val rectF = RectF()
    private val segmentPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply { style = Paint.Style.FILL }
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
    }
    private val borderPath = Path()

    private val uniqueXValues = stackEntries?.map { it.x }?.distinct()?.sorted() ?: listOf(xValue)

    private var currentDrawIndex = 0

    private val drawnXValues = mutableSetOf<Long>()

    init {
        instances.add(this)
    }

    fun resetDrawState() {
        currentDrawIndex = 0
        drawnXValues.clear()
    }

    private fun inferXValue(): Long {
        if (uniqueXValues.size <= 1) return xValue

        if (currentDrawIndex >= uniqueXValues.size) {
            currentDrawIndex = 0 
        }

        val inferredValue = uniqueXValues[currentDrawIndex]

        currentDrawIndex++

        return inferredValue
    }

  override fun draw(
    context: DrawingContext,
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
  ) {
    val actualXValue = inferXValue()

    val contentLeft = left

    val hasCustomRounding = multiColorBarStyle != null && (
      (multiColorBarStyle.cornerRadiusTopLeftDp ?: DEFAULT_CORNER_RADIUS_DP) > 0f ||
        (multiColorBarStyle.cornerRadiusTopRightDp ?: DEFAULT_CORNER_RADIUS_DP) > 0f ||
        (multiColorBarStyle.cornerRadiusBottomLeftDp ?: DEFAULT_CORNER_RADIUS_DP) > 0f ||
        (multiColorBarStyle.cornerRadiusBottomRightDp ?: DEFAULT_CORNER_RADIUS_DP) > 0f
      )

    val totalHeight = bottom - top

    if (stackEntries.isNullOrEmpty()) {
      super.draw(context, contentLeft, top, right, bottom)
      return
    }

    val currentStackEntries = stackEntries.filter { it.x == actualXValue }

    if (currentStackEntries.isEmpty()) {
      super.draw(context, contentLeft, top, right, bottom)
      return
    }

    val totalY = currentStackEntries.sumOf { it.y.toDouble() }.toFloat()

    val path = Path()
    val segmentRectF = RectF()

    var currentTop = bottom

    currentStackEntries.forEachIndexed { index, entry ->
      val segmentRatio = if (totalY > 0) entry.y.toFloat() / totalY else 0f
      val segmentHeight = totalHeight * segmentRatio
      val segmentTop = currentTop - segmentHeight

      segmentRectF.set(contentLeft, segmentTop, right, currentTop)

      segmentPaint.color = entry.color

      if (hasCustomRounding) {
        path.reset()

        val isBottomSegment = index == 0 
        val isTopSegment = index == currentStackEntries.size - 1 

        val topLeft = if (isTopSegment)
          context.dpToPx(
            multiColorBarStyle!!.cornerRadiusTopLeftDp ?: DEFAULT_CORNER_RADIUS_DP
          ) else 0f
        val topRight = if (isTopSegment)
          context.dpToPx(
            multiColorBarStyle!!.cornerRadiusTopRightDp ?: DEFAULT_CORNER_RADIUS_DP
          ) else 0f
        val bottomLeft = if (isBottomSegment)
          context.dpToPx(
            multiColorBarStyle!!.cornerRadiusBottomLeftDp ?: DEFAULT_CORNER_RADIUS_DP
          ) else 0f
        val bottomRight = if (isBottomSegment)
          context.dpToPx(
            multiColorBarStyle!!.cornerRadiusBottomRightDp ?: DEFAULT_CORNER_RADIUS_DP
          ) else 0f

        val radii = floatArrayOf(
          topLeft, topLeft,           
          topRight, topRight,         
          bottomRight, bottomRight,   
          bottomLeft, bottomLeft      
        )

        path.addRoundRect(segmentRectF, radii, Path.Direction.CW)

        context.canvas.drawPath(path, segmentPaint)
      } else {
        context.canvas.drawRect(segmentRectF, segmentPaint)
      }

      currentTop = segmentTop
    }

    multiColorBarStyle?.let { style ->
      val strokeColor = style.strokeColor

      if (strokeColor != null) {
        val topLeft = context.dpToPx(style.cornerRadiusTopLeftDp ?: DEFAULT_CORNER_RADIUS_DP)
        val topRight = context.dpToPx(style.cornerRadiusTopRightDp ?: DEFAULT_CORNER_RADIUS_DP)
        val bottomLeft =
          context.dpToPx(style.cornerRadiusBottomLeftDp ?: DEFAULT_CORNER_RADIUS_DP)
        val bottomRight =
          context.dpToPx(style.cornerRadiusBottomRightDp ?: DEFAULT_CORNER_RADIUS_DP)

        borderPaint.color = strokeColor
        borderPaint.strokeWidth = context.dpToPx(style.strokeWidthDp ?: DEFAULT_STROKE_WIDTH_DP)
        borderPaint.style = Paint.Style.STROKE

        val strokeDashLength = style.strokeDashLengthDp ?: DEFAULT_STROKE_DASH_LENGTH_DP
        if (strokeDashLength > 0f) {
          val dashLength = context.dpToPx(strokeDashLength)
          val dashGap = context.dpToPx(style.strokeGapLengthDp ?: DEFAULT_STROKE_GAP_LENGTH_DP)
          borderPaint.pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
        } else {
          borderPaint.pathEffect = null
        }

        val strokeHalfWidth = context.dpToPx(style.strokeWidthDp ?: DEFAULT_STROKE_WIDTH_DP) / 2
        val strokeLeft = left + strokeHalfWidth
        val strokeTop = top + strokeHalfWidth
        val strokeRight = right - strokeHalfWidth
        val strokeBottom = bottom - strokeHalfWidth

        rectF.set(strokeLeft, strokeTop, strokeRight, strokeBottom)

        borderPath.reset()

        val radii = floatArrayOf(
          topLeft, topLeft,           
          topRight, topRight,         
          bottomRight, bottomRight,   
          bottomLeft, bottomLeft      
        )

        borderPath.addRoundRect(rectF, radii, Path.Direction.CW)

        context.canvas.drawPath(borderPath, borderPaint)
      }
    }
  }

    companion object {
        const val DEFAULT_THICKNESS_DP = 16f
        const val DEFAULT_STROKE_WIDTH_DP = 1f
        const val DEFAULT_STROKE_DASH_LENGTH_DP = 0f
        const val DEFAULT_STROKE_GAP_LENGTH_DP = 0f
        const val DEFAULT_CORNER_RADIUS_DP = 0f

        private val instances = mutableListOf<MultiColorColumnComponent>()

        fun resetAllInstances() {
            instances.forEach { it.resetDrawState() }
        }
    }
}
