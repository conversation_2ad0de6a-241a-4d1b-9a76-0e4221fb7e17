<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="chart_content_sleep">Сон</string>
    <string name="chart_content_steps">шагов</string>
    <string name="chart_content_calories">Калории</string>
    <string name="chart_content_duration">Длительность</string>
    <string name="chart_content_ascent">Подъем</string>
    <string name="chart_content_heart_rate">ЧСС</string>
    <string name="chart_content_minimum_heart_rate">Ми<PERSON>. <PERSON><PERSON><PERSON> (день)</string>
    <string name="chart_content_sleep_minimum_heart_rate">Ми<PERSON>. ЧСС (сон)</string>
    <string name="chart_content_resting_heart_rate">ЧСС в покое</string>
    <string name="chart_content_resources">Ресурсы</string>
    <string name="chart_content_hrv">HRV</string>
    <string name="chart_content_vo2max">VO2Max</string>

    <!-- Resource states -->
    <string name="resources_state_active">Активн.</string>
    <string name="resources_state_inactive">Неактивн.</string>
    <string name="resources_state_stressed">Нагрузка</string>
    <string name="resources_state_recovering">Восстан.</string>

    <string name="resources_state_active_avg">Ср. в активном</string>
    <string name="resources_state_inactive_avg">Ср. в покое</string>
    <string name="resources_state_stressed_avg">Ср. в стрессе</string>
    <string name="resources_state_recovering_avg">Ср. Восстан.</string>

    <!-- Resource levels -->
    <string name="resources_level_low">Низк.</string>
    <string name="resources_level_moderate">Умеренн.</string>
    <string name="resources_level_high">Выс.</string>
    <string name="resources_level_very_high">Оч. выс.</string>

    <string name="chart_value_total">Итого</string>
    <string name="chart_value_total_co2e_saved">Сокращение выброса CO₂e</string>
    <string name="chart_value_daily_avg">Ср. за день</string>

    <string name="chart_value_range">Диапазон</string>
    <string name="chart_value_min_daytime_hr">Мин. ЧСС (день)</string>
    <string name="chart_value_avg_min_daytime_hr">Ср. мин. ЧСС (день)</string>
    <string name="chart_value_min_sleep_hr">Мин. ЧСС (сон)</string>
    <string name="chart_value_avg_sleep_hr">Ср. мин. ЧСС (сон)</string>
    <string name="chart_value_resting_hr">ЧСС в покое</string>
    <string name="chart_value_avg_resting_hr">Ср. ЧСС в покое</string>
    <string name="chart_value_active">Активн.</string>
    <string name="chart_value_avg">Ср.</string>

    <string name="chart_daily_target">Цель за день</string>
    <string name="chart_daily_step_target">Цель шагов за день</string>
    <string name="chart_daily_calorie_target">Цель по калориям в день</string>
    <string name="chart_daily_sleep_target">Цель сна за день</string>

    <string name="chart_comparison_compare">Сравн.</string>
    <string name="chart_comparison_hide_comparison">Скрыть сравнение</string>

    <string name="chart_granularity_daily_abbreviation">Д</string>
    <string name="chart_granularity_weekly_abbreviation">Вт</string>
    <string name="chart_granularity_seven_days_abbreviation">7D</string>
    <string name="chart_granularity_monthly_abbreviation">М</string>
    <string name="chart_granularity_thirty_days_abbreviation">30D</string>
    <string name="chart_granularity_six_weeks_abbreviation">6 Н</string>
    <string name="chart_granularity_six_months_abbreviation">6M</string>
    <string name="chart_granularity_yearly_abbreviation">Г</string>
    <string name="chart_granularity_eight_years_abbreviation">8Y</string>
    <string name="chart_granularity_sixty_day_days_abbreviation">60D</string>
    <string name="chart_granularity_three_hundred_sixty_five_days_abbreviation">365D</string>
    <string name="chart_granularity_one_hundred_eight_days_abbreviation">180D</string>

    <string name="chart_granularity_seven_days">Посл. 7 дней</string>
    <string name="chart_granularity_thirty_days">Посл. 30 дней</string>
    <string name="chart_granularity_six_weeks">6 недель</string>
    <string name="chart_granularity_six_months">6 месяцев</string>
    <string name="chart_granularity_one_year">1 год</string>
    <string name="chart_granularity_eight_years">8 лет</string>

    <string name="chart_granularity_daily_interval">Интервал за день</string>
    <string name="chart_granularity_weekly_interval">Интервал за неделю</string>
    <string name="chart_granularity_monthly_interval">Интервал за месяц</string>
    <string name="chart_granularity_yearly_interval">Интервал за год</string>

    <string name="chart_granularity_more">Подробнее</string>
    <string name="chart_granularity_time_range_title">Временной интервал</string>
    <string name="chart_granularity_time_range_desc">Выберите временное окно для анализа</string>

    <string name="calories_bmr">BMR</string>
    <string name="total_calories">Всего калорий</string>
    <string name="about_calories">Больше о калориях</string>
    <string name="calories_instruction">Общее количество калорий отражает энергию, потребляемую за день, включая базальный метаболизм (BMR) и активные калории. BMR — это минимальное количество энергии, расходуемой основными физиологическими потребностями, такими как дыхание, сердцебиение и регуляция температуры организма, в состоянии покоя. Активные калории означают энергию, которую вы тратите на повседневную активность и упражнения. Увеличение BMR происходит за счет увеличения мышечной массы, регулярных тренировок, потребления достаточного количества жидкости и здорового режима сна.</string>
    <string name="workout_sessions_title">Сеансы с упражнениями</string>
    <string name="workout_filter_all">Все</string>

    <string name="about_minimum_hr_title">О минимальной частоте сердцебиения за день</string>
    <string name="about_minimum_hr_description">Минимальная частота сердцебиения за день — это самая низкая частота сердцебиения в состоянии бодрствования, которая обычно выше, чем ЧСС в состоянии сна и покоя. Она отражает общий уровень активности и здоровья сердечно-сосудистой системы, помогая выявлять состояние усталости, стресса и восстановления, когда данные о сне недоступны.</string>
    <string name="about_sleep_minimum_hr_title">О минимальной частоте сердцебиения во сне</string>"
    <string name="about_sleep_minimum_hr_description">Минимальная частота сердцебиения во сне — это минимальная частота сердцебиения, записанная во время вашего сна. Она служит индикатором качества сна и восстановления, и обычно чем меньше ее значение — тем качественнее восстановление.</string>
    <string name="about_heart_rate_title">Больше о частоте сердцебиения</string>"
    <string name="about_heart_rate_description">Частота сердцебиения (ЧСС) означает число сердечных сокращений в минуту и является ключевым индикатором здоровья и физической формы. Понимание диапазонов ЧСС поможет вам оптимизировать тренировочный процесс и свою общую физическую кондицию. Частота сердцебиения варьируется по время сна, упражнений и повседневной активности. ЧСС в состоянии покоя и минимальный пульс во время сна будут ниже, что поможет оценить скорость восстановления и подстроить интенсивность тренировок. Мониторинг повседневной ЧСС поможет сформулировать тренды, обнаружить аномалии и получать общие сведения о вашем состоянии здоровья.</string>

    <string name="about_resting_heart_rate_title">О частоте сердцебиения в состоянии покоя (RHR)</string>
    <string name="about_resting_heart_rate_description">Частота сердцебиения в состоянии покоя (RHR) — это значение ЧСС в состоянии покоя, которое отражает общее состояние здоровья и физическую форму. Низкая RHR обычно означает, что сердце работает эффективно и находится в тонусе. Отслеживание RHR также помогает анализировать темпы восстановления и регулировать баланс между тренировками и отдыхом.</string>
    <string name="about_resources_title">О ресурсах</string>
    <string name="about_resources_description">Ресурсы означают дневные темпы восстановления и расход энергии, чтобы вы могли отслеживать свое физическое состояние и подстраивать уровень активности. Обычно энергия восстанавливается во время сна.</string>

    <!-- HRV instructions -->
    <string name="about_hrv_title">Что такое HRV?</string>
    <string name="about_hrv_description">Вариабельность частоты сердцебиения (HRV) — это мера изменчивости интервалов времени между ударами сердца. Она отражает баланс вегетативной нервной системы (ВНС) и позволяет проводить оценку общего состояния здоровья и уровней стресса. HRV — это ценный инструмент для понимания функции ВНС и поддержки здорового состояния.</string>
    <string name="how_to_read_hrv_data_title">Как следует интерпретировать данные?</string>
    <string name="how_to_read_hrv_data_description">Для получения оптимальной HRV значения должны находиться в пределах нормы, лучше всего — если немного ближе к верхнему пределу. Более высокий показатель HRV обычно считается показателем общего здоровья, однако его всегда стоит рассматривать относительно базового значения. Такие факторы, как расслабление, физическое и психическое истощение, а также болезнь (например, грипп) могут влиять на показатель HRV.</string>
    <string name="how_to_measure_hrv_title">Как измерить HRV?</string>
    <string name="how_to_measure_hrv_description">1. Suunto измеряет вашу HRV, когда вы спите. Для получения данных о HRV необходимо, чтобы часы были надеты во время сна и включена функция отслеживания сна. Вариабельность частоты сердцебиения (HRV) постоянно измеряется во время сна для расчета среднего значения RMSSD за ночь. RMSSD (среднеквадратическое значение последовательных различий) широко используется для оценки HRV.</string>
    <string name="hrv_values_explained_title">2. Объяснения значений</string>
    <string name="hrv_todays_value_explained">а. Значение HRV за сегодня вычисляется из измерений, полученных за последнюю ночь, тогда как значение HRV за вчера относится к предпоследней ночи.</string>
    <string name="hrv_seven_day_average_explained">б. Среднее значение за 7 дней вычисляется на основе измерений HRV за последние 7 ночей. Чтобы узнать среднее значение, за последние 7 дней требуется получить не менее 3 измерений HRV.</string>
    <string name="hrv_normal_range_explained">в. Чтобы определить свою норму, необходимо иметь не менее 14 измерений HRV за период 60 дней.</string>

    <string name="sleep_heart_rate_compare">Частота сердцебиения</string>
    <string name="sleep_heart_rate_hide_comparison">Скрыть ЧСС</string>
    <string name="sleep_heart_rate_min">Мин. %s</string>
    <string name="sleep_heart_rate_max">Макс. %s</string>

    <string name="sleep_stages_title">Этапы</string>
    <string name="sleep_stages_avg_awake">Ср. время бодрствования</string>
    <string name="sleep_stages_avg_rem">Ср. БДГ</string>
    <string name="sleep_stages_light">Светлая</string>
    <string name="sleep_stages_avg_light">Ср. легкий</string>
    <string name="sleep_stages_avg_deep">Ср. глубокий</string>
    <string name="sleep_quality_title">Качество сна</string>
    <string name="sleep_quality_arg_sleep_hr">Средн. ЧСС (сон)</string>
    <string name="sleep_quality_min_sleep_hr">Мин. ЧСС (сон)</string>
    <string name="sleep_quality_arg_sleep_hrv">Ср. HRV (сон)</string>
    <string name="sleep_quality_max_sleep_spo2">Ср. SpO₂ (сон)</string>
    <string name="sleep_quality_max_altitude">Макс. %d</string>
    <string name="sleep_time_duration_of_target">%s из целевого количества</string>
    <string name="sleep_time_duration_of_sleep">%s сон</string>
    <string name="sleep_time_duration_of_nap">%s дремание</string>
    <string name="sleep_wake_up_resources_title">Данные п/пробужд</string>
    <string name="sleep_wake_up_resources_gained_during_sleep">Получено</string>
    <string name="sleep_wake_up_resources_lost_during_sleep">Утрачено</string>
    <string name="sleep_comparison_title">Сравнение</string>
    <string name="sleep_comparison_graph_type_sleep_duration">Время сна</string>
    <string name="sleep_comparison_graph_type_sleep_regularity">Регулярность сна</string>
    <string name="sleep_comparison_graph_type_nap_duration">Время дремания</string>
    <string name="sleep_comparison_graph_type_total_duration">Общее время</string>
    <string name="sleep_comparison_graph_type_blood_oxygen">Ср. SpO₂ (сон)</string>
    <string name="sleep_comparison_graph_type_training">Длительность тренировки</string>
    <string name="sleep_comparison_graph_type_min_hr_during_sleep">Мин. ЧСС (сон)</string>
    <string name="sleep_comparison_graph_type_avg_hr_during_sleep">Средн. ЧСС (сон)</string>
    <string name="sleep_comparison_graph_type_morning_resources">Данные п/пробужд</string>
    <string name="sleep_goal_title">Сон за день: цель</string>

    <string name="heart_rate_stat_resting">ЧСС в покое</string>
    <string name="heart_rate_stat_average">Ср. Пульс</string>
    <string name="heart_rate_stat_min_sleep">Мин. ЧСС (сон)</string>
    <string name="heart_rate_stat_min_daytime">Мин. ЧСС (день)</string>
    <string name="chart_connect_commute">маршрут</string>
    <string name="about_commute">Что такое «Маршрут»?</string>
    <string name="commute_instruction">«Маршрут» означает прохождение пути к месту назначения. Такая активность, как забег, заезд и прогулка на расстояние от 500 м по прямой линии между точкой начала и конца, автоматически считается маршрутом. Мы поддерживаем тех, кто выбирает экологичные варианты, такие как езда на велосипеде и ходьба, поскольку таким образом уменьшается углеродный след, а также повышается качество воздуха и улучшается ваше состояние здоровья. Помимо автоматического присвоения метки «Маршрут» вы можете вручную редактировать записи в журнале активности и добавить метку «Маршрут», а также записать свой вклад в экологию планеты.</string>
    <string name="exercises_counts">упражнения</string>
    <string name="commute_tags_title">Отмечайте маршруты автоматически</string>
    <string name="commute_tags_comment">Предоставьте нам разрешение для автоматического отслеживания всех ваших забегов, заездов и прогулок с расстоянием более 500 м между точкой начала и конца, в качестве маршрутов, а также отслеживайте сокращение выбросов CO₂e.</string>

    <!-- Resource States Instructions -->
    <string name="how_to_read_data">Как использовать эти данные?</string>
    <string name="recovering">Восстан.</string>
    <string name="resources_rise_quickly">Ресурсы быстро восстанавливаются.</string>
    <string name="scenario_prefix">а. Ситуация:</string>
    <string name="recovering_scenario">Глубокое расслабление, в частности качественный сон, означает оптимальное восстановление.</string>

    <string name="inactive">Неактивн.</string>
    <string name="resources_change_slowly">Уровень ресурсов меняется быстро и непредсказуемо.</string>
    <string name="during_sleep_prefix">а. Во время сна:</string>
    <string name="inactive_sleep_desc">Уровень остается стабильным или понемногу повышается (за исключением периодов бодрствования), с медленным восстановлением.</string>
    <string name="during_wakefulness_prefix">б. Во время бодрствования:</string>
    <string name="inactive_wakefulness_desc">Может значительно варьироваться в зависимости от небольшой активности.</string>

    <string name="active">Активн.</string>
    <string name="resources_decline">Уровень ресурсов падает со скоростью, зависящей от интенсивности активности и восстановления.</string>
    <string name="active_scenario">Дневная активность и тренировки, где уровень ресурсов меняется в зависимости от интенсивности активности и времени восстановления.</string>

    <string name="stressed">Нагрузка</string>
    <string name="resources_drop_rapidly">Уровень ресурсов быстро падает.</string>
    <string name="stressed_scenario">Нагрузка во время бодрствования быстро снижает уровень ресурсов, и вы переходите в состояние повышенной нагрузки.</string>

    <string name="how_resources_measured">Как измеряются ресурсы?</string>
    <string name="resources_measured_based">Ресурсы измеряются на основании психологического состояния</string>
    <string name="physiological_state">психологическое состояние.</string>
    <string name="active_state">Активное состояние</string>
    <string name="active_state_desc">Время восстановления после активности отражает ваши потребности в восстановлении; длительное восстановление означает, что ресурсы будут расходоваться быстрее.</string>
    <string name="inactive_state">Неактивное состояние</string>
    <string name="inactive_state_desc">Вариабельность частоты сердцебиения (HRV) определяет автономный баланс. Высокое значение HRV означает, что ресурсов достаточно, низкое — что ресурсов мало. Ключевые метрики HRV, такие как RMSSD, индекс стресса и SDNN, наряду со значением ЧСС помогают определить уровень нагрузки.</string>

    <string name="resource_states_intro">Таблица отображает четыре состоянии на основании изменения ресурсов:</string>

    <string name="chart_connect_training_load">Тренировочная нагрузка</string>
    <string name="about_tss">Что такое балл тренировочного стресса (TSS)?</string>
    <string name="tss_instruction">Балл тренировочного стресса (TSS) — это количественный показатель тренировочной нагрузки, в частности для видов спорта на выносливость, который учитывает интенсивность и длительность. Чем выше этот показатель, тем выше нагрузка на физиологические системы организма. Обычно этот показатель учитывается вместе с частотой сердцебиения и данными о мощности, а интенсивность основывается на аэробном пороге спортсмена — он определяется Suunto на базе ЧСС, темпа и предельной мощности в зонах 4 и 5.</string>

    <string name="vo2_max_fitness_age">Фитнес-возраст</string>
    <string name="vo2_max_six_week_avg">Средний за 6 недель</string>

    <!-- VO2MAX instructions -->
    <string name="about_vo2max_title">Что такое VO₂max?</string>
    <string name="about_vo2max_description">Максимальное потребление кислорода (VO₂max) означает количество кислорода, которое организм может использовать во время интенсивной тренировки; оно выражается в миллиметрах на килограмм в минуту (мл/кг/мин). Это значение отражает здоровье и выносливость сердечно-сосудистой системы — чем выше значение, тем выше результативность. VO₂max зависит от таких факторов, как пол, возраст и натренированность. Повышение VO₂max улучшит ваши спортивные результаты, и Suunto рассчитывает этот показатель на основании ЧСС и скорости.</string>
    <string name="how_to_get_vo2max_title">Как получить значение VO₂max?</string>
    <string name="how_to_get_vo2max_description">Вы можете просмотреть ориентировочное значение VO₂max по анализу данных, собранных во время тренировок по бегу, включая частоту сердцебиения, темп и интенсивность упражнения. Например, бега на открытом воздухе в течение 10 минут будет достаточно для примерной оценки. Также можно запустить тест Купера в SuuntoPlus, чтобы оценить свой ресурс VO₂max.</string>

    <!-- How to use VO2MAX -->
    <string name="how_to_use_vo2max_title">Как использовать данные о VO₂max?</string>
    <string name="how_to_use_vo2max_description">Индикатор здоровья: Высокий показатель VO₂max обычно означает более оптимальное состояние сердечно-сосудистой системы и эффективную транспортировку кислорода организмом. Отслеживание этого показателя позволит узнать больше о здоровье вашего сердца и легких и поможет определить потребность в аэробных тренировках или изменении образа жизни. Сравнение с эталонным значением: Сравнение вашего значения VO₂max с нормой для вашего пола и возраста, а также с вашими предыдущими данными, поможет проанализировать аэробную выносливость. Такая аналитика поможет подстроить план упражнения — к примеру, остаться на текущем уровне нагрузки или повысить его. Длительное измерение VO₂max в течение нескольких месяцев или лет поможет вам отслеживать долгосрочные изменения в вашей физической форме. Своевременное распознавание тренда поможет подстроить объем тренировок и их интенсивность, чтобы продолжать движение к поставленным целям.</string>
</resources>
