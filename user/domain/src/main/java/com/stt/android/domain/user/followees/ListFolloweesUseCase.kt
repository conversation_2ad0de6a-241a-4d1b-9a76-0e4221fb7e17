package com.stt.android.domain.user.followees

import com.stt.android.domain.user.User
import javax.inject.Inject

class ListFolloweesUseCase @Inject constructor(
    private val followeeDataSource: FolloweeDataSource
) {
    suspend fun listFollowees(): List<User> = followeeDataSource.getFolloweesFromLocalStore()

    suspend fun listFolloweeUsernames(): List<String> =
        followeeDataSource.getFolloweeUsernamesFromLocalStore()
}
