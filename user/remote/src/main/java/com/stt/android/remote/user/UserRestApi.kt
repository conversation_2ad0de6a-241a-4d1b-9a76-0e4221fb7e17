package com.stt.android.remote.user

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.Path

interface UserRestApi {
    @GET("user/name/{username}")
    suspend fun fetchUserByUsername(
        @Path("username") username: String
    ): AskoResponse<RemoteUser>

    @GET("gear/{username}/latest")
    suspend fun getGearLatest(@Path("username") username: String): AskoResponse<List<RemoteUserGearLatest>>
}
