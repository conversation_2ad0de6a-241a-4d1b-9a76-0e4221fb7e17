package com.stt.android.remote.reactions

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path

interface ReactionRestApi {
    @GET("workouts/reactions/{workoutKey}")
    suspend fun fetchReactions(@Path("workoutKey") workoutKey: String): List<RemoteReaction>

    @Headers("Content-Type: application/json;charset=UTF-8")
    @POST("workouts/reaction/{workoutKey}")
    suspend fun saveReaction(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Path("workoutKey") workoutKey: String
    ): AskoResponse<String>

    @DELETE("workouts/reaction/{workoutKey}")
    suspend fun deleteReaction(@Path("workoutKey") workoutKey: String)
}
