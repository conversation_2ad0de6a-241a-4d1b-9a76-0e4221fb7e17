package com.stt.android.remote.smlzip

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.exceptions.remote.ClientError
import okio.BufferedSource
import timber.log.Timber
import javax.inject.Inject

class SmlRemoteApi @Inject constructor(
    private val smlRestApi: SmlRestApi,
) {
    /**
     * Fetches the SML for a workout in a compressed form from the backend.
     * @param workoutKey Identifies the workout.
     */
    suspend fun fetchSmlZip(workoutKey: String): BufferedSource? = runSuspendCatching {
        smlRestApi.fetchSmlZip(workoutKey)
            .body()
            ?.source()
    }.getOrElse { e ->
        if (e is ClientError.NotFound) {
            Timber.w("SML not found for workout: $workoutKey")
        } else {
            Timber.w(e, "SML fetch from backend failed.")
        }

        null
    }
}
