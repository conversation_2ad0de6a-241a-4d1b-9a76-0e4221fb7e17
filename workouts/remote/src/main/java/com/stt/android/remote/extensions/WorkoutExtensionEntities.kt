package com.stt.android.remote.extensions

import com.google.gson.annotations.SerializedName
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class ExtensionListResponse(
    @Json(name = "extensions") val extensions: List<RemoteWorkoutExtension>
)

/**
 * Workout extensions data entities
 */
sealed class RemoteWorkoutExtension {
    enum class Type(val value: String) {
        SUMMARY_EXTENSION("SummaryExtension"),
        FITNESS_EXTENSION("FitnessExtension"),
        SKI_EXTENSION("SkiExtension"),
        INTENSITY_EXTENSION("IntensityExtension"),
        DIVE_HEADER_EXTENSION("DiveHeaderExtension"),
        SWIMMING_HEADER_EXTENSION("SwimmingHeaderExtension"),
        WEATHER_EXTENSION("WeatherExtension"),
        JUMP_ROPE_EXTENSION("JumpRopeExtension");

        companion object {
            private val types = values().associateBy { it.value }

            @JvmField
            val textValues = values().map { it.value }
            fun fromValue(value: String): Type = types.getValue(value)
        }
    }

    @JsonClass(generateAdapter = true)
    data class RemoteSummaryExtension(
        @Json(name = "pte") val pte: Float?,
        @Json(name = "feeling") val feeling: Int?,
        @Json(name = "avgTemperature") val avgTemperature: Float?,
        @Json(name = "peakEpoc") val peakEpoc: Float?,
        @Json(name = "avgPower") val avgPower: Float?,
        @Json(name = "avgCadence") val avgCadence: Float?,
        @Json(name = "avgSpeed") val avgSpeed: Float?,
        @Json(name = "ascentTime") val ascentTime: Float?,
        @Json(name = "descentTime") val descentTime: Float?,
        @Json(name = "performanceLevel") val performanceLevel: Float?,
        @Json(name = "recoveryTime") val recoveryTime: Long?,
        @Deprecated("Use value from WorkoutHeader")
        @Json(name = "ascent")
        val ascent: Double?,
        @Deprecated("Use value from WorkoutHeader")
        @Json(name = "descent")
        val descent: Double?,
        @Json(name = "gear") val gear: RemoteSummaryGear?,
        @Json(name = "exerciseId") val exerciseId: String?,
        @Json(name = "apps") val zapps: List<RemoteZapp>?,
        @Json(name = "type") val type: String = Type.SUMMARY_EXTENSION.value,
        @Json(name = "maxCadence") val maxCadence: Float?,
        @Json(name = "repetitionCount") val repetitionCount: Int?,
        @Json(name = "avgStrideLength") val avgStrideLength: Float?,
        @Json(name = "fatConsumption") val fatConsumption: Int?,
        @Json(name = "carbohydrateConsumption") val carbohydrateConsumption: Int?,
        @Json(name = "avgGroundContactTime") val avgGroundContactTime: Float?,
        @Json(name = "avgVerticalOscillation") val avgVerticalOscillation: Float?,
        @Json(name = "avgLeftGroundContactBalance") val avgLeftGroundContactBalance: Float?,
        @Json(name = "avgRightGroundContactBalance") val avgRightGroundContactBalance: Float?,
        @Json(name = "lacticThHr") val lacticThHr: Float?,
        @Json(name = "lacticThPace") val lacticThPace: Float?,
        @Json(name = "avgAscentSpeed") val avgAscentSpeed: Float?,
        @Json(name = "maxAscentSpeed") val maxAscentSpeed: Float?,
        @Json(name = "avgDescentSpeed") val avgDescentSpeed: Float?,
        @Json(name = "maxDescentSpeed") val maxDescentSpeed: Float?,
        @Json(name = "avgDistancePerStroke") val avgDistancePerStroke: Float?,
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteSummaryGear(
        @Json(name = "hardwareVersion") val deviceHardwareVersion: String?,
        @Json(name = "softwareVersion") val deviceSoftwareVersion: String?,
        @Json(name = "productType") val productType: String?,
        @Json(name = "displayName") val displayName: String?,
        @Json(name = "name") val deviceName: String?,
        @Json(name = "serialNumber") val deviceSerialNumber: String?,
        @Json(name = "manufacturer") val deviceManufacturer: String?
    )

    // fixme remove @SerializedName when annotations are no longer necessary

    @JsonClass(generateAdapter = true)
    data class RemoteZapp(
        @Json(name = "Id")
        @SerializedName("Id")
        val id: String?,
        @Json(name = "name")
        @SerializedName("name")
        val name: String,
        @Json(name = "AuthorId")
        @SerializedName("AuthorId")
        val authorId: String?,
        @Json(name = "ExternalId")
        @SerializedName("ExternalId")
        val externalId: String?,
        @Json(name = "summaryOutputs")
        @SerializedName("summaryOutputs")
        val summaryOutputs: List<RemoteSummaryOutput>?,
        @Json(name = "channels")
        @SerializedName("channels")
        val channels: List<RemoteZappChannel>?
    )

    @JsonClass(generateAdapter = true)
    data class RemoteSummaryOutput(
        @Json(name = "id")
        @SerializedName("id")
        val id: String?,
        @Json(name = "format")
        @SerializedName("format")
        val format: String?,
        @Json(name = "postfix")
        @SerializedName("postfix")
        val postfix: String?,
        @Json(name = "name")
        @SerializedName("name")
        val name: String,
        @Json(name = "summaryValue")
        @SerializedName("summaryValue")
        val summaryValue: Double?
    )

    @JsonClass(generateAdapter = true)
    data class RemoteZappChannel(
        @SerializedName("channelId")
        @Json(name = "channelId")
        val channelId: Int,
        @SerializedName("format")
        @Json(name = "format")
        val format: String?,
        @SerializedName("inverted")
        @Json(name = "inverted")
        val inverted: Boolean?,
        @SerializedName("name")
        @Json(name = "name")
        val name: String,
        @SerializedName("variableId")
        @Json(name = "variableId")
        val variableId: String
    )

    @JsonClass(generateAdapter = true)
    data class RemoteFitnessExtension(
        @Json(name = "maxHeartRate") val maxHeartRate: Int?,
        @Json(name = "estimatedVo2Max") val vo2Max: Float?,
        @Json(name = "fitnessAge") val fitnessAge: Int?,
        @Json(name = "type") val type: String = Type.FITNESS_EXTENSION.value
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteSlopeSkiSummaryExtension(
        @Json(name = "statistics") val statistics: RemoteSlopeSkiSummaryExtensionStatistics,
        @Json(name = "runs") val runs: List<RemoteSlopeSkiSummaryExtensionRun>,
        @Json(name = "type") val type: String = Type.SKI_EXTENSION.value
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteIntensityExtension(
        @Json(name = "zones") val zones: RemoteIntensityExtensionIntensityZones?,
        @Json(name = "physiologicalThresholds") val physiologicalThresholds: RemoteIntensityExtensionPhysiologicalThresholds?,
        @Json(name = "type") val type: String = Type.INTENSITY_EXTENSION.value
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteSwimmingHeaderExtension(
        @Json(name = "avgSwolf") val avgSwolf: Int?,
        @Json(name = "avgStrokeRate") val avgStrokeRate: Float?,
        @Json(name = "type") val type: String = Type.SWIMMING_HEADER_EXTENSION.value,
        @Json(name = "breaststrokeGlideTime") val breaststrokeGlideTime: Int?,
        @Json(name = "ventilationFrequency") val ventilationFrequency: Int?,
        @Json(name = "avgFreestyleBreathAngle") val avgFreestyleBreathAngle: Int?,
        @Json(name = "maxFreestyleBreathAngle") val maxFreestyleBreathAngle: Int?,
        @Json(name = "freestyleGlideAngle") val freestyleGlideAngle: Int?,
        @Json(name = "avgBreaststrokeBreathAngle") val avgBreaststrokeBreathAngle: Int?,
        @Json(name = "maxBreaststrokeBreathAngle") val maxBreaststrokeBreathAngle: Int?,
        @Json(name = "freestyleDuration") val freestyleDuration: Int?,
        @Json(name = "breaststrokeDuration") val breaststrokeDuration: Int?,
        @Json(name = "freestylePercent") val freestylePercentage: Int?,
        @Json(name = "breaststrokePercent") val breaststrokePercentage: Int?,
        @Json(name = "breaststrokeHeadAngle") val breaststrokeHeadAngle: Int?,
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteDiveHeaderExtension(
        @Json(name = "maxDepth") val maxDepth: Float?,
        @Json(name = "algorithm") val algorithm: String?,
        @Json(name = "personalSetting") val personalSetting: Int?,
        @Json(name = "diveNumberInSeries") val diveNumberInSeries: Int?,
        @Json(name = "cns") val cns: Float?,
        @Json(name = "algorithmLock") val algorithmLock: Boolean?,
        @Json(name = "diveMode") val diveMode: String?,
        @Json(name = "otu") val otu: Float?,
        @Json(name = "pauseDuration") val pauseDuration: Float?,
        @Json(name = "gasConsumption") val gasConsumption: Float?,
        @Json(name = "altitudeSetting") val altitudeSetting: Float?,
        // typo known in backend
        @Json(name = "gasQuantitites") val gasQuantities: Map<String, Float?>?,
        @Json(name = "surfaceTime") val surfaceTime: Float?,
        @Json(name = "diveTime") val diveTime: Float?,
        @Json(name = "gasesUsed") val gasesUsed: List<String>?,
        @Json(name = "maxDepthTemperature") val maxDepthTemperature: Float?,
        @Json(name = "avgDepth") val avgDepth: Float?,
        @Json(name = "minGF") val minGF: Float?,
        @Json(name = "maxGF") val maxGF: Float?,
        @Json(name = "type") val type: String = Type.DIVE_HEADER_EXTENSION.value
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteWeatherExtension(
        @Json(name = "airPressure") val airPressure: Float?, // kPa
        @Json(name = "cloudiness") val cloudiness: Int?,
        @Json(name = "groundLevelAirPressure") val groundLevelAirPressure: Float?, // kPa
        @Json(name = "humidity") val humidity: Int?,
        @Json(name = "rainVolume1h") val rainVolume1h: Float?,
        @Json(name = "rainVolume3h") val rainVolume3h: Float?,
        @Json(name = "seaLevelAirPressure") val seaLevelAirPressure: Float?, // kPa
        @Json(name = "snowVolume1h") val snowVolume1h: Float?,
        @Json(name = "snowVolume3h") val snowVolume3h: Float?,
        @Json(name = "temperature") val temperature: Float?, // Kelvin
        @Json(name = "weatherIcon") val weatherIcon: String?,
        @Json(name = "windDirection") val windDirection: Float?, // Degrees
        @Json(name = "windSpeed") val windSpeed: Float?,
        @Json(name = "type") val type: String = Type.WEATHER_EXTENSION.value
    ) : RemoteWorkoutExtension()

    @JsonClass(generateAdapter = true)
    data class RemoteJumpRopeExtension(
        @Json(name = "rounds") val rounds: Int?,
        @Json(name = "avgSkipsPerRound") val avgSkipsPerRound: Int?,
        @Json(name = "maxConsecutiveSkips") val maxConsecutiveSkips: Int?,
        @Json(name = "type") val type: String = Type.JUMP_ROPE_EXTENSION.value
    ) : RemoteWorkoutExtension()

    object RemoteUnknownExtension : RemoteWorkoutExtension()
}

/**
 * Ski extension related data entities
 */
@JsonClass(generateAdapter = true)
data class RemoteSlopeSkiSummaryExtensionStatistics(
    @Json(name = "numberOfRuns") val numberOfRuns: Int,
    @Json(name = "descentDurationSeconds") val descentDurationSeconds: Long,
    @Json(name = "descentMeters") val descentMeters: Double,
    @Json(name = "descentDistanceMeters") val descentDistanceMeters: Double,
    @Json(name = "maxSpeed") val maxSpeed: Double
)

@JsonClass(generateAdapter = true)
data class RemoteSlopeSkiSummaryExtensionRun(
    @Json(name = "startTimestamp") val startTimestamp: Long,
    @Json(name = "descentDurationSeconds") val descentDurationSeconds: Long,
    @Json(name = "descentMeters") val descentMeters: Double,
    @Json(name = "descentDistanceMeters") val descentDistanceMeters: Double
)

/**
 * Intensity extension related data entities
 */
@JsonClass(generateAdapter = true)
data class RemoteIntensityExtensionIntensityZones(
    @Json(name = "heartRate") val heartRate: RemoteIntensityExtensionZones?,
    @Json(name = "speed") val speed: RemoteIntensityExtensionZones?,
    @Json(name = "power") val power: RemoteIntensityExtensionZones?
)

@JsonClass(generateAdapter = true)
data class RemoteIntensityExtensionZones(
    @Json(name = "zone1") val zone1: RemoteIntensityExtensionZone,
    @Json(name = "zone2") val zone2: RemoteIntensityExtensionZone,
    @Json(name = "zone3") val zone3: RemoteIntensityExtensionZone,
    @Json(name = "zone4") val zone4: RemoteIntensityExtensionZone,
    @Json(name = "zone5") val zone5: RemoteIntensityExtensionZone
)

@JsonClass(generateAdapter = true)
data class RemoteIntensityExtensionZone(
    @Json(name = "totalTime") val totalTime: Float,
    @Json(name = "lowerLimit") val lowerLimit: Float
)

@JsonClass(generateAdapter = true)
data class RemoteIntensityExtensionPhysiologicalThresholds(
    @Json(name = "aerobic") val aerobic: RemoteIntensityExtensionPhysiologicalThresholdsValues,
    @Json(name = "anaerobic") val anaerobic: RemoteIntensityExtensionPhysiologicalThresholdsValues
)

@JsonClass(generateAdapter = true)
data class RemoteIntensityExtensionPhysiologicalThresholdsValues(
    @Json(name = "heartRate") val heartRate: Int,
    @Json(name = "speed") val speed: Float,
    @Json(name = "power") val power: Float
)

fun String.toRemoteWorkoutExtensionType(): RemoteWorkoutExtension.Type =
    RemoteWorkoutExtension.Type.fromValue(this)
