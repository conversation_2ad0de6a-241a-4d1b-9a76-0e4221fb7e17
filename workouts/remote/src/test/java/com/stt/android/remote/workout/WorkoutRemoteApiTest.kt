package com.stt.android.remote.workout

import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.response.AskoResponse
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class WorkoutRemoteApiTest {
    @Mock
    private lateinit var workoutRestApi: WorkoutRestApi

    @Mock
    private lateinit var workoutRestV2Api: WorkoutRestV2Api

    @Mock
    private lateinit var generateOTPUseCase: GenerateOTPUseCase

    private lateinit var workoutRemoteApi: WorkoutRemoteApi

    private val lastFetchTimestamp = 13L

    @Before
    fun setup() = runTest {
        workoutRemoteApi = WorkoutRemoteApi(
            workoutRestApi,
            workoutRestV2Api,
            createMoshiWithWorkoutExtensionAdapter(),
            generateOTPUseCase
        )
    }

    @Test
    fun `fetch workouts limit 5 max 10 should return 8 available workouts`() = runTest {
        val limit = 5
        val max = 10
        whenever(workoutRestApi.fetchOwnWorkouts(any(), any(), any()))
            .thenReturn(
                AskoResponse(
                    null,
                    mapOf(METADATA_UNTIL to "54"),
                    (1..limit).map { WorkoutEntityFactory.makeRemoteSyncedWorkout() }
                )
            )
            .thenReturn(
                AskoResponse(
                    null,
                    mapOf(METADATA_UNTIL to "54"),
                    (1..3).map { WorkoutEntityFactory.makeRemoteSyncedWorkout() }
                )
            )

        val actual =
            workoutRemoteApi.fetchOwnWorkoutsPaged(limit = limit, max = max)
                .collectAsSinglePage().second.size
        assertThat(actual).isEqualTo(8)
    }

    @Test
    fun `fetch workouts limit 5 max 15 should return available 3 workouts`() = runTest {
        val limit = 5
        val max = 15
        whenever(workoutRestApi.fetchOwnWorkouts(any(), any(), any()))
            .thenReturn(
                AskoResponse(
                    null,
                    mapOf(METADATA_UNTIL to "54"),
                    (1..3).map { WorkoutEntityFactory.makeRemoteSyncedWorkout() }
                )
            )

        val actual = workoutRemoteApi.fetchOwnWorkoutsPaged(limit = limit, max = max)
            .collectAsSinglePage().second.size
        assertThat(actual).isEqualTo(3)
    }

    @Test
    fun `fetch workouts metadata is null should fallback to last fetch timestamp`() =
        runTest {
            whenever(workoutRestApi.fetchOwnWorkouts(any(), any(), any()))
                .thenReturn(
                    AskoResponse(
                        null,
                        null,
                        listOf(WorkoutEntityFactory.makeRemoteSyncedWorkout())
                    )
                )

            val actual = workoutRemoteApi.fetchOwnWorkoutsPaged(since = lastFetchTimestamp)
                .collectAsSinglePage().first

            assertThat(actual).isEqualTo(lastFetchTimestamp)
        }

    @Test
    fun `fetch workouts metadata parsing error should fallback to last fetch timestamp`() =
        runTest {
            whenever(workoutRestApi.fetchOwnWorkouts(any(), any(), any()))
                .thenReturn(
                    AskoResponse(
                        null,
                        mapOf(METADATA_UNTIL to "cannot parse this"),
                        listOf(WorkoutEntityFactory.makeRemoteSyncedWorkout())
                    )
                )

            val actual = workoutRemoteApi.fetchOwnWorkoutsPaged(since = lastFetchTimestamp)
                .collectAsSinglePage().first

            assertThat(actual).isEqualTo(lastFetchTimestamp)
        }

    @Test
    fun `fetch workouts paged limit 5 should return 8 available workouts in two pages`() =
        runTest {
            val limit = 5
            whenever(workoutRestApi.fetchOwnWorkouts(any(), any(), any()))
                .thenReturn(
                    AskoResponse(
                        null,
                        mapOf(METADATA_UNTIL to "54"),
                        (1..limit).map { WorkoutEntityFactory.makeRemoteSyncedWorkout() }
                    )
                )
                .thenReturn(
                    AskoResponse(
                        null,
                        mapOf(METADATA_UNTIL to "0"),
                        (1..3).map { WorkoutEntityFactory.makeRemoteSyncedWorkout() }
                    )
                )

            val actualPages = withTimeout(500L) {
                workoutRemoteApi.fetchOwnWorkoutsPaged(limit = limit).toList()
            }

            assertThat(actualPages.size).isEqualTo(2)
            assertThat(actualPages[0].second.size).isEqualTo(5)
            assertThat(actualPages[1].second.size).isEqualTo(3)
        }

    private suspend fun Flow<Pair<Long, List<RemoteSyncedWorkout>>>.collectAsSinglePage(): Pair<Long, List<RemoteSyncedWorkout>> {
        val pages = toList()
        return pages.last().first to pages.map { it.second }.flatten()
    }
}
