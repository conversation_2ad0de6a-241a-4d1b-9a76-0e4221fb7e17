package com.stt.android.remote.workout

import com.stt.android.remote.extensions.RemoteIntensityExtensionIntensityZones
import com.stt.android.remote.extensions.RemoteIntensityExtensionPhysiologicalThresholds
import com.stt.android.remote.extensions.RemoteIntensityExtensionPhysiologicalThresholdsValues
import com.stt.android.remote.extensions.RemoteIntensityExtensionZone
import com.stt.android.remote.extensions.RemoteIntensityExtensionZones
import com.stt.android.remote.extensions.RemoteSlopeSkiSummaryExtensionRun
import com.stt.android.remote.extensions.RemoteSlopeSkiSummaryExtensionStatistics
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import readFileContent

/**
 * Factory object to generate entities for testing
 */
internal object WorkoutEntityFactory {
    val moshi = createMoshiWithWorkoutExtensionAdapter()

    fun makeRemoteSyncedWorkout(): RemoteSyncedWorkout {
        return moshi.adapter(RemoteSyncedWorkout::class.java)
            .fromJson("workout/RemoteSyncedWorkout.json".readFileContent())!!
    }

    fun makeWorkoutExtensionEntityList(): List<RemoteWorkoutExtension> {
        return listOf(
            makeWorkoutExtensionEntity(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION),
            makeWorkoutExtensionEntity(RemoteWorkoutExtension.Type.FITNESS_EXTENSION),
            makeWorkoutExtensionEntity(RemoteWorkoutExtension.Type.SKI_EXTENSION),
            makeWorkoutExtensionEntity(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION)
        )
    }

    private fun makeWorkoutExtensionEntity(type: RemoteWorkoutExtension.Type): RemoteWorkoutExtension {
        return when (type) {
            RemoteWorkoutExtension.Type.SUMMARY_EXTENSION -> {
                RemoteWorkoutExtension.RemoteSummaryExtension(
                    pte = 1f,
                    feeling = 2,
                    avgTemperature = 3f,
                    peakEpoc = 4f,
                    avgPower = 5f,
                    avgCadence = 6f,
                    avgSpeed = 7f,
                    ascentTime = 8f,
                    descentTime = 9f,
                    performanceLevel = null,
                    recoveryTime = null,
                    ascent = null,
                    descent = null,
                    gear = null,
                    exerciseId = null,
                    zapps = listOf(
                        RemoteWorkoutExtension.RemoteZapp(
                            id = "zapp",
                            authorId = null,
                            externalId = null,
                            name = "a name",
                            summaryOutputs = listOf(
                                RemoteWorkoutExtension.RemoteSummaryOutput(
                                    "format",
                                    "id",
                                    "summaryOutput",
                                    "postfix",
                                    1.0
                                )
                            ),
                            channels = listOf(
                                RemoteWorkoutExtension.RemoteZappChannel(
                                    channelId = 0,
                                    format = "format",
                                    inverted = true,
                                    name = "name",
                                    variableId = "id",
                                )
                            )
                        )
                    ),
                    type = RemoteWorkoutExtension.Type.SUMMARY_EXTENSION.value,
                    maxCadence = 0f,
                    repetitionCount = 0,
                    avgStrideLength = 0f,
                    fatConsumption = 0,
                    carbohydrateConsumption = 0,
                    avgGroundContactTime = 0f,
                    avgVerticalOscillation = 0f,
                    avgLeftGroundContactBalance = 0f,
                    avgRightGroundContactBalance = 0f,
                    lacticThHr = null,
                    lacticThPace = null,
                    avgAscentSpeed = 10f,
                    maxAscentSpeed = 10f,
                    avgDescentSpeed = 10f,
                    maxDescentSpeed = 10f,
                    avgDistancePerStroke = 10f
                )
            }

            RemoteWorkoutExtension.Type.INTENSITY_EXTENSION -> {
                RemoteWorkoutExtension.RemoteIntensityExtension(
                    RemoteIntensityExtensionIntensityZones(
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        ),
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        ),
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        )
                    ),
                    RemoteIntensityExtensionPhysiologicalThresholds(
                        RemoteIntensityExtensionPhysiologicalThresholdsValues(
                            1,
                            2f,
                            3f
                        ),
                        RemoteIntensityExtensionPhysiologicalThresholdsValues(
                            1,
                            2f,
                            3f
                        )
                    )
                )
            }

            RemoteWorkoutExtension.Type.FITNESS_EXTENSION -> {
                RemoteWorkoutExtension.RemoteFitnessExtension(
                    1,
                    2f,
                    null,
                )
            }

            RemoteWorkoutExtension.Type.SKI_EXTENSION -> {
                RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension(
                    RemoteSlopeSkiSummaryExtensionStatistics(
                        1,
                        2L,
                        3.0,
                        4.0,
                        5.0
                    ),
                    listOf(
                        RemoteSlopeSkiSummaryExtensionRun(
                            1L,
                            2L,
                            3.0,
                            4.0
                        )
                    )
                )
            }

            else -> RemoteWorkoutExtension.RemoteUnknownExtension
        }
    }
}
