package com.stt.android.data.workout.videos

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.workout.toVideo
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.videos.VideoDataSource
import com.stt.android.remote.workout.video.VideoRemoteApi
import timber.log.Timber
import javax.inject.Inject

/**
 * Uploads unsynced videos to the server and returns the total count of successful uploads
 */
class SyncNewVideos
@Inject constructor(
    private val videosDataSource: VideoDataSource,
    private val videoFileRepository: VideoFileRepository,
    private val videoRemoteApi: VideoRemoteApi,
    private val currentUser: CurrentUser
) {

    suspend operator fun invoke(): Int = sync()

    /**
     * can sync videos from certain workout instead of all videos
     */
    suspend fun sync(workoutHeader: WorkoutHeader? = null): Int = aggregateAndThrowCapturedErrors { errors ->
        val newWorkoutVideos = runSuspendCatching {
            workoutHeader?.let {
                videosDataSource.findByWorkoutId(it.id)
                    .filter { video -> video.workoutId != null && !video.workoutKey.isNullOrEmpty() && video.locallyChanged }
            } ?: videosDataSource.findUnsyncedVideos(currentUser.getUsername())
        }.getOrElse { e ->
            Timber.w(e, "Unable to get new workout pictures from local DB, skipping...")
            return@aggregateAndThrowCapturedErrors 0
        }

        var uploaded = 0
        newWorkoutVideos.forEach { newWorkoutVideo ->
            Timber.v("Uploading workout video: ${newWorkoutVideo.filename}")
            runSuspendCatching {
                val videoFile = videoFileRepository.getVideoFile(
                    newWorkoutVideo.filename
                        ?: throw IllegalStateException("Video filename cannot be null")
                )
                val response = videoRemoteApi.uploadVideo(
                    workoutKey = newWorkoutVideo.workoutKey
                        ?: throw IllegalStateException("Workout key cannot be null"),
                    timestamp = newWorkoutVideo.timestamp,
                    latitude = newWorkoutVideo.location?.latitude,
                    longitude = newWorkoutVideo.location?.longitude,
                    videoFile = videoFile.file,
                    thumbnail = videoFileRepository.getVideoThumbnailFile(
                        newWorkoutVideo.thumbnailFilename
                            ?: throw IllegalStateException("Video thumbnail filename cannot be null")
                    ),
                    md5hash = videoFile.md5hash,
                    totaltime = newWorkoutVideo.totalTime,
                    width = newWorkoutVideo.width,
                    height = newWorkoutVideo.height
                )

                // Keep workoutId, Video ID and timestamp from the local content. Backend returns a
                // different timestamp, but we keep using the local one so that the ordering of
                // videos is stable.
                videosDataSource.saveVideo(
                    response.toVideo(newWorkoutVideo.workoutId)
                        .copy(id = newWorkoutVideo.id, timestamp = newWorkoutVideo.timestamp)
                )
                uploaded++
            }.onFailure { e ->
                // Report error and continue to next video
                Timber.w(
                    e,
                    "Unable to upload video file for workout key ${newWorkoutVideo.workoutKey}"
                )
                errors += e
            }
        }

        uploaded
    }
}
