package com.stt.android.data.workout.sync

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.featureflags.FeatureFlags
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao
import com.stt.android.data.workout.WorkoutRemoteExtensionMapper
import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.data.workout.toDomainWorkout
import com.stt.android.domain.achievements.AchievementDataSource
import com.stt.android.domain.weather.GetHistoricalWeatherConditionsUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.videos.VideoDataSource
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.remote.workout.RemoteUnsyncedWorkout
import com.stt.android.remote.workout.WorkoutRemoteApi
import com.stt.android.utils.firstOfType
import timber.log.Timber
import java.io.File
import javax.inject.Inject

class SyncNewWorkout
@Inject constructor(
    private val smlZipReferenceDao: SMLZipReferenceDao,
    private val extensionsDataSource: ExtensionsDataSource,
    private val workoutRemoteApi: WorkoutRemoteApi,
    private val featureFlags: FeatureFlags,
    private val binaryFileRepository: BinaryFileRepository,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val extensionRemoteMapper: WorkoutRemoteExtensionMapper,
    private val pictureDataSource: PicturesDataSource,
    private val videoDataSource: VideoDataSource,
    private val workoutRemoteExtensionMapper: WorkoutRemoteExtensionMapper,
    private val achievementDataSource: AchievementDataSource,
    private val getHistoricalWeatherConditionsUseCase: GetHistoricalWeatherConditionsUseCase,
) {
    suspend operator fun invoke(workoutHeader: WorkoutHeader) {
        Timber.d("Uploading new workout ${workoutHeader.id}")
        val extensions = extensionsDataSource.getExtensionsForWorkout(workoutHeader)
            .map(extensionRemoteMapper.toDataEntity())

        val syncedWorkoutHeader = save(
            workoutHeader = workoutHeader,
            extensions = extensions,
        )

        val syncedKey = syncedWorkoutHeader.key
        if (syncedKey != null && workoutHeaderDataSource.findByKey(syncedKey) != null) {
            // This situation can happen if a push sync reached the server but we didn't get the
            // response, and then we successfully fetched the workout from server and gave it a new
            // id as we couldn't store the key here. The workout is now twice in the local db,
            // and the 'original' that stays in a not synced state, as trying to continue this
            // method normally would end in failure due to workoutHeader with the key already existing
            // (server recognizes the duplicate and returns the same key). For now, delete the original.
            // TODO: Add conflict resolution when last modification timestamp is added to workouts
            workoutHeaderDataSource.markDeletedOrPermanentlyDelete(workoutHeader.id)
            return
        }

        // We update the workout header with the response from the server because there
        // are some workout header values that are updated on the server.
        // The backend returns us picture count 0 because we haven't uploaded pictures yet
        // but we've them locally so let's force the picture count and also marked as synced.
        val updatedWorkoutHeader = syncedWorkoutHeader.copy(
            id = workoutHeader.id,
            pictureCount = workoutHeader.pictureCount,
            seen = workoutHeader.seen,
            locallyChanged = false
            // it's useful to check this flag if you are testing new extension data that is not
            // supported on backend yet, this way it prevents downloading the extensions again
            // extensionsFetched = true
        )

        workoutHeaderDataSource.storeWorkout(updatedWorkoutHeader)
        Timber.v("Workout ${workoutHeader.id} uploaded successfully")

        val workoutId = updatedWorkoutHeader.id

        Timber.v("Updating workout's pics and videos workout key")
        pictureDataSource.findByWorkoutId(workoutId)
            .map { it.copy(workoutKey = updatedWorkoutHeader.key) }
            .forEach { pictureDataSource.savePicture(it) }

        videoDataSource.findByWorkoutId(workoutId)
            .map { it.copy(workoutKey = updatedWorkoutHeader.key) }
            .forEach { videoDataSource.saveVideo(it) }

        achievementDataSource.calculateAchievements(updatedWorkoutHeader)
    }

    private suspend fun save(
        workoutHeader: WorkoutHeader,
        extensions: List<RemoteWorkoutExtension>,
    ): WorkoutHeader {
        val smlZipFile: File? = getSMLZipFile(workoutHeader.id)
        val hasSml = smlZipFile != null
        val binary = if (hasSml) {
            null // Don't send the binary if SML exists
        } else {
            binaryFileRepository.get(workoutHeader)
        }
        val remoteExtensions = if (hasSml) {
            // Send only extensions that the backend cannot generate from the SML
            listOfNotNull(
                getWeatherExtension(workoutHeader, extensions, hasSml),
            )
        } else {
            extensions
        }

        Timber.d("Syncing a new workout. Has SML:$hasSml, extensions:$remoteExtensions")
        return workoutRemoteApi.saveWorkout(
            RemoteUnsyncedWorkout(
                binary,
                remoteExtensions,
                smlZipFile,
            )
        ).toDomainWorkout(workoutRemoteExtensionMapper, workoutHeader.id).header
    }

    private suspend fun getWeatherExtension(
        workoutHeader: WorkoutHeader,
        extensions: List<RemoteWorkoutExtension>,
        hasSml: Boolean,
    ): RemoteWorkoutExtension? {
        extensions.firstOfType<RemoteWorkoutExtension.RemoteWeatherExtension>()
            ?.let { return it }

        // We don't do it for workouts tracked by phone.
        if (!hasSml) {
            return null
        }

        // We only tried to get weather info when watch sends workout to mobile, and it might fail
        // because e.g. user doesn't have network connection. So let's try again here.
        val stopPosition = workoutHeader.stopPosition ?: return null
        return runSuspendCatching {
            val weather = getHistoricalWeatherConditionsUseCase(
                GetHistoricalWeatherConditionsUseCase.Params(
                    latitude = stopPosition.latitude,
                    longitude = stopPosition.longitude,
                    timestamp = workoutHeader.stopTime / 1000L,
                )
            )
            extensionRemoteMapper.toDataEntity()(
                WeatherExtension(workoutHeader.id, weather)
            )
        }.getOrElse { e ->
            Timber.w(e, "Error while trying to get weather info")
            null
        }
    }

    private suspend fun getSMLZipFile(workoutId: Int): File? {
        return if (featureFlags.isPushSMLZipEnabled()) {
            val localSMLZipPath = try {
                smlZipReferenceDao.findById(workoutId)?.zipPath
            } catch (e: Exception) {
                Timber.w(e, "Error retrieving local sml zip reference")
                null
            }
            if (localSMLZipPath != null) {
                File(localSMLZipPath)
            } else {
                null
            }
        } else {
            null
        }
    }
}
