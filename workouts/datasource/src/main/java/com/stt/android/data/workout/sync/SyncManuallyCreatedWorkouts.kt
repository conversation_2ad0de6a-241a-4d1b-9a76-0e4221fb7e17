package com.stt.android.data.workout.sync

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import timber.log.Timber
import javax.inject.Inject

/**
 * Syncs manually created workouts to the server
 */
class SyncManuallyCreatedWorkouts
@Inject constructor(
    private val dummyBinaryFile: BinaryFileRepository,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val syncNewWorkout: SyncNewWorkout
) {

    suspend operator fun invoke() = sync()

    /**
     * can sync a manually created workout from param instead of all workout
     */
    suspend fun sync(workoutHeader: WorkoutHeader? = null) =
        aggregateAndThrowCapturedErrors { errors ->
            val workoutHeaders =
                workoutHeader?.let { it.key?.let { emptyList() } ?: listOf(workoutHeader) } ?: run {
                    runSuspendCatching {
                        workoutHeaderDataSource.findManuallyCreatedWorkouts()
                    }.getOrElse { e ->
                        Timber.w(e, "Unable to get manually created workouts from local DB")
                        throw e
                    }
                }
            workoutHeaders.forEach { workoutHeader ->
                runSuspendCatching {
                    Timber.v("Creating dummy binary for workout ${workoutHeader.id}")
                    dummyBinaryFile.create(workoutHeader)
                    Timber.v("Syncing workout ${workoutHeader.id} to the server")
                    syncNewWorkout(workoutHeader)
                }.onFailure { e ->
                    Timber.w(e, "Unable to push manually created workout. %s", workoutHeader)
                    errors += e
                }
            }
        }
}
