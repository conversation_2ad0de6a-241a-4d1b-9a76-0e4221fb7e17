package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.diveextension.LocalDiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtension
import javax.inject.Inject

class DiveExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalDiveExtension, DiveExtension> {

    override fun toDomainEntity(): Function1<LocalDiveExtension, DiveExtension> = {
        DiveExtension(
            it.workoutId,
            it.maxDepth,
            it.algorithm,
            it.personalSetting,
            it.diveNumberInSeries,
            it.cns,
            it.algorithmLock,
            it.diveMode,
            it.otu,
            it.pauseDuration,
            it.gasConsumption,
            it.altitudeSetting,
            it.gasQuantities,
            it.surfaceTime,
            it.diveTime,
            it.gasesUsed,
            it.maxDepthTemperature,
            it.avgDepth,
            it.minGF,
            it.maxGF
        )
    }

    override fun toDataEntity(): Function1<DiveExtension, LocalDiveExtension> = {
        LocalDiveExtension(
            it.workoutId ?: throw IllegalStateException("Workout ID cannot be null"),
            it.maxDepth,
            it.algorithm,
            it.personalSetting,
            it.diveNumberInSeries,
            it.cns,
            it.algorithmLock,
            it.diveMode,
            it.otu,
            it.pauseDuration,
            it.gasConsumption,
            it.altitudeSetting,
            it.gasQuantities ?: emptyMap(),
            it.surfaceTime,
            it.diveTime,
            it.gasesUsed ?: emptyList(),
            it.maxDepthTemperature,
            it.avgDepth,
            it.minGF,
            it.maxGF
        )
    }
}
