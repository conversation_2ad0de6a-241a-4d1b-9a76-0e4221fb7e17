package com.stt.android.domain.achievements

import com.stt.android.data.toEpochMilli
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.utils.FixedFirstDayOfTheWeekCalendarProvider
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.LocalTime
import java.time.ZoneId
import java.util.Locale

@RunWith(MockitoJUnitRunner::class)
class AchievementsTest {

    @Mock
    private lateinit var workoutHeaderDataSource: WorkoutHeaderDataSource

    @Mock
    private lateinit var achievementAnalytics: AchievementAnalytics

    private lateinit var achievementProvider: AchievementProvider

    private val calendarProvider = FixedFirstDayOfTheWeekCalendarProvider(Locale.getDefault())

    @Before
    fun setup() {
        achievementProvider = AchievementProvider(workoutHeaderDataSource, achievementAnalytics, calendarProvider)
    }

    @Test
    fun `should create expected cumulative achievement`() {
        val expected = expectedAchievementWithCumulativeValues()

        val zoneId = ZoneId.systemDefault()
        val newWorkoutLocalDate = Instant.ofEpochMilli(newWorkout.startTime).atZone(zoneId).toLocalDate()
        val firstDayOfTheYear = newWorkoutLocalDate.withDayOfYear(1)
        val startOfYearMillis = firstDayOfTheYear.atStartOfDay().atZone(zoneId).toEpochMilli()
        val firstDayOfMonth = newWorkoutLocalDate.withDayOfMonth(1)
        val startOfMonthMillis = firstDayOfMonth.atStartOfDay().atZone(zoneId).toEpochMilli()
        val dayOfWeek = calendarProvider.getDayOfWeekField()
        val startOfCurrentWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).toEpochMilli()
        val startOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).minusWeeks(1).toEpochMilli()
        val endOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 7).atTime(LocalTime.MAX).atZone(zoneId).minusWeeks(1)
                .toEpochMilli()

        runTest {
            whenever(workoutHeaderDataSource.loadActivityTypeCount(newWorkout.activityTypeId)).thenReturn(5L)
            whenever(
                workoutHeaderDataSource.loadFastestOfActivityType(
                    anyInt(),
                    anyLong(),
                    anyLong()
                )
            ).thenReturn(workoutInDB)

            whenever(
                workoutHeaderDataSource.loadFarthestOfActivityType(
                    anyInt(),
                    anyLong(),
                    anyLong()
                )
            ).thenReturn(workoutInDB)

            whenever(workoutHeaderDataSource.loadActivityCountInPeriod(startOfYearMillis, newWorkout.startTime)).thenReturn(74L)
            whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(560L)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfYearMillis, newWorkout.startTime)).thenReturn(31L)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfCurrentWeekMillis, newWorkout.startTime)).thenReturn(6)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfPreviousWeekMillis, endOfPreviousWeekMillis)).thenReturn(3)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfMonthMillis, newWorkout.startTime)).thenReturn(15)

            val achievement = achievementProvider.calculateAchievements(newWorkout)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.description).isEqualTo(expected.cumulativeAchievements[0].description)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.activityCount).isEqualTo(expected.cumulativeAchievements[0].activityCounts.activityCount)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.previousCount).isEqualTo(expected.cumulativeAchievements[0].activityCounts.previousCount)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.currentCount).isEqualTo(expected.cumulativeAchievements[0].activityCounts.currentCount)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.timeCategory).isEqualTo(expected.cumulativeAchievements[0].activityCounts.timeCategory)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.firstInCount).isEqualTo(expected.cumulativeAchievements[0].activityCounts.firstInCount)
            Assertions.assertThat(achievement?.cumulativeAchievements?.get(0)?.activityCounts?.activityTypeCount).isEqualTo(expected.cumulativeAchievements[0].activityCounts.activityTypeCount)
        }
    }

    fun expectedAchievementWithCumulativeValues(): Achievement {
        return Achievement(
            workoutKey = "123456789",
            activityType = 63,
            timestamp = 10000,
            cumulativeAchievements = listOf(
                CumulativeAchievement(
                    description = 0,
                    activityCounts = ActivityCounts(
                        timeCategory = 2,
                        currentCount = 6,
                        previousCount = 3,
                        activityTypeCount = 0,
                        firstInCount = null,
                        activityCount = 0
                    )
                )
            )
        )
    }

    @Test
    fun `should create expected personal best achievement`() {
        val expected = expectedAchivementWithPersonalBestValues()

        val zoneId = ZoneId.systemDefault()
        val newWorkoutLocalDate = Instant.ofEpochMilli(newWorkout.startTime).atZone(zoneId).toLocalDate()
        val firstDayOfTheYear = newWorkoutLocalDate.withDayOfYear(1)
        val startOfYearMillis = firstDayOfTheYear.atStartOfDay().atZone(zoneId).toEpochMilli()
        val firstDayOfMonth = newWorkoutLocalDate.withDayOfMonth(1)
        val startOfMonthMillis = firstDayOfMonth.atStartOfDay().atZone(zoneId).toEpochMilli()
        val dayOfWeek = calendarProvider.getDayOfWeekField()
        val startOfCurrentWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).toEpochMilli()
        val startOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).minusWeeks(1).toEpochMilli()
        val endOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 7).atTime(LocalTime.MAX).atZone(zoneId).minusWeeks(1)
                .toEpochMilli()

        runTest {
            whenever(workoutHeaderDataSource.loadActivityTypeCount(newWorkout.activityTypeId)).thenReturn(5L)
            whenever(
                workoutHeaderDataSource.loadFastestOfActivityType(
                    anyInt(),
                    anyLong(),
                    anyLong()
                )
            ).thenReturn(workoutInDB)

            whenever(
                workoutHeaderDataSource.loadFarthestOfActivityType(
                    anyInt(),
                    anyLong(),
                    anyLong()
                )
            ).thenReturn(workoutInDB)

            whenever(workoutHeaderDataSource.loadActivityCountInPeriod(startOfYearMillis, newWorkout.startTime)).thenReturn(74L)
            whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(560L)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfYearMillis, newWorkout.startTime)).thenReturn(31L)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfCurrentWeekMillis, newWorkout.startTime)).thenReturn(6)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfPreviousWeekMillis, endOfPreviousWeekMillis)).thenReturn(3)
            whenever(workoutHeaderDataSource.loadActivityTypeCountInPeriod(newWorkout.activityTypeId, startOfMonthMillis, newWorkout.startTime)).thenReturn(15)

            val achievement = achievementProvider.calculateAchievements(newWorkout)
            Assertions.assertThat(achievement?.personalBestAchievements?.get(0)?.valueCategory).isEqualTo(expected.personalBestAchievements[0].valueCategory)
            Assertions.assertThat(achievement?.personalBestAchievements?.get(0)?.timeCategory).isEqualTo(expected.personalBestAchievements[0].timeCategory)
            Assertions.assertThat(achievement?.personalBestAchievements?.get(0)?.since).isEqualTo(expected.personalBestAchievements[0].since)
        }
    }

    fun expectedAchivementWithPersonalBestValues(): Achievement {
        return Achievement(
            workoutKey = "123456789",
            activityType = 63,
            timestamp = 10000,
            personalBestAchievements = listOf(
                PersonalBestAchievement(
                    timeCategory = 0,
                    valueCategory = 2,
                    since = 0
                )
            )
        )
    }

    private val newWorkout = WorkoutHeader(
        id = 1,
        key = "123456789",
        totalDistance = 140.0,
        maxSpeed = 0.0,
        activityTypeId = 63,
        avgSpeed = 45.0,
        description = null,
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 10000,
        stopTime = 20000,
        totalTime = 10000.0,
        energyConsumption = 50.0,
        username = "foo",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 180.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 1,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 0,
        polyline = null,
        manuallyAdded = true,
        reactionCount = 0,
        totalAscent = 0.0,
        totalDescent = 0.0,
        recoveryTime = 10000,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 0.0,
        minAltitude = 0.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )

    private val workoutInDB = WorkoutHeader(
        id = 1,
        key = "123456789",
        totalDistance = 140.0,
        maxSpeed = 0.0,
        activityTypeId = 63,
        avgSpeed = 20.0,
        description = null,
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 10000,
        stopTime = 20000,
        totalTime = 10000.0,
        energyConsumption = 50.0,
        username = "foo",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 180.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 1,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 0,
        polyline = null,
        manuallyAdded = true,
        reactionCount = 0,
        totalAscent = 0.0,
        totalDescent = 0.0,
        recoveryTime = 10000,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 0.0,
        minAltitude = 0.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )
}
