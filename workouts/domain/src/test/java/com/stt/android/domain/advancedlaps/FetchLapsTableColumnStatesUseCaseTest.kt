package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.infomodel.SummaryItem
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class FetchLapsTableColumnStatesUseCaseTest {
    @Mock
    private lateinit var dataSource: LapsTableStateDataSource

    private lateinit var useCase: FetchLapsTableColumnStatesUseCase

    @Before
    fun setup() {
        useCase = FetchLapsTableColumnStatesUseCase(dataSource)
    }

    @Test
    fun `test that fetching lapstable column state completes successfully`() {
        val summaryList = listOf(SummaryItem.DURATION, SummaryItem.AVGPOWER, SummaryItem.AVGCADENCE, SummaryItem.AVGSPEED)
            .map { LapsTableDataType.Summary(it) }
        whenever(dataSource.fetchColumnsState(any())).thenReturn(summaryList)

        assertEquals(summaryList, useCase(8, LapsTableType.DISTANCE_AUTO_LAP))
    }
}
