package com.stt.android.domain.workouts.tag

import com.soy.algorithms.intensity.IntensityZones
import com.stt.android.domain.Point
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.extensions.intensity.WorkoutImpactUseCase
import com.suunto.algorithms.data.Length.Companion.meters
import com.suunto.algorithms.ddfa.DynamicDFAZones
import com.suunto.algorithms.geo.LatLng
import com.suunto.algorithms.tag.WorkoutTag
import com.suunto.algorithms.tag.WorkoutTagCalculationInput
import com.suunto.algorithms.tag.WorkoutTagCalculator
import javax.inject.Inject

class SuuntoTagUseCase @Inject constructor(
    private val workoutImpactUseCase: WorkoutImpactUseCase,
) {
    suspend fun calculateSuuntoTags(
        workoutHeader: BasicWorkoutHeader,
        autoTagCommute: Boolean,
        marathonTagSupported: Boolean,
        startPoint: Point?,
        endPoint: Point?,
        intensityZones: IntensityZones? = null,
        dynamicDFAZones: DynamicDFAZones? = null,
    ): List<SuuntoTag> {
        val suuntoTagsFromWorkoutTags = WorkoutTagCalculator.calculateWorkoutTags(
            input = WorkoutTagCalculationInput(
                activityTypeId = workoutHeader.activityTypeId,
                startLatLng = startPoint?.toSTTLatLng(),
                endLatLng = endPoint?.toSTTLatLng(),
                distance = workoutHeader.totalDistance.meters,
            ),
        ).mapNotNull { workoutTag ->
            when (workoutTag) {
                WorkoutTag.COMMUTE -> {
                    if (autoTagCommute) {
                        SuuntoTag.COMMUTE
                    } else {
                        null
                    }
                }
                WorkoutTag.MARATHON -> if (marathonTagSupported) SuuntoTag.MARATHON else null
                WorkoutTag.HALF_MARATHON -> if (marathonTagSupported) SuuntoTag.HALF_MARATHON else null
            }
        }

        val suuntoTagsFromWorkoutImpacts = workoutImpactUseCase
            .calculateWorkoutImpacts(
                workoutHeader = workoutHeader,
                intensityZones = intensityZones,
                dynamicDFAZones = dynamicDFAZones,
            )
            .mapNotNull(SuuntoTag::fromWorkoutImpact)

        return suuntoTagsFromWorkoutTags + suuntoTagsFromWorkoutImpacts
    }

    private companion object {
        fun Point.toSTTLatLng(): LatLng = LatLng(
            latitude = latitude,
            longitude = longitude,
        )
    }
}
