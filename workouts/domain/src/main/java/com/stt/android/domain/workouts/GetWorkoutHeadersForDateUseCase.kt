package com.stt.android.domain.workouts

import com.stt.android.domain.CoroutineUseCase
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import javax.inject.Inject

class GetWorkoutHeadersForDateUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) : CoroutineUseCase<List<WorkoutHeader>, GetWorkoutHeadersForDateUseCase.Params> {

    override suspend fun run(params: Params): List<WorkoutHeader> {
        val startTimeMillis = params.date
            .atStartOfDay(params.zoneId)
            .toInstant()
            .toEpochMilli()

        val endTimeMillis = params.date
            .plusDays(1L)
            .atStartOfDay(params.zoneId)
            .toInstant()
            .toEpochMilli() - 1 // Minus one to exclude workout at midnight the next day

        return workoutHeaderDataSource.findByStartTime(
            params.username,
            startTimeMillis,
            endTimeMillis
        )
    }

    class Params(
        val username: String,
        val date: LocalDate,
        val zoneId: ZoneId = ZoneOffset.systemDefault()
    )
}
