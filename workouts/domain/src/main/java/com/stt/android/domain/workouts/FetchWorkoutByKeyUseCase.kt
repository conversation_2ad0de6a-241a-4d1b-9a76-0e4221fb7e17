package com.stt.android.domain.workouts

import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.domain.workouts.videos.VideoDataSource
import java.util.EnumSet
import javax.inject.Inject

/**
 * Fetches the workout from the backend and stores the header, photos and videos to local db
 */
class FetchWorkoutByKeyUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource,
    private val picturesDataSource: PicturesDataSource,
    private val videoDataSource: VideoDataSource
) {
    suspend operator fun invoke(
        username: String,
        workoutKey: String,
        extensions: EnumSet<Extension>? = null,
        additionalData: EnumSet<AdditionalData>? = null,
    ): DomainWorkout {
        val workout = if (username.isEmpty()) {
            workoutDataSource.fetchWorkout(
                workoutKey = workoutKey,
            )
        } else {
            workoutDataSource.fetchCombinedWorkout(
                username = username,
                workoutKey = workoutKey,
                extensions = extensions,
                additionalData = additionalData
            )
        }
        // Store header to local db
        val header = workoutDataSource.store(workout.header)
        // Store media to local db
        storePictures(header, workout.pictures)
        storeVideos(header, workout.videos)
        return workout.copy(
            header = header,
            pictures = picturesDataSource.findByWorkoutId(header.id),
            videos = videoDataSource.findByWorkoutId(header.id),
        )
    }

    private suspend fun storePictures(
        header: WorkoutHeader,
        pictures: List<Picture>?
    ) {
        val localSyncedPics = picturesDataSource.findByWorkoutId(header.id)
            .filterNot { it.locallyChanged }
            .associateBy { it.key }

        pictures?.map { it.copy(workoutId = header.id) }
            ?.forEach { remotePic ->
                val picture = if (localSyncedPics.containsKey(remotePic.key)) {
                    val localPic = localSyncedPics[remotePic.key]
                    remotePic.copy(
                        id = localPic?.id,
                        fileName = localPic?.fileName
                    )
                } else {
                    remotePic
                }
                picturesDataSource.savePicture(picture)
            }
    }

    private suspend fun storeVideos(
        header: WorkoutHeader,
        videos: List<Video>?
    ) {
        val localSyncedVideos = videoDataSource.findByWorkoutId(header.id)
            .filterNot { it.locallyChanged }
            .associateBy { it.key }

        videos?.map { it.copy(workoutId = header.id) }
            ?.forEach { remoteVideo ->
                val video = if (localSyncedVideos.containsKey(remoteVideo.key)) {
                    val localVideo = localSyncedVideos[remoteVideo.key]
                    remoteVideo.copy(
                        id = localVideo?.id,
                        filename = localVideo?.filename,
                        thumbnailFilename = localVideo?.thumbnailFilename
                    )
                } else {
                    remoteVideo
                }
                videoDataSource.saveVideo(video)
            }
    }
}
