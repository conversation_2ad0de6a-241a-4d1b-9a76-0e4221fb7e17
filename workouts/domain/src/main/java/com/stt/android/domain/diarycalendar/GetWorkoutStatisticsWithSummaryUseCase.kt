package com.stt.android.domain.diarycalendar

import com.stt.android.domain.CoroutineUseCase
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase.Params
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtensionDataSource
import com.stt.android.infomodel.shouldNotCountAscentForActivity
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.menstrualcycle.domain.MenstrualCycleLocalDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import com.stt.android.utils.iterator
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import javax.inject.Inject
import kotlin.math.roundToLong

/**
 * Get statistics for workouts in the given date range ([Params.firstDay]-[Params.endDay]). The data
 * includes daily values for each activity group and a summary that has the total values for
 * each activity type.
 *
 * Current system default timezone is used. The returned data may change if the system timezone
 * changes, even for the same user.
 *
 * Only non-deleted workouts for the given [Params.username] are considered.
 */
class GetWorkoutStatisticsWithSummaryUseCase
@Inject constructor(
    private val workoutsDataSource: WorkoutHeaderDataSource,
    private val mapper: ActivityGroupMapper,
    private val diveExtensionDataSource: DiveExtensionDataSource,
    private val menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource
) : CoroutineUseCase<DailyWorkoutStatisticsWithSummary, Params> {

    override suspend fun run(params: Params): DailyWorkoutStatisticsWithSummary {
        val firstDayMillis = params.firstDay.toPeriodStartMillis()
        val onePastLastDayMillis = params.endDay.toPeriodEndMillis()

        Timber.d("Fetch starting from ${params.firstDay} up to and including ${params.endDay}")

        // All workouts in period
        val workoutsInPeriod = if (params.loadWorkouts) {
            workoutsDataSource.findByStartTime(
                params.username,
                firstDayMillis,
                onePastLastDayMillis - 1L // Minus 1 milli so that workout at midnight is not included
            )
        } else {
            emptyList()
        }

        val menstrualCycles = if (params.loadMenstrualCycles) {
            fetchMenstrualCycle(params.firstDay, params.endDay, params.loadPredictedMenstrualCycles)
        } else {
            emptyList()
        }

        return createSummary(workoutsInPeriod, menstrualCycles, params.firstDay, params.endDay)
    }

    /**
     * Generates multiple summaries without need to fetch same workouts multiple times from the database.
     * The database is queried in a way that all workouts from earliest start to latest end are fetched,
     * so to get any benefits from using this the time periods need to overlap.
     *
     * The username is taken from first params in the list.
     *
     * @return List of [DailyWorkoutStatisticsWithSummary] in the same order as requested in the
     *   params list
     */
    suspend fun runWithMultipleTimePeriods(params: List<Params>): List<DailyWorkoutStatisticsWithSummary> {
        if (params.isEmpty()) return emptyList()

        val username = params.first().username
        val totalFirstDay = params.minOf { it.firstDay }
        val totalEndDay = params.maxOf { it.endDay }
        val loadPredictedMenstrualCycles = params.first().loadPredictedMenstrualCycles

        val totalFirstDayMillis = totalFirstDay.toPeriodStartMillis()
        val totalOnePastLastDayMillis = totalEndDay.toPeriodEndMillis()

        Timber.d("Fetch starting from $totalFirstDay up to and including $totalEndDay")

        // All workouts in time period that covers all requested periods
        val workoutsInAllPeriods = workoutsDataSource.findByStartTime(
            username,
            totalFirstDayMillis,
            totalOnePastLastDayMillis - 1L // Minus 1 milli so that workout at midnight is not included
        )

        val loadMenstrualCycles = params.first().loadMenstrualCycles
        val menstrualCyclesInAllPeriods = if (loadMenstrualCycles) {
            fetchMenstrualCycle(totalFirstDay, totalEndDay, loadPredictedMenstrualCycles)
        } else {
            emptyList()
        }

        val diveExtensionsCache = mutableMapOf<Int, DiveExtension?>()

        return params.map { period ->
            val periodStartMillis = period.firstDay.toPeriodStartMillis()
            val periodEndMillis = period.endDay.toPeriodEndMillis()

            val workoutsInThisPeriod = workoutsInAllPeriods.filter {
                // Don't include periodEndMillis so midnight isn't included
                it.startTime in periodStartMillis until periodEndMillis
            }

            val menstrualCyclesInThisPeriod = menstrualCyclesInAllPeriods.filter {
                !it.startDate.isAfter(period.endDay) || !it.endDate.isBefore(period.firstDay)
            }

            createSummary(
                workoutsInThisPeriod,
                menstrualCyclesInThisPeriod,
                period.firstDay,
                period.endDay,
                diveExtensionsCache
            )
        }
    }

    private suspend fun fetchMenstrualCycle(
        startDate: LocalDate,
        endDate: LocalDate,
        loadPredictedMenstrualCycles: Boolean
    ): List<MenstrualCycle> {
        return menstrualCycleLocalDataSource.fetchInRange(
            startDate,
            endDate
        ).filter { it.includedDates.isNotEmpty() }
            .filter {
                loadPredictedMenstrualCycles ||
                    it.menstrualCycleType != MenstrualCycleType.PREDICTED
            }
    }

    private suspend fun createSummary(
        workouts: List<WorkoutHeader>,
        menstrualCycles: List<MenstrualCycle>,
        firstDay: LocalDate,
        endDay: LocalDate,
        diveExtensionsCache: MutableMap<Int, DiveExtension?> = mutableMapOf()
    ): DailyWorkoutStatisticsWithSummary {
        val diveWorkouts = workouts.filter {
            mapper.activityTypeIdToGroup(it.activityTypeId) == ActivityGroup.Diving
        }

        // All workouts grouped by date
        val workoutsByDate: Map<LocalDate, List<WorkoutHeader>> =
            workouts.groupBy {
                Instant.ofEpochMilli(it.startTime)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
            }

        // Daily workouts grouped by activity group
        val workoutsByDateAndGroup: Map<LocalDate, Map<ActivityGroup, List<WorkoutHeader>>> =
            workoutsByDate.mapValues { entry ->
                entry.value.groupBy { header ->
                    mapper.activityTypeIdToGroup(header.activityTypeId)
                }
            }

        // Get workout IDs for each date and total daily durations by activity group
        val data = getDailyData(firstDay, endDay, workoutsByDateAndGroup, menstrualCycles)

        // Total values by activity type for the whole period
        val totalValuesByActivityType: TotalValuesByActivityType =
            getTotalValuesByActivityType(workouts, diveExtensionsCache)

        val totalMaxDepth: Float = totalValuesByActivityType.mapNotNull { (_, values) ->
            values.maxDepth
        }.maxOrNull() ?: 0f

        val totalValues = DiaryCalendarTotalValues(
            duration = workouts.sumOf { it.totalTime },
            distance = workouts.sumOf { it.totalDistance },
            ascent = totalValuesByActivityType.values.mapNotNull { it.ascent }.sum(),
            energy = workouts.sumOf { it.energyConsumption },
            activityCount = workouts.size,
            diveCount = diveWorkouts.size,
            maxDepth = totalMaxDepth
        )

        val locations: List<LocationWithActivityType> = getLocations(workouts)

        return DailyWorkoutStatisticsWithSummary(
            dailyData = data,
            totalValuesByActivityType = totalValuesByActivityType,
            totalValues = totalValues,
            locations = locations
        )
    }

    /**
     * Get daily workout duration by activity group between [firstDay] and [endDay] (inclusive).
     */
    private fun getDailyData(
        firstDay: LocalDate,
        endDay: LocalDate,
        workoutsByDateAndGroup: Map<LocalDate, Map<ActivityGroup, List<WorkoutHeader>>>,
        menstrualCycles: List<MenstrualCycle>
    ): Map<LocalDate, DiaryCalendarDailyData> {
        val data = mutableMapOf<LocalDate, DiaryCalendarDailyData>()
        for (day in firstDay..endDay) {
            val workoutsByGroup = workoutsByDateAndGroup[day] ?: emptyMap()
            val durationByGroup = workoutsByGroup.mapValues { (_, workoutHeaders) ->
                workoutHeaders.totalDurationMillis()
            }

            val workoutIds = workoutsByGroup.values
                .flatten()
                .map { it.id }

            val menstrualDateType: MenstrualDateType = getMenstrualDateType(day, menstrualCycles)

            data[day] = DiaryCalendarDailyData(
                durationByActivityGroup = durationByGroup,
                workoutIds = workoutIds,
                menstrualDateType = menstrualDateType
            )
        }

        return data
    }

    private fun getMenstrualDateType(
        day: LocalDate,
        menstrualCycles: List<MenstrualCycle>
    ): MenstrualDateType {
        var menstrualDateType: MenstrualDateType? = null
        val today = LocalDate.now()
        for (menstrualCycle in menstrualCycles) {
            if (day !in menstrualCycle.includedDates) continue

            menstrualDateType = when (menstrualCycle.menstrualCycleType) {
                MenstrualCycleType.PREDICTED -> {
                    when {
                        day.isEqual(menstrualCycle.startDate) && day.isEqual(menstrualCycle.endDate) -> MenstrualDateType.BOTH_START_AND_END_IN_PREDICTION
                        day.isEqual(menstrualCycle.startDate) -> MenstrualDateType.START_OF_PREDICTION
                        day.isEqual(menstrualCycle.endDate) -> MenstrualDateType.END_OF_PREDICTION
                        day.isAfter(menstrualCycle.startDate) && day.isBefore(menstrualCycle.endDate) -> MenstrualDateType.IN_PREDICTION
                        else -> null
                    }
                }

                MenstrualCycleType.HISTORICAL -> {
                    when {
                        day.isEqual(menstrualCycle.startDate) && day.isEqual(menstrualCycle.endDate) -> MenstrualDateType.BOTH_START_AND_END_IN_HISTORY
                        day.isEqual(menstrualCycle.startDate) -> MenstrualDateType.START_OF_HISTORY
                        day.isEqual(menstrualCycle.endDate) ->
                            if (day.isAfter(today)) MenstrualDateType.END_OF_HISTORY_AFTER_TODAY else MenstrualDateType.END_OF_HISTORY_NORMAL

                        day.isAfter(menstrualCycle.startDate) && day.isBefore(menstrualCycle.endDate) ->
                            if (day.isAfter(today)) MenstrualDateType.IN_HISTORY_AFTER_TODAY else MenstrualDateType.IN_HISTORY_NORMAL

                        else -> null
                    }
                }
            }
            if (menstrualDateType != null) break
        }
        return menstrualDateType ?: MenstrualDateType.NOTHING
    }

    /**
     * Get the total duration, distance, ascent, etc, as a [DiaryCalendarTotalValues] object for the
     * given list of [workouts].
     */
    private suspend fun getTotalValuesByActivityType(
        workouts: List<WorkoutHeader>,
        diveExtensionsCache: MutableMap<Int, DiveExtension?>
    ): TotalValuesByActivityType {
        return workouts
            .groupBy { it.activityTypeId }
            .mapValues { (activityType, workoutHeaders) ->
                val workoutIdsToKeys: Map<Int?, String?> = workoutHeaders.associate {
                    it.id to it.key
                }

                val maxDepth: Float? // for dives
                val ascent: Double? // for non-dives
                var diveCount = 0
                when {
                    mapper.activityTypeIdToGroup(activityType) == ActivityGroup.Diving -> {
                        ascent = null
                        diveCount = workoutHeaders.size

                        val diveExtensions: List<DiveExtension> = workoutHeaders
                            .map { it.id }
                            .mapNotNull { workoutId ->
                                diveExtensionsCache.getOrPut(workoutId) {
                                    diveExtensionDataSource.findById(workoutId)
                                }
                            }

                        maxDepth = diveExtensions.mapNotNull { it.maxDepth }.maxOrNull()
                    }

                    shouldNotCountAscentForActivity(activityType) -> {
                        maxDepth = null
                        ascent = null
                    }

                    else -> {
                        maxDepth = null
                        ascent = workoutHeaders.sumOf { it.totalAscent }
                    }
                }

                DiaryCalendarTotalValues(
                    duration = workoutHeaders.sumOf { it.totalTime },
                    distance = workoutHeaders.sumOf { it.totalDistance },
                    ascent = ascent,
                    energy = workoutHeaders.sumOf { it.energyConsumption },
                    activityCount = workoutHeaders.size,
                    workoutIdsToKeys = workoutIdsToKeys,
                    diveCount = diveCount,
                    maxDepth = maxDepth
                )
            }
    }

    private fun getLocations(
        workouts: List<WorkoutHeader>
    ): List<LocationWithActivityType> {
        return workouts.mapNotNull {
            if (it.startPosition != null && !it.startPosition.isOrigin) {
                LocationWithActivityType(
                    startTime = it.startTime,
                    latitude = it.startPosition.latitude,
                    longitude = it.startPosition.longitude,
                    activityType = it.activityTypeId,
                    polyline = it.polyline
                )
            } else {
                null
            }
        }
    }

    class Params(
        val username: String,
        val firstDay: LocalDate,
        val endDay: LocalDate,
        val loadMenstrualCycles: Boolean,
        val loadPredictedMenstrualCycles: Boolean,
        val loadWorkouts: Boolean,
    )
}

private fun List<WorkoutHeader>.totalDurationMillis(): Long =
    sumOf { it.totalTime * 1000.0 }.roundToLong() // From seconds to millis

private fun LocalDate.toPeriodStartMillis(): Long = this
    .atStartOfDay()
    .atZone(ZoneOffset.systemDefault())
    .toInstant()
    .toEpochMilli()

private fun LocalDate.toPeriodEndMillis(): Long = this
    .plusDays(1)
    .atStartOfDay()
    .atZone(ZoneOffset.systemDefault())
    .toInstant()
    .toEpochMilli()
