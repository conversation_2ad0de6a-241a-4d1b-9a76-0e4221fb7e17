package com.stt.android.domain.workouts.extensions.intensity

import com.soy.algorithms.impact.WorkoutImpact
import com.stt.android.domain.trenddata.StepsLocalDataSource
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import java.time.LocalDate
import javax.inject.Inject

class DayCategorisationUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource,
    private val stepsLocalDataSource: StepsLocalDataSource,
    private val workoutImpactUseCase: WorkoutImpactUseCase
) {

    suspend fun getDayCategorization(username: String, day: LocalDate): DayCategorisation = withContext(IO) {
        val fromEpochMillis = day.atStartOfDay().toEpochMilli()
        val toEpochMillis = day.atEndOfDay().toEpochMilli()
        val workouts = workoutDataSource.fetchUserWorkoutsBasic(
            username = username,
            minStartTime = fromEpochMillis,
            maxStartTime = toEpochMillis,
        )
        val workoutImpacts: List<WorkoutImpact> =
            workouts.map {
                workoutImpactUseCase.calculateWorkoutImpacts(it)
            }.flatten()
        val steps = stepsLocalDataSource.countStepsBetween(
            fromTimestamp = fromEpochMillis,
            toTimestamp = toEpochMillis
        )
        when {
            workouts.isEmpty() && steps < 5000 -> DayCategorisation.REST_DAY
            workouts.isEmpty() -> DayCategorisation.ACTIVE_DAY
            workoutImpacts.all { it == WorkoutImpact.EASY_RECOVERY } -> DayCategorisation.RECOVERY_DAY
            workoutImpacts.any { it == WorkoutImpact.LONG_AEROBIC_BASE } -> DayCategorisation.LONG_TRAINING_DAY
            workoutImpacts.any {
                it == WorkoutImpact.ANAEROBIC_THRESHOLD ||
                    it == WorkoutImpact.ABOVE_THRESHOLD_VO2MAX
            } -> DayCategorisation.HIGH_INTENSITY_TRAINING_DAY
            else -> DayCategorisation.TRAINING_DAY
        }
    }
}
