package com.stt.android.domain.workouts.extensions.intensity

import com.soy.algorithms.intensity.IntensityZonesData

data class IntensityDistribution(
    val hrZones: ZoneDuration = ZoneDuration(),
    val speedZones: ZoneDuration = ZoneDuration(),
    val runningPowerZones: ZoneDuration = ZoneDuration(),
    val cyclingPowerZones: ZoneDuration = ZoneDuration(),
) {
    /**
     * Durations in seconds
     */
    data class ZoneDuration(
        val zone1: Float = 0f,
        val zone2: Float = 0f,
        val zone3: Float = 0f,
        val zone4: Float = 0f,
        val zone5: Float = 0f,
    ) {
        operator fun plus(zones: IntensityZonesData?): ZoneDuration =
            ZoneDuration(
                zone1 = zone1 + (zones?.zone1Duration ?: 0f),
                zone2 = zone2 + (zones?.zone2Duration ?: 0f),
                zone3 = zone3 + (zones?.zone3Duration ?: 0f),
                zone4 = zone4 + (zones?.zone4Duration ?: 0f),
                zone5 = zone5 + (zones?.zone5Duration ?: 0f),
            )

        operator fun div(value: Float): ZoneDuration =
            ZoneDuration(
                zone1 = zone1 / value,
                zone2 = zone2 / value,
                zone3 = zone3 / value,
                zone4 = zone4 / value,
                zone5 = zone5 / value,
            )

        operator fun get(index: Int): Float = when (index) {
            1 -> zone1
            2 -> zone2
            3 -> zone3
            4 -> zone4
            5 -> zone5
            else -> throw IndexOutOfBoundsException("$index is not in 1..5 range")
        }

        override fun toString(): String = """
            zone5=$zone5,
            zone4=$zone4,
            zone3=$zone3,
            zone2=$zone2,
            zone1=$zone1,
        """.trimIndent()

        fun maxZoneValue(): Float = maxOf(zone1, zone2, zone3, zone4, zone5)
    }
}
