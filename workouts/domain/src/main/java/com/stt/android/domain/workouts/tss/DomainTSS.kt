package com.stt.android.domain.workouts.tss

import android.os.Parcelable
import com.soy.algorithms.tss.TSSCalculationMethod
import kotlinx.parcelize.Parcelize

@Parcelize
data class TSS(
    val trainingStressScore: Float,
    val calculationMethod: TSSCalculationMethod,
    val intensityFactor: Float? = null,
    val normalizedPower: Float? = null,
    val averageGradeAdjustedPace: Float? = null,
) : Parcelable
