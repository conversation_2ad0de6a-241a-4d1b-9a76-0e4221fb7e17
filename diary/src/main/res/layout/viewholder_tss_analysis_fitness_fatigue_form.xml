<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="fitnessValue"
            type="int" />

        <variable
            name="fatigueValue"
            type="int" />

        <variable
            name="formValue"
            type="int" />

        <variable
            name="openExplanationsHandler"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{openExplanationsHandler}"
        android:background="?attr/selectableItemBackground">

        <TextView
            android:id="@+id/tss_analysis_fitness_value"
            style="@style/Body.XLarge.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="0dp"
            android:text="@{Integer.valueOf(fitnessValue).toString()}"
            android:textColor="@color/fitness_value_text_color"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33"
            tools:text="46" />

        <TextView
            android:id="@+id/tss_analysis_fitness_label"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_large"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="0dp"
            android:text="@string/tss_fitness_ctl_label"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tss_analysis_fitness_value"
            app:layout_constraintStart_toStartOf="@id/tss_analysis_fitness_value"
            app:layout_constraintTop_toBottomOf="@id/tss_analysis_fitness_value" />

        <View
            android:id="@+id/tss_analysis_fitness_divider"
            android:layout_width="@dimen/size_divider"
            android:layout_height="0dp"
            android:background="@color/suunto_light_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tss_analysis_fitness_value"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tss_analysis_fatigue_value"
            style="@style/Body.XLarge.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="0dp"
            android:text="@{Integer.valueOf(fatigueValue).toString()}"
            android:textColor="@color/fatigue_value_text_color"
            app:layout_constraintStart_toEndOf="@id/tss_analysis_fitness_divider"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33"
            tools:text="53" />

        <TextView
            android:id="@+id/tss_analysis_fatigue_label"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_large"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="0dp"
            android:text="@string/tss_fatigue_atl_label"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tss_analysis_fatigue_value"
            app:layout_constraintStart_toStartOf="@id/tss_analysis_fatigue_value"
            app:layout_constraintTop_toBottomOf="@id/tss_analysis_fatigue_value" />

        <View
            android:id="@+id/tss_analysis_fatigue_divider"
            android:layout_width="@dimen/size_divider"
            android:layout_height="0dp"
            android:background="@color/suunto_light_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/tss_analysis_fatigue_value"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tss_analysis_form_value"
            style="@style/Body.XLarge.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_xsmall"
            android:paddingEnd="0dp"
            android:text="@{Integer.valueOf(formValue).toString()}"
            android:textColor="@color/form_value_text_color"
            app:layout_constraintStart_toEndOf="@id/tss_analysis_fatigue_divider"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.33"
            tools:text="-6"
            tools:textColor="@color/form_maintaining_fitness" />

        <TextView
            android:id="@+id/tss_analysis_form_label"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_large"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="0dp"
            android:text="@string/tss_form_tsb_label"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tss_analysis_form_value"
            app:layout_constraintStart_toStartOf="@id/tss_analysis_form_value"
            app:layout_constraintTop_toBottomOf="@id/tss_analysis_form_value" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
