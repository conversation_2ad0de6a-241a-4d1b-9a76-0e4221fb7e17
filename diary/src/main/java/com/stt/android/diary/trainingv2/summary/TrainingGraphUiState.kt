package com.stt.android.diary.trainingv2.summary

import androidx.annotation.StringRes
import com.stt.android.diary.trainingv2.TrainingDateRange

data class TrainingGraphUiState(
    val graphPages: List<TrainingGraphPage>,
    val graphPageIndex: Int,
    val primaryGraphType: TrainingGraphType,
    @StringRes val primaryGraphUnitResId: Int?,
    val secondaryGraphType: TrainingGraphType,
    @StringRes val secondaryGraphUnitResId: Int?,
    val primaryGraphTypes: List<TrainingGraphType>,
    val secondaryGraphTypes: List<TrainingGraphType>,
    val entryYFormatter: TrainingGraphEntryYFormatter,
)

data class TrainingGraphPage(
    val countWorkouts: Int,
    val trainingDateRange: TrainingDateRange?,
    val chartData: TrainingChartData?,
)
