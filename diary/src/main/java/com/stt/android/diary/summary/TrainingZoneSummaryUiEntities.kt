package com.stt.android.diary.summary

import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.runtime.Immutable
import com.squareup.moshi.JsonClass
import com.squareup.moshi.adapters.PolymorphicJsonAdapterFactory
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.core.domain.workouts.MINIMUM_COUNT_TO_SHOW_GROUP
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.parcelize.Parcelize
import java.time.LocalDate

@Immutable
data class BasicWorkoutValue(
    val value: String? = null,
    @StringRes val unitRes: Int? = null
)

@Immutable
data class SummaryTableViewStateNew(
    val columns: ImmutableList<TrainingZoneSummaryColumn> = persistentListOf(),
    val rows: ImmutableList<TableRowItemUiState>,
    val showNoDataForSelectedFilter: Boolean = false,
    val maxValueLength: MaxValueLength = MaxValueLength(),
) {
    val hasTotalsRowItem = rows.any { it.isTotal }
}

@Immutable
data class TableRowItem(
    val showYear: Boolean,
    val year: Int,
    val formattedDate: String,
    val timestamp: Long?,
    val numberOfWorkouts: Int,
    val showActivityType: Boolean,
    val activityIconRes: Int,
    val activityColorRes: Int,
    val totalDuration: Double,
    val totalDistance: Double?,
    val avgSpeed: Double?,
    val avgPace: Double?,
    val heartRateAverage: Double?,
    val totalAscent: Double?,
    val tss: Float?,
    val energyConsumption: Int?, // kCal
    val estVo2Peak: Float?,
    val workouts: ImmutableList<SummaryWorkoutHeader>,
    val isTotal: Boolean,
    // This is only used when the grouping is By activity, so we can do some diving specific formatting, like DIVETIME
    val isDiving: Boolean = false,
    // Cycling
    val avgPower: Double? = null,
    val normalizedPower: Double? = null,
    // Swimming
    val avgSwimPace: Double?,

) {
    val isEmpty: Boolean = numberOfWorkouts == 0
}

@Immutable
data class TableRowItemUiState(
    val showYear: Boolean,
    val year: Int,
    val date: String,
    val numberOfWorkouts: Int,
    val showActivityType: Boolean,
    val activityIconRes: Int,
    val activityColorRes: Int,
    val totalDuration: BasicWorkoutValue?,
    val totalDistance: BasicWorkoutValue?,
    val avgSpeed: BasicWorkoutValue?,
    val avgPace: BasicWorkoutValue?,
    val heartRateAverage: BasicWorkoutValue?,
    val totalAscent: BasicWorkoutValue?,
    val tss: BasicWorkoutValue?,
    val energyConsumption: BasicWorkoutValue?, // kcal
    val estVo2Peak: BasicWorkoutValue?,
    val workouts: ImmutableList<SummaryWorkoutHeader>,
    val isTotal: Boolean,
    val rowCount: Int = 1, // This will indicate how many rows are grouped
    val avgPower: BasicWorkoutValue?,
    val normalizedPower: BasicWorkoutValue?,
    val avgSwimPace: BasicWorkoutValue?,
) {
    val isEmpty: Boolean = numberOfWorkouts == 0

    companion object {
        fun empty(rowCount: Int): TableRowItemUiState {
            return TableRowItemUiState(
                showYear = false,
                year = 0,
                date = "",
                numberOfWorkouts = 0,
                showActivityType = false,
                activityIconRes = 0,
                activityColorRes = 0,
                workouts = persistentListOf(),
                isTotal = false,
                rowCount = rowCount,
                totalDuration = null,
                totalDistance = null,
                avgSpeed = null,
                avgPace = null,
                heartRateAverage = null,
                totalAscent = null,
                tss = null,
                energyConsumption = null,
                estVo2Peak = null,
                avgPower = null,
                normalizedPower = null,
                avgSwimPace = null,
            )
        }
    }
}

@Immutable
data class SummaryDetailsViewState(
    val numberOfWorkouts: Int = 0,
    val columns: ImmutableList<TrainingZoneSummaryColumn> = persistentListOf(),
    val rows: ImmutableList<TableRowItemUiState> = persistentListOf(),
    val selectedDate: String = "",
    val maxValueLength: MaxValueLength = MaxValueLength(),
) {
    val hasTotalsRowItem = rows.any { it.isTotal }
}

// This is used to hold the max value in term of length of characters that is presented in a cell
// The length is used to dynamically calculate the width of the cell
data class MaxValueLength(
    val maxDate: String = "",
    val maxNumberOfWorkouts: String = "",
    val maxTotalDuration: String = "",
    val maxTotalDistance: String = "",
    val maxAvgSpeed: String = "",
    val maxAvgPace: String = "",
    val maxHeartRateAverage: String = "",
    val maxTotalAscent: String = "",
    val maxTss: String = "",
    val maxEnergyConsumption: String = "",
    val maxEstVo2Peak: String = "",
    val maxAvgPower: String = "",
    val maxNormalizedPower: String = "",
    val maxAvgSwimPace: String = "",
    val maxRowCount: Int = 1
)

enum class DateSelectionType {
    START,
    END
}

enum class TrainingZoneSummaryFilterCategory() {
    GROUP_BY,
    DATE_RANGE,
    TAGS,
    DISTANCE,
    SPORTS,
    ;
}

enum class TrainingSummaryGraphSheetCategory() {
    SPORTS,
    PRIMARY_GRAPH_TYPE,
    SECONDARY_GRAPH_TYPE,
    ;
}

@Immutable
data class TrainingZoneSummaryFilterUiState(
    val grouping: TrainingZoneSummaryGrouping,
    val sports: ImmutableList<CoreActivityType>,
    val summarySuuntoTags: ImmutableList<SummaryTag.SummarySuuntoTag>,
    val summaryUserTags: ImmutableList<SummaryTag.SummaryUserTag>,
    val selectedStartDateMillis: Long?,
    val selectedEndDateMillis: Long?,
    val distance: DistanceUiState = DistanceUiState(
        minDistance = DistanceUiState.RangeValue.None,
        maxDistance = DistanceUiState.RangeValue.None
    ),
    val distanceUnit: Int,
    val showEmptyRowsChecked: Boolean
) {
    val isDistanceSupported: Boolean = sports.isEmpty() || sports.any { it.hasDistance }
    val suuntoTags = summarySuuntoTags.map { it.suuntoTag }.toImmutableList()
    val userTags = summaryUserTags.map { it.userTag }.toImmutableList()

    // This is used to show grouped sports for example "All running" instead of "RUNNING", "TRAIL_RUNNING", "TREADMILL"
    val sportGrouping: ImmutableList<CoreActivityGrouping> = mutableListOf<CoreActivityGrouping>().apply {
        fun addGroupIfContains(group: CoreActivityGrouping) {
            if (sports.intersect(group.activityTypes).size >= MINIMUM_COUNT_TO_SHOW_GROUP) {
                add(group)
            } else {
                addAll(sports.filter { it in group.activityTypes })
            }
        }
        CoreActivityGroup.entries.forEach {
            addGroupIfContains(it)
        }
        val allActivityTypesForGroups = CoreActivityGroup.entries.flatMap { it.activityTypes }.toSet()
        addAll(sports.filter { it !in allActivityTypesForGroups })
    }.toImmutableList()
}

enum class TrainingZoneSummarySortingOrder {
    DESCENDING,
    ASCENDING
}

@Immutable
data class TrainingZoneSummarySortingUiState(
    val selectedColumn: TrainingZoneSummaryColumn,
    val selectedOrder: TrainingZoneSummarySortingOrder
)

@Parcelize
@Immutable
data class DistanceUiState(
    val minDistance: RangeValue,
    val maxDistance: RangeValue
) : Parcelable {
    @Parcelize
    sealed class RangeValue : Parcelable {
        data object None : RangeValue()
        data class Exact(val value: Int) : RangeValue()
        data object Overflow : RangeValue()
    }

    companion object {
        fun fromFilterRange(minDistance: Float, maxDistance: Float): DistanceUiState {
            val minDistanceAsInt = minDistance.toInt()
            val maxDistanceAsInt = maxDistance.toInt()
            return DistanceUiState(
                minDistance = progressToValue(minDistanceAsInt),
                maxDistance = progressToValue(maxDistanceAsInt)
            )
        }

        fun toFilterRange(distance: DistanceUiState): Pair<Float, Float> {
            return valueToProgress(distance.minDistance).coerceAtLeast(SummaryDistanceFilterRange.start) to
                valueToProgress(distance.maxDistance).coerceAtMost(SummaryDistanceFilterRange.endInclusive)
        }

        private fun valueToProgress(rangeValue: RangeValue): Float {
            // This is the reverse of progressToValue()
            return when (rangeValue) {
                RangeValue.None -> 0f
                is RangeValue.Exact -> {
                    if (rangeValue.value <= 30) {
                        rangeValue.value.toFloat()
                    } else {
                        (rangeValue.value / 5 + 24).toFloat()
                    }
                }

                is RangeValue.Overflow -> SummaryDistanceFilterRange.endInclusive
            }
        }

        private fun progressToValue(progress: Int): RangeValue {
            // from 0 to 30 we increment by 1
            // from 30 to 100 we increment by 5
            // after 100, we show 100+
            if (progress == 0) return RangeValue.None

            return if (progress <= 30) {
                RangeValue.Exact(progress)
            } else if (progress <= (SummaryDistanceFilterRange.endInclusive - 1)) {
                RangeValue.Exact((30 + (progress - 30) * 5))
            } else {
                RangeValue.Overflow
            }
        }
    }
}


@Parcelize
sealed class SummaryTag(open val timestamp: Long): Parcelable {
    @JsonClass(generateAdapter = true)
    data class SummarySuuntoTag(
        val suuntoTag: SuuntoTag,
        override val timestamp: Long,
    ) : SummaryTag(timestamp)

    @JsonClass(generateAdapter = true)
    data class SummaryUserTag(
        val userTag: UserTag,
        override val timestamp: Long
    ) : SummaryTag(timestamp)

    companion object {
        val jsonAdapterFactory : PolymorphicJsonAdapterFactory<SummaryTag> = PolymorphicJsonAdapterFactory.of(SummaryTag::class.java, "SummaryTag")
            .withSubtype(SummarySuuntoTag::class.java, "SummarySuuntoTag")
            .withSubtype(SummaryUserTag::class.java, "SummaryUserTag")
    }
}

data class EditableSummaryColumn(
    val column: TrainingZoneSummaryColumn,
    val hidden: Boolean,
)

data class TimeFrameSummaryWorkout(
    val timeRange: GraphTimeRange,
    val timeFrame: ClosedRange<LocalDate>,
    val trainingDateRange: TrainingDateRange,
    val workouts: List<SummaryWorkoutHeader>,
)
