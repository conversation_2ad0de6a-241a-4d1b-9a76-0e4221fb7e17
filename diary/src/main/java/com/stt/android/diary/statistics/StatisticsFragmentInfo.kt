package com.stt.android.diary.statistics

import androidx.fragment.app.Fragment
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.diary.DiaryFragmentInfo
import com.stt.android.home.diary.R
import javax.inject.Inject

class StatisticsFragmentInfo @Inject constructor() : DiaryFragmentInfo {
    override fun clazz(): Class<out Fragment> = StatisticsFragment::class.java
    override fun getTitleResId(): Int = R.string.statistics_tab_title
    override fun getTabTypeForAnalytics(): String =
        AnalyticsPropertyValue.SuuntoDiaryType.STATISTICS
}
