package com.stt.android.diary.summary

import kotlinx.collections.immutable.persistentListOf
import javax.inject.Inject

class TrainingZoneSummaryGetRowsUseCase @Inject constructor(
    private val formatter: TrainingZoneSummaryFormatter
) {
    companion object {
        internal const val MINIMUM_EMPTY_ROWS_TO_GROUP = 4
    }
    operator fun invoke(
        workouts: List<TableRowItem>,
        total: TableRowItem?,
    ): List<TableRowItemUiState> {
        return run {
            if (total != null) {
                listOf(total)
            } else {
                emptyList()
            } + workouts
        }.toTableRowItemUiState(formatter = formatter)
    }

    private fun TableRowItem.toTableRowItemUiState(
        formatter: TrainingZoneSummaryFormatter,
    ): TableRowItemUiState {
        return if (isEmpty) {
            TableRowItemUiState(
                showYear = showYear,
                year = year,
                date = formattedDate,
                numberOfWorkouts = 0,
                showActivityType = showActivityType,
                activityIconRes = activityIconRes,
                activityColorRes = activityColorRes,
                totalDuration = null,
                totalDistance = null,
                avgSpeed = null,
                avgPace = null,
                heartRateAverage = null,
                totalAscent = null,
                tss = null,
                energyConsumption = null,
                estVo2Peak = null,
                workouts = persistentListOf(),
                isTotal = isTotal,
                avgPower = null,
                normalizedPower = null,
                avgSwimPace = null,
            )
        } else {
            TableRowItemUiState(
                showYear = showYear,
                year = year,
                date = formattedDate,
                numberOfWorkouts = numberOfWorkouts,
                showActivityType = showActivityType,
                activityIconRes = activityIconRes,
                activityColorRes = activityColorRes,
                totalDuration = if (isDiving) {
                    formatter.formatDiveTime(totalDuration)
                } else {
                    formatter.formatDuration(totalDuration)
                },
                totalDistance = formatter.formatDistance(totalDistance),
                avgSpeed = formatter.formatAvgSpeed(avgSpeed),
                avgPace = formatter.formatAvgPace(avgPace),
                heartRateAverage = formatter.formatAvgHeartRate(heartRateAverage),
                totalAscent = formatter.formatAscent(totalAscent),
                tss = formatter.formatTss(tss),
                energyConsumption = formatter.formatEnergy(energyConsumption),
                estVo2Peak = formatter.formatVo2Peak(estVo2Peak),
                workouts = workouts,
                isTotal = isTotal,
                avgPower = formatter.formatAvgPower(avgPower),
                normalizedPower = formatter.formatNormalizedPower(normalizedPower),
                avgSwimPace = formatter.formatAvgSwimPace(avgSwimPace),
            )
        }
    }

    private fun List<TableRowItem>.toTableRowItemUiState(
        formatter: TrainingZoneSummaryFormatter,
    ): List<TableRowItemUiState> {

        val result = mutableListOf<TableRowItemUiState>()
        val consecutiveEmptyRows = mutableListOf<TableRowItem>()

        fun addEmptyRows() {
            if (consecutiveEmptyRows.size >= MINIMUM_EMPTY_ROWS_TO_GROUP) {
                result.add(TableRowItemUiState.empty(rowCount = consecutiveEmptyRows.size))
            } else {
                for (emptyTableRowItem in consecutiveEmptyRows) {
                    result.add(emptyTableRowItem.toTableRowItemUiState(formatter))
                }
            }
        }
        for (tableRowItem in this) {
            if (tableRowItem.isEmpty) {
                consecutiveEmptyRows.add(tableRowItem)
            } else {
                addEmptyRows()
                result.add(tableRowItem.toTableRowItemUiState(formatter))
                consecutiveEmptyRows.clear()
            }
        }

        if (consecutiveEmptyRows.isNotEmpty()) {
            addEmptyRows()
        }

        return result
    }
}
