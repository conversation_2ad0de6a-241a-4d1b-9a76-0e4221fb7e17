package com.stt.android.diary.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.chart.impl.R
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.GenericSegmentedControl
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.extensions.shortStringRes
import com.stt.android.extensions.stringRes

@Composable
internal fun TimeRangeSegmentedControl(
    timeRange: GraphTimeRange,
    timeRanges: List<GraphTimeRange>,
    moreTimeRanges: List<GraphTimeRange>,
    onTimeRangeToggled: (GraphTimeRange) -> Unit,
    modifier: Modifier = Modifier,
) = GenericSegmentedControl(
    modifier = modifier,
    segment = timeRange,
    segments = timeRanges,
    moreSegments = moreTimeRanges,
    onSegmentToggled = onTimeRangeToggled,
    shortName = { stringResource(shortStringRes) },
    longName = { stringResource(stringRes) },
    description = { stringResource(R.string.chart_granularity_daily_interval) },
    bottomSheetTitle = {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                text = stringResource(R.string.chart_granularity_time_range_title),
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyXLargeBold,
            )
            Text(
                text = stringResource(R.string.chart_granularity_time_range_desc),
                color = MaterialTheme.colorScheme.secondary,
                style = MaterialTheme.typography.body,
            )
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
    },
)
