package com.stt.android.diary.progress

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.key
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.tss.TSSAnalysisFitnessFatigueChart
import com.stt.android.diary.tss.TSSAnalysisFormChart
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.R
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.drop

internal fun LazyListScope.tssAnalysisFitnessCharts(
    timeRange: GraphTimeRange,
    fitnessViewDataMap: Map<Int, StateFlow<FitnessViewData>>,
    fitnessPageIndex: Int,
    fitnessPageCount: Int,
    fitnessPageStartDate: String,
    fitnessPageEndDate: String,
    onFitnessPageUpdated: (Int) -> Unit,
    highlightedIndex: Int?,
    onHighlighted: (page: Int, index: Int?) -> Unit,
    onPositionInRootYChanged: (Int) -> Unit,
) {
    item(key = "tss_analysis_fitness_fatigue_chart") {
        val chartTopOffset = with(LocalDensity.current) { 44.dp.roundToPx() }
        ChartSection(
            title = buildString {
                append(stringResource(R.string.progress_fitness_and_fatigue))
                append(" ")
                append(fitnessPageStartDate)
                append(" - ")
                append(fitnessPageEndDate)
            },
        )
        key(timeRange) {
            val pagerState = rememberPagerState(initialPage = fitnessPageIndex) { fitnessPageCount }
            HorizontalPager(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.dp),
                state = pagerState,
            ) { page ->
                val viewData = fitnessViewDataMap[page]?.collectAsState()?.value
                if (viewData is FitnessViewData.Loaded) {
                    val isCurrent = page == fitnessPageIndex
                    TSSAnalysisFitnessFatigueChartWrapper(
                        modifier = Modifier
                            .onLayoutRectChanged(debounceMillis = 0L) {
                                onPositionInRootYChanged(it.positionInRoot.y + chartTopOffset)
                            }
                            .fillMaxSize(),
                        viewData = viewData,
                        highlightedIndex = if (isCurrent) highlightedIndex else null,
                        onHighlighted = {
                            if (isCurrent) {
                                onHighlighted(page, it)
                            }
                        },
                    )
                }
            }
            LaunchedEffect(Unit) {
                pagerState.scrollToPage(fitnessPageIndex)
            }
            LaunchedEffect(Unit) {
                snapshotFlow { pagerState.currentPage }
                    .drop(1)
                    .collect { onFitnessPageUpdated(it) }
            }
            LaunchedEffect(fitnessPageIndex) {
                if (fitnessPageIndex != pagerState.currentPage) {
                    pagerState.animateScrollToPage(fitnessPageIndex)
                }
            }
        }
    }
    item(key = "tss_analysis_form_chart") {
        ChartSection(title = stringResource(R.string.tss_form_tss_d_label))
        key(timeRange) {
            val pagerState = rememberPagerState(initialPage = fitnessPageIndex) { fitnessPageCount }
            HorizontalPager(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.dp),
                state = pagerState,
            ) { page ->
                val viewData = fitnessViewDataMap[page]?.collectAsState()?.value
                if (viewData is FitnessViewData.Loaded) {
                    val isCurrent = page == fitnessPageIndex
                    TSSAnalysisFormChartWrapper(
                        modifier = Modifier.fillMaxSize(),
                        viewData = viewData,
                        highlightedIndex = if (isCurrent) highlightedIndex else null,
                        onHighlighted = {
                            if (isCurrent) {
                                onHighlighted(page, it)
                            }
                        },
                    )
                }
            }
            LaunchedEffect(Unit) {
                pagerState.scrollToPage(fitnessPageIndex)
            }
            LaunchedEffect(Unit) {
                snapshotFlow { pagerState.currentPage }
                    .drop(1)
                    .collect { onFitnessPageUpdated(it) }
            }
            LaunchedEffect(fitnessPageIndex) {
                if (fitnessPageIndex != pagerState.currentPage) {
                    pagerState.animateScrollToPage(fitnessPageIndex)
                }
            }
        }
    }
}

@Composable
private fun ChartSection(
    title: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.small,
        ),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = title,
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colorScheme.onSurface,
        )
        Text(
            text = stringResource(R.string.tss_graph_y_axis_label),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}

@Composable
private fun TSSAnalysisFitnessFatigueChartWrapper(
    viewData: FitnessViewData.Loaded,
    highlightedIndex: Int?,
    onHighlighted: (Int?) -> Unit,
    modifier: Modifier = Modifier,
) {
    AndroidView(
        factory = { context -> TSSAnalysisFitnessFatigueChart(context) },
        update = { chart ->
            chart.apply {
                newProgressStyle = true
                suuntoApp = viewData.isSuuntoApp
                chartStartDate = viewData.startDate
                endDate = viewData.endDate
                weekFields = viewData.calendarProvider.getWeekFields()
                fitnessValues = viewData.fitnessValues
                fatigueValues = viewData.fatigueValues
                yAxisMax = viewData.fitnessFatigueGraphMaxY
                indexToHighlight = highlightedIndex
                timeRange = viewData.timeRange
                trendsAnalytics = viewData.trendsAnalytics
                onValueHighlighted = onHighlighted
                setup()
            }
        },
        modifier = modifier,
    )
}

@Composable
private fun TSSAnalysisFormChartWrapper(
    viewData: FitnessViewData.Loaded,
    highlightedIndex: Int?,
    onHighlighted: (Int?) -> Unit,
    modifier: Modifier = Modifier,
) {
    AndroidView(
        factory = { context -> TSSAnalysisFormChart(context) },
        update = { chart ->
            chart.apply {
                newProgressStyle = true
                chartStartDate = viewData.startDate
                endDate = viewData.endDate
                weekFields = viewData.calendarProvider.getWeekFields()
                formValues = viewData.formValues
                yAxisMin = viewData.formGraphMinY
                yAxisMax = viewData.formGraphMaxY
                currentPhase = viewData.currentFormPhase
                indexToHighlight = highlightedIndex
                timeRange = viewData.timeRange
                trendsAnalytics = viewData.trendsAnalytics
                onValueHighlighted = onHighlighted
                setup()
            }
        },
        modifier = modifier,
    )
}
