package com.stt.android.diary.progress

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.LocalContentColor
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.diary.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun Vo2MaxInfoBottomSheet(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ModalBottomSheet(
        modifier = modifier,
        onDismissRequest = onDismiss,
        dragHandle = {
            BottomSheetDefaults.DragHandle(
                color = MaterialTheme.colorScheme.secondary.copy(alpha = 0.4f),
            )
        },
        containerColor = MaterialTheme.colorScheme.surface,
    ) {
        Vo2MaxInfoContent()
    }
}

@Composable
private fun Vo2MaxInfoContent(
    modifier: Modifier = Modifier,
) = Column(
    modifier = modifier
        .verticalScroll(rememberScrollState())
        .fillMaxWidth()
        .padding(
            start = MaterialTheme.spacing.medium,
            end = MaterialTheme.spacing.medium,
            bottom = MaterialTheme.spacing.xxlarge,
        ),
) {
    CompositionLocalProvider(LocalContentColor provides MaterialTheme.colorScheme.onSurface) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_what_is_vo2max_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_what_is_vo2max_description),
            style = MaterialTheme.typography.bodyLarge,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Text(
            text = stringResource(R.string.progress_how_to_measure_vo2max_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_how_to_measure_vo2max_description),
            style = MaterialTheme.typography.bodyLarge,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subtitle_1),
            style = MaterialTheme.typography.bodyLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subdescription_1),
            style = MaterialTheme.typography.bodyLarge,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subtitle_2),
            style = MaterialTheme.typography.bodyLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subdescription_2),
            style = MaterialTheme.typography.bodyLarge,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subtitle_3),
            style = MaterialTheme.typography.bodyLargeBold,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.progress_how_to_use_vo2max_subdescription_3),
            style = MaterialTheme.typography.bodyLarge,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
    }
}
