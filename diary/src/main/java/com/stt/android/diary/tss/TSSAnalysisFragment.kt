package com.stt.android.diary.tss

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.R
import com.stt.android.home.diary.databinding.FragmentTssAnalysisBinding
import com.stt.android.premium.PremiumRequiredToAccessHandler
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class TSSAnalysisFragment : ViewStateListFragment2<TSSAnalysisData, TSSAnalysisViewModel>() {
    override val viewModel: TSSAnalysisViewModel by viewModels()
    override val layoutId = R.layout.fragment_tss_analysis

    private val binding: FragmentTssAnalysisBinding get() = requireBinding()

    @Inject
    lateinit var premiumRequiredToAccessHandler: PremiumRequiredToAccessHandler

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        premiumRequiredToAccessHandler.onCreate(this)
        premiumRequiredToAccessHandler.startCheckingForPremiumAccess(
            viewLifecycleOwner = viewLifecycleOwner,
            containerView = view as ViewGroup,
            description = getString(R.string.buy_premium_popup_tss_analysis_description),
            analyticsSource = AnalyticsPropertyValue.BuyPremiumPopupShownSource.TSS_ANALYSIS,
            analyticsReason = null
        )

        viewModel.loadData(
            viewModel.selectedGraphTimeRange.value ?: GraphTimeRange.entries.first()
        )
        viewModel.selectedGraphTimeRange.observeNotNull(viewLifecycleOwner) {
            viewModel.loadData(it)
        }

        viewModel.openExplanationsEvent.observeK(viewLifecycleOwner) {
            showValueExplanationDialog()
        }

        viewModel.showReadMoreBottomSheet.observeK(viewLifecycleOwner) {
            TSSAnalysisReadMoreBottomSheet.newInstance()
                .show(childFragmentManager, TSSAnalysisReadMoreBottomSheet.TAG)
        }

        viewModel.showVo2MaxBottomSheet.observeK(viewLifecycleOwner) {
            TSSAnalysisVo2MaxBottomSheet.newInstance()
                .show(childFragmentManager, TSSAnalysisVo2MaxBottomSheet.TAG)
        }

        viewModel.highlightedValue.observeK(viewLifecycleOwner) {
            if (it != null) {
                updateInfoPanelPosition(getGraphTitleCenterYPosition() ?: 0f)
                showInfoPanel(it)
            } else {
                clearInfoPanel()
            }
        }

        binding.list.addOnScrollListener(scrollListener)

        val divider = resources.getDimensionPixelSize(R.dimen.top_bottom_divider)
        val dividerColor = resources.getColor(BaseR.color.light_grey, requireTheme())
        binding.list.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(
                dividerColor = dividerColor
            ) { item: Any?,
                nextItem: Any? ->
                if (item == null || nextItem == null) {
                    divider
                } else {
                    null
                }
            }
        )
    }

    override fun onDestroyView() {
        binding.list.removeOnScrollListener(scrollListener)
        super.onDestroyView()
    }

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            clearInfoPanel()
        }
    }

    private fun showValueExplanationDialog() {
        val dialog = TSSValueExplanationsBottomSheetFragment()
        dialog.show(parentFragmentManager, TSSValueExplanationsBottomSheetFragment.FRAGMENT_TAG)
    }

    private fun getGraphTitleCenterYPosition(): Float? =
        binding.list.layoutManager?.findViewByPosition(GRAPH_TITLE_VIEW_LIST_POSITION)?.run {
            (top.toFloat() + bottom.toFloat()) / 2f
        }

    private fun showInfoPanel(point: TSSHighlightedPoint) =
        binding.tssMarkerInfoPanel.setPoint(point)

    private fun clearInfoPanel() = binding.tssMarkerInfoPanel.clear()

    private fun updateInfoPanelPosition(yOffset: Float) {
        binding.tssMarkerInfoPanel.translationY = yOffset
    }

    companion object {
        // Assume that the list content is static and item at this index can be used to anchor
        // the info panel.
        private const val GRAPH_TITLE_VIEW_LIST_POSITION = 2

        fun newInstance() = TSSAnalysisFragment()
    }
}
