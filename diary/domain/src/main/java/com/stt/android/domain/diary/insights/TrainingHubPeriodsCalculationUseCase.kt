package com.stt.android.domain.diary.insights

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

class TrainingHubPeriodsCalculationUseCase @Inject constructor() {

    operator fun invoke(
        weeksToAdd: Int,
        firstDayOfWeek: DayOfWeek
    ): TrainingHubPeriodsCalculationUseCaseResult {
        val startDate = LocalDate.now()
            .with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            .plusWeeks(weeksToAdd.toLong())
        val (firstPeriodStartDate, firstPeriodEndDateInclusive) = calculateFirstPeriod(startDate)
        val (secondPeriodStartDate, secondPeriodEndDateInclusive) = calculateSecondPeriod(
            firstPeriodStartDate
        )
        return TrainingHubPeriodsCalculationUseCaseResult(
            firstPeriodStartDate = firstPeriodStartDate,
            firstPeriodEndDateInclusive = firstPeriodEndDateInclusive,
            secondPeriodStartDate = secondPeriodStartDate,
            secondPeriodEndDateInclusive = secondPeriodEndDateInclusive

        )
    }

    private fun calculateFirstPeriod(startDate: LocalDate): Pair<LocalDate, LocalDate> {
        return startDate to minOf(
            startDate.plusWeeks(1).minusDays(1),
            LocalDate.now()
        )
    }

    private fun calculateSecondPeriod(firstPeriodStartDate: LocalDate): Pair<LocalDate, LocalDate> {
        return firstPeriodStartDate.minusWeeks(6) to
            firstPeriodStartDate.minusDays(1)
    }
}

data class TrainingHubPeriodsCalculationUseCaseResult(
    val firstPeriodStartDate: LocalDate,
    val firstPeriodEndDateInclusive: LocalDate,
    val secondPeriodStartDate: LocalDate,
    val secondPeriodEndDateInclusive: LocalDate
)
