package com.stt.android.remote.workouts.update;


public enum SharingFlags {
    PRIVATE(0),
    FOLLOWERS(16),
    PUBLIC(2),
    UNKNOWN(-1);

    private final int flag;

    SharingFlags(int flag) {
        this.flag = flag;
    }

    public static SharingFlags valueOf(int position) {
        for (SharingFlags sharingOption : values()) {
            if (position == sharingOption.ordinal()) {
                return sharingOption;
            }
        }
        throw new IllegalArgumentException("Invalid SharingFlags ordinal value");
    }

    public int getFlag() {
        return flag;
    }
}
