package com.stt.android.remote.headsetneck

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path

interface NeckAssessmentApi {

    @POST("necks")
    suspend fun saveAssessmentValues(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Body assessmentValues: NeckRemoteAssessmentValues
    ): AskoResponse<NeckRemoteAssessmentValues>

    @GET("necks")
    suspend fun getNeckAssessmentValues(): AskoResponse<List<NeckRemoteAssessmentValues>>

    @DELETE("necks/{neckId}")
    suspend fun deleteNeckAssessment(@Path("neckId") id: String): AskoResponse<Boolean>
}
