package com.stt.android.remote.headsetjump

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path

interface JumpAssessmentApi {

    @POST("jumps")
    suspend fun saveJumpAssessmentValues(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Body jumpAssessmentRemoteValue: JumpAssessmentRemoteValue
    ): AskoResponse<CurrentJumpAssessmentResponseValue>

    @GET("jumps/standard")
    suspend fun getStandardAssessmentValue(): AskoResponse<JumpAssessmentBaselineResponseValue?>

    @GET("jumps")
    suspend fun getHistoryAssessmentValues(): AskoResponse<List<HistoryJumpAssessmentResponseValue>>

    @DELETE("jumps/{jumpId}")
    suspend fun deleteJumpData(@Path("jumpId") jumpId: String): AskoResponse<Any?>
}
