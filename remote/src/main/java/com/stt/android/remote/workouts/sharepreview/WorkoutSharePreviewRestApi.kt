package com.stt.android.remote.workouts.sharepreview

import retrofit2.http.GET
import retrofit2.http.Path

interface WorkoutSharePreviewRestApi {

    @GET("share/workoutmetadata/{username}/{workoutKey}/{brand}")
    suspend fun fetchWorkoutSharePreviewMetadata(
        @Path("username") username: String,
        @Path("workoutKey") workoutKey: String,
        @Path("brand") brand: String
    ): RemoteWorkoutSharePreviewMetadata
}
