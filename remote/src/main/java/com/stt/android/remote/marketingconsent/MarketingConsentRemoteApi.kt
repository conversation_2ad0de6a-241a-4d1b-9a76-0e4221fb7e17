package com.stt.android.remote.marketingconsent

import com.stt.android.TestOpen
import javax.inject.Inject

/**
 * Class responsible of making remote calls to MarketingConsent API Service
 */
@TestOpen
class MarketingConsentRemoteApi
@Inject constructor(
    private val marketingConsentRestApi: MarketingConsentRestApi
) {

    /**
     * Notifies the server that user has accepted marketing consent
     *
     *
     */
    suspend fun acceptMarketingConsent(marketingConsentInfo: MarketingConsentInfo) =
        marketingConsentRestApi.acceptMarketingConsent(marketingConsentInfo)
}
