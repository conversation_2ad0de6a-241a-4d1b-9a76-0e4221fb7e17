syntax = "proto3";

import "Wrappers.proto";

option java_package = "com.sportstracker.apiserver.domain.route.v2";
option java_outer_classname = "RouteEntitiesProtos";

enum RouteVisibility {
    NULL_VISIBILITY = 0;
    PRIVATE = 1;
    PUBLIC = 2;
    URL = 3;
}

enum SyncState {
    IGNORED = 0;
    PENDING = 1;
    SYNCING = 2;
    SYNCED = 3;
    ERROR = 4;
}

enum ConstraintWaypointType {
    NULL_TYPE = 0;
    BUILDING = 1;
    HOME = 2;
    CAR = 3;
    PARKING = 4;
    CAMP = 5;
    CAMPING = 6;
    FOOD = 7;
    RESTAURANT = 8;
    CAFE = 9;
    LODGING = 10;
    HOSTEL = 11;
    HOTEL = 12;
    WATER = 13;
    RIVER = 14;
    LAKE = 15;
    COAST = 16;
    MOUNTAIN = 17;
    HILL = 18;
    VALLEY = 19;
    CLIFF = 20;
    FOREST = 21;
    CROSSROADS = 22;
    SIGHT = 23;
    BEGIN = 24;
    END = 25;
    GEOCACHE = 26;
    WAYPOINT = 27;
    ROAD = 28;
    TRAIL = 29;
    ROCK = 30;
    MEADOW = 31;
    CAVE = 32;
    EMERGENCY = 33;
    INFO = 34;
    PEAK = 35;
    WATERFALL = 36;
    // Below types are not official and don't exists in SML.
    // Do not use except if you are a developer working with these features.
    // aligned with https://bitbucket.org/suunto/nextgen/src/develop/nea/wbresources/suunto_navigation.yaml
    // (See RoutePointType, id+1)
    FISHINGSPOT = 37;
    BEDDING = 38;
    PRINTS = 39;
    RUB = 40;
    SCRAPE = 41;
    STAND = 42;
    TRAILCAM = 43;
    BIGGAME = 44;
    SMALLGAME = 45;
    BIRD = 46;
    SHOT = 47;
    FISH = 48;
    // Added in June 2022
    BIGFISH = 65;
    CORALREEF = 66;
    BEACH = 67;
    MARINEMAMALS = 68;
    KELPFOREST = 69;
    WRECK = 71;
    MARINERESERVE = 72;
    AVALANCHE = 73;
    DANGER = 74;
    AIDSTATION = 75;
    WATERPOINT = 76;
    MUSHROOMS = 77;
    CAMPFIRE = 78;
    // Navigation
    LEFT_TURN = 49;
    RIGHT_TURN = 50;
    SHARP_LEFT_TURN = 51;
    SHARP_RIGHT_TURN = 52;
    SLIGHT_LEFT_TURN  = 53;
    SLIGHT_RIGHT_TURN = 54;
    LEFT_AT_FORK_TURN = 55;
    RIGHT_AT_FORK_TURN  = 56;
    U_TURN = 57;
    STRAIGHT_TURN = 58;
    ROUNDABOUT_EXIT_1_TURN = 59;
    ROUNDABOUT_EXIT_2_TURN = 60;
    ROUNDABOUT_EXIT_3_TURN = 61;
    ROUNDABOUT_EXIT_4_TURN = 62;
    ROUNDABOUT_EXIT_5_TURN = 63;
    ROUNDABOUT_EXIT_N_TURN = 64;
    NONE = 255;
}

/* this wrapper "NoConflict" is one (least disruptive) way to avoid this protoc error:

   > RouteEntities.proto:29:5: Note that enum values use C++ scoping rules, meaning that enum values are siblings of their type,
     not children of it.  Therefore, "CAR" must be unique within the global scope, not just within "ConstraintWaypointType".
*/
message NoConflict {
    enum RouteVehicle {
        NULL_ROUTEVEHICLE = 0;
        FREE = 1;
        CAR = 2;
        SMALL_TRUCK = 3;
        TRUCK = 4;
        FOOT = 5;
        HIKE = 6;
        BIKE = 7;
        MTB = 8;
        RACINGBIKE = 9;
    }
}

message ListOfRoute {
    repeated .Route items = 1;
}

message ListOfUserRoute {
    repeated .UserTopRoute items = 1;
}

message UserTopRoute {
    .Route route = 1;
    BoolValue watchEnabled = 2;
    BoolValue favorite = 3;
    Int64Value created = 4;
    Int64Value modified = 5;
}

message RoutePoint {
    FloatValue latitude = 1;
    FloatValue longitude = 2;
    FloatValue altitude = 3;
    FloatValue relativeDistance = 4;
    StringValue name = 5;
    .ConstraintWaypointType type = 6;
    StringValue description = 7;
}

message Route {
    StringValue id = 1;
    StringValue username = 2;
    StringValue description = 3;
    repeated string coauthors = 4;
    repeated int32 activities = 5;
    repeated .RouteSegment segments = 6;
    .RoutePoint startPoint = 7;
    .RoutePoint centerPoint = 8;
    .RoutePoint endPoint = 9;
    .RouteVisibility visibility = 10;
    Int64Value created = 11;
    FloatValue averageSpeed = 12;
    FloatValue totalDistance = 13; // Total distance in meters
    repeated StringValue comments = 14;
    Int64Value modified = 15;
    bool watchEnabled = 16;
    Int32Value watchSyncResponseCode = 17;
    .SyncState watchSyncState = 18;
    Int32Value watchRouteId = 19;
    Int64Value clientModifiedDate = 20;
    StringValue topRouteId = 21;
    StringValue topRouteVersion = 22;
    BoolValue turnWaypointsEnabled = 23;
    .Producer producer = 24;
    StringValue externalId = 25;
    Int64Value externalUpdateTime = 26;
    StringValue externalUrl = 27;
    repeated .RouteSegment segmentsGCJ02 = 28;  // Amap coordinate system
    .RoutePoint startPointGCJ02 = 29;
    .RoutePoint centerPointGCJ02 = 30;
    .RoutePoint endPointGCJ02 = 31;
    .RouteMetadata routeMetadata = 32;
}

message RouteSegment {
    .RoutePoint start = 1;
    .RoutePoint end = 2;
    int32 position = 3; //the position of this segment within the route.
    .NoConflict.RouteVehicle routing = 4;
    FloatValue ascent = 5;
    FloatValue descent = 6;
    FloatValue distance = 7;
    Int32Value duration = 8;
    repeated .RoutePoint routePoints = 9;
}

message Producer {
    StringValue id = 1;
    StringValue name = 2;
    StringValue iconUrl = 3;
}

// Part of route data that can be saved to Blob storage
message RouteBlobData {
    repeated .RouteSegment segments = 1;
}

message RouteMetadata {
    map<int32, .RouteActivityMetadata> routeActivityMetadata = 1;
}

message RouteActivityMetadata {
    FloatValue ascent = 1;
    FloatValue descent = 2;
    FloatValue distance = 3;
    Int32Value duration = 4;
    FloatValue popularity = 5;
    FloatValue averageSpeed = 6;
    .RouteImage routeImage = 7;
    map<string, .RouteLocalization> routeLocalizations = 8;
}

message RouteImage {
    repeated StringValue routeDefaultImage = 1;
}

message RouteLocalization {
    StringValue startAddress = 1;
    StringValue endAddress = 2;
    StringValue routeName = 3;
}
