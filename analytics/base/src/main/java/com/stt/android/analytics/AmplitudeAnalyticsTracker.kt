package com.stt.android.analytics

import android.app.Application

interface AmplitudeAnalyticsTracker {

    fun initialize(app: Application)

    fun setUUID(analyticsUUID: String?): Boolean

    fun getUUID(): String?

    fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        properties: AnalyticsProperties
    )

    fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        propertyName: String,
        propertyValue: Any
    )

    fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        properties: Map<String, Any>
    )

    fun trackEvent(
        @AnalyticsEvent.EventName
        event: String
    )

    fun trackUserProperty(name: String, value: Any)

    fun trackUserProperties(properties: AnalyticsProperties)

    fun trackUserProperties(properties: Map<String, Any>)

    fun logout()

    fun getDeviceId(): String?

    fun analyticsInitialised(): Boolean
}
