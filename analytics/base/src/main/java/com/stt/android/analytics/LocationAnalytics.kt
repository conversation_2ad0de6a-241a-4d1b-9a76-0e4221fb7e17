package com.stt.android.analytics

import android.content.Context
import android.location.Location
import com.stt.android.utils.isBackgroundLocationPermissionGranted
import java.util.concurrent.TimeUnit

class LocationAnalytics(
    val gpsStartedTime: Long,
    val context: Context
) {

    /**
     * Number of coordinates received from GPS.
     */
    private var coordinateCount: Int = 0
    private var latestCoordinateReceivedTime: Long? = null

    /**
     * Number of breaks, where no gps locations in received during GPS_BREAK_THRESHOLD_MILLISECONDS.
     */
    private var numberOfGpsBreaks: Int = 0
    private var longestDistanceBetweenCoordinates: Int = 0
    private var longestTimeBetweenLocations: Long = 0
    private var gpsPreviousLocation: Location? = null
    var stopped = false

    /**
     * Add location to location analytics. Method is not thread safe.
     */
    fun addLocation(location: Location) {
        coordinateCount++
        val previousLocation = gpsPreviousLocation
        previousLocation?.let {
            val distance = it.distanceTo(location)
            if (longestDistanceBetweenCoordinates < distance) {
                longestDistanceBetweenCoordinates = Math.round(distance)
            }
            val timeBetweenCoordinates = location.time - it.time
            if (longestTimeBetweenLocations < timeBetweenCoordinates) {
                longestTimeBetweenLocations = timeBetweenCoordinates
            }
            if (timeBetweenCoordinates > GPS_BREAK_THRESHOLD_MILLISECONDS) {
                numberOfGpsBreaks++
            }
        }

        latestCoordinateReceivedTime = System.currentTimeMillis()
        gpsPreviousLocation = location
    }

    private fun toSeconds(timeInMillis: Long): Long {
        return TimeUnit.MILLISECONDS.toSeconds(timeInMillis)
    }

    fun stopGpsAnalyticsProperties(): AnalyticsProperties {
        stopped = true

        val backgroundPermission =
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                context.isBackgroundLocationPermissionGranted()
            } else {
                true
            }

        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.GPS_DURATION,
                toSeconds(System.currentTimeMillis() - gpsStartedTime)
            )
            .put(AnalyticsEventProperty.GPS_COORDINATES, coordinateCount)
            .put(
                AnalyticsEventProperty.GPS_MAX_OFFSET_IN_TIME,
                toSeconds(longestTimeBetweenLocations)
            )
            .put(
                AnalyticsEventProperty.GPS_MAX_OFFSET_IN_METERS,
                longestDistanceBetweenCoordinates
            )
            .put(
                AnalyticsEventProperty.GPS_NUMBER_OFFSETS_ABOVE_THRESHOLD,
                numberOfGpsBreaks
            )
            .put(
                AnalyticsEventProperty.POWER_SAVE_MODE_ON,
                isPowerSaveMode(context)
            )
            .put(
                AnalyticsEventProperty.IGNORING_DOZE_MODE,
                isIgnoringBatteryOptimisations(context)
            )
            .putYesNo(AnalyticsEventProperty.GPS_BACK_GROUND_LOCATION_ENABLED, backgroundPermission)

        latestCoordinateReceivedTime?.let {
            properties.put(
                AnalyticsEventProperty.GPS_SECONDS_SINCE_LAST,
                toSeconds(System.currentTimeMillis() - it)
            )
        }
        return properties
    }

    fun startGpsAnalyticsProperties(): AnalyticsProperties {
        val analyticsProperties = AnalyticsProperties()
        analyticsProperties
            .put(
                AnalyticsEventProperty.POWER_SAVE_MODE_ON,
                isPowerSaveMode(context)
            )
            .put(
                AnalyticsEventProperty.IGNORING_DOZE_MODE,
                isIgnoringBatteryOptimisations(context)
            )
        return analyticsProperties
    }

    companion object {
        const val GPS_BREAK_THRESHOLD_MILLISECONDS = 5000
    }
}
