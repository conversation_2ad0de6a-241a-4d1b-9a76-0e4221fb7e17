package com.stt.android.domain.session

import com.stt.android.domain.session.facebook.FacebookSignInResult
import com.stt.android.domain.session.facebook.NewUserCredentials
import com.stt.android.domain.session.facebook.Sex
import com.stt.android.domain.session.status.AccountStatusDataSource
import com.stt.android.domain.user.DomainNewUser
import com.stt.android.exceptions.remote.STTError
import java.time.LocalDate
import javax.inject.Inject

/**
 * Use case for login with email
 */
class LoginWithEmailUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(usernameOrEmail: String, password: String): DomainUserSession =
        sessionDataSource.loginWithEmail(usernameOrEmail, password)
}

/**
 * Use case for login with phone number
 */
class LoginWithPhoneNumberUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(
        phoneNumber: String,
        phoneNumberVerificationToken: String
    ): DomainUserSession =
        sessionDataSource.loginWithPhoneNumber(phoneNumber, phoneNumberVerificationToken)
}

/**
 * Use case for login with Facebook
 */
class FacebookSignInUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(accessToken: String, termscode: String): FacebookSignInResult {
        return try {
            sessionDataSource.loginWithFacebook(accessToken, termscode)
                .let { FacebookSignInResult.Success(it) }
        } catch (e: Exception) {
            if (e is STTError.AccountDoesNotExist) {
                val newUserCredentials: NewUserCredentials = sessionDataSource.fetchFacebookInfo()
                FacebookSignInResult.SignupNeeded(newUserCredentials)
            } else {
                throw e
            }
        }
    }
}

/**
 * Use case for login with gmail
 */
class GmailSignInUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(authCode: String, termscode: String): DomainUserSession {
        return try {
            sessionDataSource.loginWithGmail(authCode, termscode)
        } catch (e: Exception) {
            throw e
        }
    }
}



/**
 * Use case for login with phone number
 */
class LoginWithAppleUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(
        code: String,
        email: String?,
        fullName: String?,
        termscode: String,
    ): DomainUserSession = sessionDataSource.loginWithApple(
        code = code,
        email = email,
        fullName = fullName,
        termscode = termscode,
    )
}

/**
 * Use case for email based sign-up
 */
class SignupWithEmailUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(
        fullName: String,
        password: String,
        email: String,
        verificationToken: String,
        birthday: LocalDate?,
        facebookAccessToken: String?,
        phoneNumber: String?,
        sex: Sex?,
        termscode: String,
    ): DomainNewUser =
        sessionDataSource.signupWithEmail(
            fullName = fullName,
            password = password,
            email = email,
            verificationToken = verificationToken,
            birthday = birthday,
            facebookAccessToken = facebookAccessToken,
            phoneNumber = phoneNumber,
            sex = sex,
            termscode = termscode,
        )
}

/**
 * Use case for phone number based sign-up
 */
class SignupWithPhoneNumberUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke(
        fullName: String,
        phoneNumber: String,
        phoneNumberVerificationToken: String,
        email: String?
    ): DomainNewUser =
        sessionDataSource.signupWithPhoneNumber(
            fullName,
            phoneNumber,
            phoneNumberVerificationToken,
            email
        )
}

/**
 * Use case for checking if an account exists with given email
 */
class FetchEmailStatusUseCase
@Inject constructor(
    private val accountStatusDataSource: AccountStatusDataSource
) {
    suspend operator fun invoke(usernameOrEmail: String): EmailStatus =
        accountStatusDataSource.fetchEmailStatus(usernameOrEmail)
}

/**
 * Use case for checking if an account exists with given username
 */
class FetchUsernameStatusUseCase
@Inject constructor(
    private val accountStatusDataSource: AccountStatusDataSource
) {
    suspend operator fun invoke(username: String): UsernameStatus =
        accountStatusDataSource.fetchUsernameStatus(username)
}

/**
 * Use case for checking if an account exists with given phone number
 */
class FetchPhoneNumberStatusUseCase
@Inject constructor(
    private val accountStatusDataSource: AccountStatusDataSource
) {
    suspend operator fun invoke(phoneNumber: String): PhoneNumberStatus =
        accountStatusDataSource.fetchPhoneNumberStatus(phoneNumber)
}

/**
 * Use case for logout
 */
class LogoutUseCase
@Inject constructor(
    private val sessionDataSource: SessionDataSource
) {
    suspend operator fun invoke() = sessionDataSource.logout()
}
