package com.stt.android.domain.session.phonenumberverification

import javax.inject.Inject

/**
 * Use case for verifying phone number
 * */
class VerifyPhoneNumberUseCase
@Inject constructor(
    private val phoneNumberVerificationDataSource: PhoneNumberVerificationDataSource
) {
    suspend operator fun invoke(phoneNumberVerificationParameter: PhoneNumberVerificationParameter): String =
        phoneNumberVerificationDataSource.verifyPhoneNumber(phoneNumberVerificationParameter)
}

data class PhoneNumberVerificationParameter(
    val phoneNumber: String,
    val pinCode: String
)
