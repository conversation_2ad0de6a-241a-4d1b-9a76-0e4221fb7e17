package com.stt.android.session.facebook

import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.CallSuper
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginBehavior
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.identity.AuthorizationRequest
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.Scopes
import com.google.android.gms.common.api.Scope
import com.stt.android.common.coroutines.LiveDataSuspendState
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.domain.session.LoginMethod
import com.stt.android.domain.session.facebook.NewUserCredentials
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.exceptions.remote.facebook.FacebookSignInException
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.session.R
import com.stt.android.session.SessionInitializerResult
import com.stt.android.session.getErrorMessageForException
import com.stt.android.session.gmail.GmaiLoginResult
import com.stt.android.session.signin.SignInActivity
import com.stt.android.session.signin.SignInOnboardingViewModel
import com.stt.android.session.signup.SignUp
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.util.concurrent.CancellationException
import javax.inject.Inject
import com.stt.android.R as BaseR

/**
 * This is means to be implemented only by [SplashIntroFragment]
 */
// Note: derived classes should use @AndroidEntryPoint annotation
abstract class FacebookAndGmailSignInFragment : ViewModelFragment2() {

    override val viewModel: SignInOnboardingViewModel by activityViewModels()

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    private var facebookCallbackManager: CallbackManager? = null

    private val signInClient: SignInClient by lazy {
        Identity.getSignInClient(requireContext())
    }

    private val facebookLoginCallback: FacebookCallback<LoginResult> =
        object : FacebookCallback<LoginResult> {
            override fun onSuccess(loginResult: LoginResult) {
                val token = loginResult.accessToken.token
                viewModel.signInWithFacebook(token, viewModel.termscode())
            }

            override fun onCancel() {
                Timber.d("User cancelled log-in process")
                handleFacebookSignInError(CancellationException())
            }

            override fun onError(error: FacebookException) {
                Timber.d(error, "Logging in Facebook SDK failed")
                handleFacebookSignInError(error)
            }
        }

    private val gmailSignLauncher =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                val authorizationResult = Identity.getAuthorizationClient(
                    requireActivity()
                ).getAuthorizationResultFromIntent(it.data)
                authorizationResult.serverAuthCode?.let { authCode ->
                    viewModel.signInWithGmail(
                        authCode,
                        authorizationResult.toGoogleSignInAccount()?.email ?: ""
                    )
                } ?: run {
                    Timber.w("serverAuthCode is null")
                }
            }
        }

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerFacebookCallbackManager()
    }

    @CallSuper
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.facebookSignInWrapperState.observeNotNull(
            viewLifecycleOwner,
            ::handleFacebookSignInState
        )
        viewModel.signUpState.observeNotNull(viewLifecycleOwner, ::handleSignupState)
        viewModel.gmailSignInState.observeNotNull(viewLifecycleOwner, ::handleGmailSignInState)
    }

    @CallSuper
    override fun onDestroy() {
        super.onDestroy()
        unregisterFacebookCallbackManager()
    }

    @Deprecated("Deprecated in Java")
    @CallSuper
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        facebookCallbackManager?.onActivityResult(requestCode, resultCode, data)
    }

    abstract fun getActionContinueWithMissingEmail(): NavDirections

    internal fun doFacebookLogin() {
        /*
        The permissions here can only be read permissions. If any publish permissions are
        included, the login attempt by the user will fail. The LoginButton can only be
        associated with either read permissions or publish permissions, but not both. Calling
        both setReadPermissions and setPublishPermissions on the same instance of LoginButton
        will result in an exception being thrown unless clearPermissions is called in between.
         */
        try {
            LoginManager.getInstance().run {
                logOut()
                logInWithReadPermissions(
                    this@FacebookAndGmailSignInFragment,
                    STTConstants.FB_READ_PERMISSION_LIST
                )
            }
        } catch (e: Exception) {
            handleFacebookSignInError(e)
        }
    }

    private fun handleFacebookSignInState(state: LiveDataSuspendState<FacebookSignIn.FlowResult>) {
        if (state is LiveDataSuspendState.Idle || state is LiveDataSuspendState.InProgress) {
            // should not handle
            return
        }
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                val successValue = state.successValue
                if (successValue is FacebookSignIn.FlowResult.Success) {
                    (activity as? SignInActivity)?.handleSessionInitSuccess(
                        successValue.sessionInitializerResult
                    )
                } else if (successValue is FacebookSignIn.FlowResult.SignupNeeded) {
                    signupUser(successValue.newUserCredentials)
                }
            } else if (state is LiveDataSuspendState.Failure) {
                // error at this point means login failed, we can't do much but show it
                handleFacebookSignInError(state.throwable)
            }
        }
    }

    private fun handleSignupState(state: LiveDataSuspendState<SessionInitializerResult>) {
        if (state is LiveDataSuspendState.Idle || state is LiveDataSuspendState.InProgress) {
            // should not handle
            return
        }
        state.ifNotHandled {
            if (state is LiveDataSuspendState.Success) {
                (activity as? SignInActivity)?.handleSessionInitSuccess(state.successValue)
            } else if (state is LiveDataSuspendState.Failure) {
                handleFacebookSignInError(state.throwable)
            }
        }
    }

    private fun signupUser(credentials: NewUserCredentials) {
        if (credentials.email.isNullOrBlank()) {
            findNavController().navigate(
                getActionContinueWithMissingEmail()
            )
        } else {
            with(viewModel) {
                setupWithNewUserCredentials(credentials)
                viewModel.signUp(SignUp.FlowParams(LoginMethod.FACEBOOK))
            }
        }
    }

    private fun registerFacebookCallbackManager() {
        if (facebookCallbackManager != null) return
        facebookCallbackManager = CallbackManager.Factory.create()
        LoginManager.getInstance().run {
            setLoginBehavior(LoginBehavior.NATIVE_WITH_FALLBACK)
            try {
                registerCallback(facebookCallbackManager, facebookLoginCallback)
            } catch (e: Exception) {
                Timber.w(e, "Error registering facebookCallbackManager")
            }
        }
    }

    private fun unregisterFacebookCallbackManager() {
        try {
            if (facebookCallbackManager != null) {
                LoginManager.getInstance().unregisterCallback(facebookCallbackManager)
            }
        } catch (e: Exception) {
            Timber.w(e, "Error unregistering facebookCallbackManager")
        } finally {
            facebookCallbackManager = null
        }
    }

    private fun handleFacebookSignInError(cause: Throwable) {
        val wrappedException = FacebookSignInException(cause.message, cause)
        Timber.w(wrappedException, "Error with facebook sign in")
        val message = if (cause is HttpException) {
            getErrorMessageForException(cause, requireContext())
        } else {
            getString(BaseR.string.error_generic_try_again)
        }
        Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_LONG).show()
    }

    /** Handles the result of the GIS pending-intent */
    private val signInLauncher = registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
        if (result.resultCode == RESULT_OK && result.data != null) {
            handleSignInResult(result.data!!)
        } else {
            // User cancelled or some error occurred
        }
    }

    internal fun doGmailSignIn() {

        val request = BeginSignInRequest.Builder()
            .setGoogleIdTokenRequestOptions(
                BeginSignInRequest.GoogleIdTokenRequestOptions.Builder()
                    .setSupported(true)
                    .setServerClientId(getString(R.string.default_web_client_id)) // web client-id
                    .setFilterByAuthorizedAccounts(false)                    // always show chooser
                    .build()
            )
            .setAutoSelectEnabled(false)    // show account chooser even if only 1 acc
            .build()

        signInClient.beginSignIn(request)
            .addOnSuccessListener { result ->
                val intentSenderRequest = IntentSenderRequest.Builder(result.pendingIntent).build()
                signInLauncher.launch(intentSenderRequest)
            }
            .addOnFailureListener {
                Timber.w(it, "authorization failed")
            }
    }

    /** Called when the user completes the Google flow */
    private fun handleSignInResult(data: Intent) {
        try {
            val credential   = signInClient.getSignInCredentialFromIntent(data)
            val idToken      = credential.googleIdToken          // <-- send to backend
            val email        = credential.id                     // primary e-mail
            val displayName  = credential.displayName            // may be null
            val avatarUri    = credential.profilePictureUri      // may be null

            if (idToken != null) {
                viewModel.signInWithGmail(
                            idToken,
                            email
                )
            } else {
                Timber.w("serverAuthCode is null")
            }
        } catch (t: Throwable) {
            Timber.w(t, "error handling Google sign-in")
        }
    }

    private fun handleGmailSignInState(gmaiLoginResult: GmaiLoginResult) {
        when (gmaiLoginResult) {
            is GmaiLoginResult.Success -> {
                (activity as? SignInActivity)?.handleSessionInitSuccess(gmaiLoginResult.sessionInitializerResult)
            }

            is GmaiLoginResult.Failure -> {
                val message = if (gmaiLoginResult.error is HttpException) {
                    getErrorMessageForException(gmaiLoginResult.error, requireContext())
                } else {
                    getString(com.stt.android.R.string.error_generic_try_again)
                }
                Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_LONG).show()
                Timber.w(gmaiLoginResult.error,"gmail login failed ${gmaiLoginResult.error.message}")
            }
        }
    }

}
