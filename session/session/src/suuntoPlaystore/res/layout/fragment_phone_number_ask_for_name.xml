<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onSignUpClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="showEmailInput"
            type="boolean" />

        <variable
            name="viewModel"
            type="com.stt.android.session.signin.SignInOnboardingViewModel" />

        <variable
            name="onActionDone"
            type="com.stt.android.utils.OnActionDone" />

        <import type="android.view.View" />

        <import type="android.view.inputmethod.EditorInfo" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:theme="@style/WhiteTheme">

        <include
            android:id="@+id/phone_ask_for_name_toolbar"
            layout="@layout/view_signup_toolbar"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:id="@+id/phone_ask_for_name_scrollview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phone_ask_for_name_toolbar"
            app:layout_constraintWidth_max="@dimen/content_max_width">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/phone_ask_for_name_scrollview_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    android:id="@+id/phone_ask_for_name_title"
                    layout="@layout/view_signup_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    app:titleText="@{@string/sign_up}"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/phone_ask_for_name_phone_number_input"
                    layout="@layout/view_phone_number"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    app:inputError="@{viewModel.signUpWithPhonePhoneNumberError}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/phone_ask_for_name_title"
                    app:nationalNumber="@{viewModel.nationalNumber}"
                    app:onPhoneRegionClicked="@{()->viewModel.onPhoneRegionClicked()}"
                    app:phoneRegion="@{viewModel.phoneRegion}"
                    app:phoneRegionEnabled="@{viewModel.phoneRegionSelectionEnabled}"
                    app:readOnly="@{true}"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/phone_ask_for_name_with_email_email_input"
                    layout="@layout/view_email_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="@{showEmailInput ? View.VISIBLE : View.GONE}"
                    app:inputError="@{viewModel.signUpWithPhoneEmailError}"
                    app:layout_constraintTop_toBottomOf="@id/phone_ask_for_name_phone_number_input"
                    app:readOnly="@{true}"
                    app:viewEmailInputHint="@{viewModel.signUpWithEmailHint}"
                    app:viewModel="@{viewModel}" />

                <include
                    android:id="@+id/phone_ask_for_name_full_name_input"
                    layout="@layout/view_full_name_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:inputError="@{viewModel.signUpWithPhoneRealNameError}"
                    app:layout_constraintTop_toBottomOf="@id/phone_ask_for_name_with_email_email_input"
                    app:imeActionLabel="@{@string/sign_up}"
                    app:imeOptions="@{EditorInfo.IME_ACTION_DONE}"
                    app:onActionDone="@{onActionDone}"
                    app:viewModel="@{viewModel}" />

                <Button
                    android:id="@+id/phone_ask_for_name_continue_button"
                    style="@style/Button.RoundedSecondaryAccent"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/height_button"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:enabled="@{!viewModel.phoneSignUpVerificationSmsSendingInProgress}"
                    android:onClick="@{onSignUpClicked}"
                    android:text="@string/sign_up"
                    android:transitionName="@string/transition_name_main_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/phone_ask_for_name_full_name_input" />

                <Space
                    android:id="@+id/phone_ask_for_name_stretch_area"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toTopOf="@+id/phone_ask_for_name_contact_support_button"
                    app:layout_constraintTop_toBottomOf="@id/phone_ask_for_name_continue_button"
                    app:layout_goneMarginTop="@dimen/size_spacing_large" />

                <include
                    android:id="@+id/phone_ask_for_name_terms_and_conditions"
                    layout="@layout/view_terms_and_conditions"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <include
                    android:id="@+id/phone_ask_for_name_contact_support_button"
                    layout="@layout/view_contact_support"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_xlarge"
                    android:layout_marginBottom="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toTopOf="@id/phone_ask_for_name_terms_and_conditions"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:viewModel="@{viewModel}" />

                <ProgressBar
                    android:id="@+id/phone_ask_for_name_progressbar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    app:layout_constraintBottom_toBottomOf="@id/phone_ask_for_name_full_name_input"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/phone_ask_for_name_full_name_input"
                    app:visible="@{viewModel.signUpInProgress}" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
