<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="phoneNumber"
            type="java.lang.String" />

        <variable
            name="onLoginOrSignUpClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="onEditPhoneNumberClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="doLogin"
            type="boolean" />

        <variable
            name="onActionDone"
            type="com.stt.android.utils.OnActionDone" />

        <variable
            name="viewModel"
            type="com.stt.android.session.signin.SignInOnboardingViewModel" />
    </data>

    <!-- The purpose of this CoordinatorLayout is to make sure <PERSON>nack<PERSON> anchors properly to this
    layout root, otherwise fitsSystemWindows=true attribute is ignored -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:theme="@style/WhiteTheme">

            <include
                android:id="@+id/phone_number_verification_code_toolbar"
                layout="@layout/view_signup_toolbar"
                app:layout_constraintTop_toTopOf="parent" />

            <ScrollView
                android:id="@+id/phone_number_verification_code_scroll_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/phone_number_verification_code_toolbar"
                app:layout_constraintWidth_max="@dimen/content_max_width">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/phone_number_verification_code_scroll_view_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textview_verify_code_title"
                        style="@style/Body.Larger.Bold.Local"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_medium"
                        android:layout_marginTop="@dimen/size_spacing_xlarge"
                        android:layout_marginEnd="@dimen/size_spacing_medium"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="4sp"
                        android:text="@string/phone_num_code_verification_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/textview_verify_code_sub_title"
                        style="@style/Body.Medium.Local"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_medium"
                        android:layout_marginTop="@dimen/size_spacing_medium"
                        android:layout_marginEnd="@dimen/size_spacing_medium"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="2sp"
                        android:text="@{@string/phone_num_code_verification_sub_title(phoneNumber)}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/textview_verify_code_title"
                        tools:text="@string/phone_num_code_verification_sub_title" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textinputlayout_code"
                        style="@style/TextInputWrapper"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_medium"
                        android:layout_marginTop="@dimen/size_spacing_xlarge"
                        android:layout_marginEnd="@dimen/size_spacing_medium"
                        android:hint="@string/phone_num_verification_code"
                        app:helperText="@string/phone_num_verification_code_hint"
                        app:inputError="@{doLogin ? viewModel.loginWithPhoneNumberCodeInputError : viewModel.signUpVerificationCodeError}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textview_verify_code_sub_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edittext_code"
                            style="@style/TextInputField"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="@{!viewModel.signUpWithPhoneNumberInProgress &amp;&amp; !viewModel.loginWithPhoneNumberInProgress}"
                            android:inputType="number"
                            android:text="@={viewModel.phoneNumberVerificationCode}"
                            android:theme="@style/TextInputField"
                            android:imeOptions="actionDone"
                            android:imeActionLabel="@string/verify_str"
                            android:singleLine="true"
                            app:onActionDone="@{onActionDone}"/>

                        <requestFocus />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/button_verify"
                        style="@style/Button.RoundedSecondaryAccent"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/height_button"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="@dimen/size_spacing_xlarge"
                        android:layout_marginTop="@dimen/size_spacing_large"
                        android:layout_marginEnd="@dimen/size_spacing_xlarge"
                        android:enabled="@{viewModel.canSignupWithVerificationCode}"
                        android:onClick="@{onLoginOrSignUpClicked}"
                        android:text="@string/verify_str"
                        android:transitionName="@string/transition_name_main_button"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textinputlayout_code"
                        tools:text="@string/verify_str" />

                    <TextView
                        android:id="@+id/textview_did_not_receive_code"
                        style="@style/Body.Small"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_xlarge"
                        android:layout_marginTop="@dimen/size_spacing_xlarge"
                        android:layout_marginEnd="@dimen/size_spacing_xlarge"
                        android:gravity="center"
                        android:text="@string/phone_num_did_not_receive_code"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/button_verify" />

                    <Button
                        android:id="@+id/button_resend_code"
                        style="@style/ButtonFlat.Medium"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_xlarge"
                        android:layout_marginTop="@dimen/size_spacing_xsmall"
                        android:layout_marginEnd="@dimen/size_spacing_xlarge"
                        android:enabled="@{viewModel.enableResendingVerificationCode}"
                        android:gravity="center"
                        android:onClick="@{() -> viewModel.resendVerificationCode()}"
                        android:text="@{viewModel.resendVerificationCodeTimer &gt; 0 ? @string/phone_num_resend_code_count_down(viewModel.resendVerificationCodeTimer) : @string/phone_num_resend_code}"
                        android:textAllCaps="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textview_did_not_receive_code" />

                    <Button
                        android:id="@+id/button_edit_phone_number"
                        style="@style/ButtonFlat.Medium"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/size_spacing_xlarge"
                        android:layout_marginEnd="@dimen/size_spacing_xlarge"
                        android:gravity="center"
                        android:onClick="@{onEditPhoneNumberClicked}"
                        android:text="@string/phone_num_edit_phone_number"
                        android:textAllCaps="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/button_resend_code" />

                    <include
                        android:id="@+id/ask_for_verification_code_contact_support_button"
                        layout="@layout/view_contact_support"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/button_edit_phone_number"
                        app:viewModel="@{viewModel}" />

                    <ProgressBar
                        android:id="@+id/progressbar_phone_number_spinner"
                        style="@style/Widget.AppCompat.ProgressBar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/size_spacing_small"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ask_for_verification_code_contact_support_button"
                        app:visible="@{viewModel.signUpWithPhoneNumberInProgress || viewModel.loginWithPhoneNumberInProgress}"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
