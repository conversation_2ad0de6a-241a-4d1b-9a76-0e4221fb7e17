<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onAgreeClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="onDenyClicked"
            type="android.view.View.OnClickListener" />

    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_large"
            android:paddingBottom="@dimen/size_spacing_large"
            tools:theme="@style/WhiteTheme"
            >

            <TextView
                android:id="@+id/title"
                style="@style/Body.XLarge.Bold"
                android:text="@string/tos_and_privacy_notice_title"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />

            <TextView
                android:id="@+id/content"
                style="@style/Body"
                app:layout_constraintTop_toBottomOf="@+id/title"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="@dimen/size_spacing_large"
                app:linkHtmlToCustomTab="@{@string/tos_and_privacy_notice_message}"
                tools:text="@string/tos_and_privacy_notice_message"
                />

            <Button
                android:id="@+id/agree_button"
                style="@style/Button.Primary"
                app:layout_constraintTop_toBottomOf="@+id/content"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginTop="@dimen/size_spacing_xlarge"
                android:text="@string/agree"
                android:onClick="@{onAgreeClicked}"
                />

            <Button
                android:id="@+id/deny_button"
                style="@style/ButtonFlat"
                app:layout_constraintTop_toTopOf="@+id/agree_button"
                app:layout_constraintBottom_toBottomOf="@+id/agree_button"
                app:layout_constraintEnd_toStartOf="@+id/agree_button"
                android:layout_marginEnd="@dimen/size_spacing_large"
                android:text="@string/tos_and_privacy_noticy_deny"
                android:onClick="@{onDenyClicked}"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</layout>
