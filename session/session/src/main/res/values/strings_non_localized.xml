<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="apple_sign_in_auth_url" translatable="false">https://appleid.apple.com/auth/authorize</string>
    <string name="apple_sign_in_redirect_uri" translatable="false">https://api.sports-tracker.com/apiserver/v1/authentication/apple/redirect</string>
    <string name="apple_sign_in_client_id" translatable="false">com.sports-tracker.iphone.signinwithapple</string>


    <!-- Google Web Client Id. Used for "Sign-in with Google". -->
    <!-- Will be overridden by the host app. Also because different for ST and SA -->
    <string name="default_web_client_id" translatable="false">TO_BE_REPLACE_BY_THE_APP</string>
</resources>
