package com.stt.android.session.phonenumberverification

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.session.phonenumberverification.PhoneNumberVerificationParameter
import com.stt.android.domain.session.phonenumberverification.RequestPhoneNumberVerificationSMSUseCase
import com.stt.android.domain.session.phonenumberverification.VerifyPhoneNumberUseCase
import com.stt.android.domain.user.settings.PhoneNumberTokenParameter
import com.stt.android.domain.user.settings.SaveUserPhoneNumberUseCase
import com.stt.android.exceptions.remote.STTError
import com.stt.android.session.InputError
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.net.UnknownHostException
import javax.inject.Inject
import com.stt.android.R as BaseR

data class PhoneNumberCodeVerificationViewState(
    val phoneNumber: String,
    val code: String?,
    val isVerifyEnabled: Boolean,
    val isLoadingVisible: Boolean,
    val verificationToken: String?,
    val isResendEnabled: Boolean,
    val countDownSecond: Long? = null,
    val useCaseType: PhoneNumberCodeVerificationUseCaseType? = null,
    val codeInputError: InputError = InputError.None
)

enum class PhoneNumberCodeVerificationUseCaseType {
    RESEND_CODE,
    VERIFY
}

@HiltViewModel
class PhoneNumberCodeVerificationViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val saveUserPhoneNumberUseCase: SaveUserPhoneNumberUseCase,
    private val requestPhoneNumberVerificationSMSUseCase: RequestPhoneNumberVerificationSMSUseCase,
    private val verifyPhoneNumberUseCase: VerifyPhoneNumberUseCase,
    private val countDownTimer: CountDownTimer,
    private val analyticsTracker: PhoneNumberCodeVerificationAnalyticsTracker,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : LoadingStateViewModel<PhoneNumberCodeVerificationViewState>(ioThread, mainThread) {

    private val phoneNumber = savedStateHandle.get(PHONE_NUMBER) ?: ""

    val code = MutableLiveData<String>()

    private val _onEditPhoneNumberClicked = SingleLiveEvent<Any>()
    val onEditPhoneNumberClicked: LiveData<Any>
        get() = _onEditPhoneNumberClicked

    private val _onResendCodeClicked = SingleLiveEvent<Any>()
    val onResendCodeClicked: LiveData<Any>
        get() = _onResendCodeClicked

    private val _onContactSupportClicked = SingleLiveEvent<Any>()
    val onContactSupportClicked: LiveData<Any>
        get() = _onContactSupportClicked

    init {
        notifyDataLoaded(
            PhoneNumberCodeVerificationViewState(
                phoneNumber = phoneNumber,
                code = null,
                isVerifyEnabled = false,
                isLoadingVisible = false,
                verificationToken = null,
                isResendEnabled = true
            )
        )
    }

    fun onVerifyClicked(code: String) {
        launch {
            notifyLoading(
                viewState.value?.data?.copy(
                    code = code,
                    isLoadingVisible = true,
                    isVerifyEnabled = false,
                    verificationToken = null,
                    useCaseType = PhoneNumberCodeVerificationUseCaseType.VERIFY
                )
            )
            runCatching {
                val verificationToken =
                    verifyPhoneNumberUseCase(PhoneNumberVerificationParameter(phoneNumber, code))
                saveUserPhoneNumberUseCase(
                    PhoneNumberTokenParameter(
                        verificationToken,
                        phoneNumber
                    )
                )
                verificationToken
            }.onSuccess {
                notifyDataLoaded(
                    viewState.value?.data?.copy(
                        isLoadingVisible = false,
                        isVerifyEnabled = true,
                        verificationToken = it,
                        codeInputError = InputError.None,
                        useCaseType = PhoneNumberCodeVerificationUseCaseType.VERIFY
                    )
                )
            }.onFailure {
                notifyError(
                    it,
                    viewState.value?.data?.copy(
                        isLoadingVisible = false,
                        isVerifyEnabled = true,
                        verificationToken = null,
                        codeInputError = if (it is STTError.InvalidPinCode) {
                            InputError(BaseR.string.error_1412)
                        } else {
                            InputError.None
                        },
                        useCaseType = PhoneNumberCodeVerificationUseCaseType.VERIFY
                    )
                )
                if (it is STTError.InvalidPinCode) {
                    analyticsTracker.trackVerificationError(AnalyticsPropertyValue.OnboardingVerificationProperty.VERIFICATION_NUMBER_NOT_MATCH)
                } else if (it is UnknownHostException) {
                    analyticsTracker.trackVerificationError(AnalyticsPropertyValue.OnboardingVerificationProperty.NETWORK_PROBLEM)
                }
            }
        }
    }

    fun onEditPhoneNumberClicked() {
        _onEditPhoneNumberClicked.call()
        analyticsTracker.trackEditPhoneNumber()
    }

    fun onContactSupportClicked() {
        _onContactSupportClicked.call()
    }

    fun onResendCodeClicked() {
        launch {
            analyticsTracker.trackResendCode()
            notifyLoading(
                viewState.value?.data?.copy(
                    isLoadingVisible = true,
                    isResendEnabled = false,
                    useCaseType = PhoneNumberCodeVerificationUseCaseType.RESEND_CODE
                )
            )
            runCatching {
                requestPhoneNumberVerificationSMSUseCase(phoneNumber)
            }.onSuccess {
                notifyDataLoaded(
                    viewState.value?.data?.copy(
                        isLoadingVisible = false,
                        useCaseType = PhoneNumberCodeVerificationUseCaseType.RESEND_CODE
                    )
                )
                countDownTimer.start(30)
                    .onEach { onTick(it) }
                    .onCompletion { onCountDownFinish() }
                    .buffer()
                    .collect()
            }.onFailure {
                notifyError(
                    it,
                    viewState.value?.data?.copy(
                        isResendEnabled = true,
                        isLoadingVisible = false,
                        useCaseType = PhoneNumberCodeVerificationUseCaseType.RESEND_CODE
                    )
                )
            }
        }
    }

    override fun retryLoading() {
        when (viewState.value?.data?.useCaseType) {
            PhoneNumberCodeVerificationUseCaseType.RESEND_CODE -> onResendCodeClicked()
            PhoneNumberCodeVerificationUseCaseType.VERIFY -> viewState.value?.data?.code?.let { onVerifyClicked(it) }
            null -> {
                // do nothing
            }
        }
    }

    internal fun onCodeChanged(code: String) {
        notifyDataLoaded(
            viewState.value?.data?.copy(
                code = code,
                isVerifyEnabled = code.length == VERIFICATION_CODE_LENGTH,
                verificationToken = null,
                useCaseType = null,
                codeInputError = InputError.None
            )
        )
    }

    companion object {
        const val PHONE_NUMBER = "phone_number"
        const val VERIFICATION_CODE_LENGTH = 6
    }

    private fun onTick(secondsUntilFinished: Int) {
        notifyDataLoaded(
            viewState.value?.data?.copy(
                isResendEnabled = false,
                countDownSecond = secondsUntilFinished.toLong(),
                useCaseType = null
            )
        )
    }

    private fun onCountDownFinish() {
        notifyDataLoaded(
            viewState.value?.data?.copy(
                isResendEnabled = true,
                countDownSecond = null
            )
        )
    }
}
