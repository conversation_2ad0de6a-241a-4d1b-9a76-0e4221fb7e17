package com.stt.android.session.di

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.session.SignInFlowHook
import com.stt.android.session.signin.SignInFlowHookImpl
import com.stt.android.session.splashintro.SplashIntroAnalyticsWorker
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(SingletonComponent::class)
abstract class SignInAppModule {

    @Binds
    abstract fun bindLoginFlowHook(hook: SignInFlowHookImpl): SignIn<PERSON>lowHook

    @Binds
    @IntoMap
    @WorkerKey(SplashIntroAnalyticsWorker::class)
    abstract fun bindSplashIntroAnalyticsWorker(factory: SplashIntroAnalyticsWorker.Factory): CoroutineWorkerAssistedFactory
}
