package com.stt.android.domain.trenddata

import com.stt.android.data.TimeUtils
import java.time.ZonedDateTime

/**
 * Represents trend data for a single day
 * @param serial Device serial number
 * @param timestamp UTC timestamp in milliseconds
 * @param energy in joules unit
 * @param steps step count
 * @param hr heartrate in Hz
 * @param hrMin minimum HR in this time window (usually 10 minutes) in Hz
 * @param hrMax maximum HR in this time window (usually 10 minutes) in Hz
 * @param spo2 spo2 percentage value 0..1
 * @param altitude in meters
 * @param timeISO8601 timestamp in local time
 * @param variantName eg:Monza
 */
data class WeChatTrendData(
    val serial: String,
    val timestamp: Long,
    val energy: Float,
    val steps: Int,
    val hr: Float?,
    val hrMin: Float?,
    val hrMax: Float?,
    val spo2: Float?,
    val altitude: Float?,
    val timeISO8601: ZonedDateTime = TimeUtils.epochToLocalZonedDateTime(timestamp),
    val variantName: String
) {
    val hasHr: Boolean
        get() = hr != null && hr > 0
}
