package com.stt.android.domain.routes

import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import io.reactivex.Completable
import io.reactivex.Scheduler
import kotlinx.coroutines.rx2.rxCompletable
import javax.inject.Inject

/**
 * Use case for deleting a [Route] instance.
 */
class DeleteRouteUseCase
@Inject constructor(
    private val routeRepository: RouteRepository,
    private val routeSyncWithWatchJobScheduler: RouteSyncWithWatchJobScheduler,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(ioThread, mainThread) {
    suspend fun deleteRoute(route: Route) {
        routeRepository.delete(route)
        routeSyncWithWatchJobScheduler.schedule(false)
    }

    fun deleteRouteRx(route: Route): Completable = rxCompletable {
        deleteRoute(route)
    }
}
