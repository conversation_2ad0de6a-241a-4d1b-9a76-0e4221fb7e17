package com.stt.android.domain.firstpairing

import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import io.reactivex.Completable
import io.reactivex.Scheduler
import io.reactivex.Single
import javax.inject.Inject

class FirstPairingInfoUseCase
@Inject constructor(
    private val repository: FirstPairingRepository,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseUseCase(ioThread, mainThread) {

    /**
     * Returns true if this pairing attempt is done by the user for the first time since
     *
     */
    fun isFirstTimePairingAttempt(): Single<Boolean> {
        return repository.isFirstTimePairingAttempt()
            .subscribeOn(scheduler)
    }

    /**
     * Set the flag for pairing attempt is done by the user for the first time since
     * the installation to true
     *
     */
    fun markFirstPairingAttemptAsDone(): Completable {
        return repository.markFirstPairingAttemptAsDone()
            .subscribeOn(scheduler)
    }

    /**
     * Returns true if this pairing attempt for a watch with onboarding is done by the user for
     * the first time since the installation
     */
    fun isOnboardingEverShown(deviceType: SuuntoDeviceType): Single<Boolean> {
        return repository.isOnboardingEverShown(deviceType)
            .subscribeOn(scheduler)
    }

    /**
     * Set the flag for pairing attempt for a watch with onboarding is done by the user for the
     * first time since the installation to true
     */
    fun markOnboardingShownAtLeastOnce(deviceType: SuuntoDeviceType): Completable {
        return repository.markOnboardingShownAtLeastOnce(deviceType)
            .subscribeOn(scheduler)
    }
}
