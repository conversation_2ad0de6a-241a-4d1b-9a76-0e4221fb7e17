package com.stt.android.domain.device

data class ConnectedWatchState constructor(
    val connectedWatchSyncState: ConnectedWatchSyncState,
    val connectedWatchConnectionState: ConnectedWatchConnectionState,
    val connectedWatchConnectionQuality: ConnectedWatchConnectionQuality,
    val deviceInfo: DeviceInfo?,
    val isBusy: <PERSON><PERSON><PERSON>,
    val isRegistered: <PERSON><PERSON><PERSON>,
    val isFWUpdateAvailable: <PERSON><PERSON><PERSON>,
    val isFWUpdateRequired: <PERSON><PERSON><PERSON>,
    val uploadProgressState: UploadProgressState
) {
    fun isNormalState(): Boolean {
        return !isBusy && connectedWatchConnectionState == ConnectedWatchConnectionState.CONNECTED &&
            connectedWatchSyncState.state == SyncState.NOT_SYNCING
    }

    fun isConnectionInstabilityDetected(): Boolean {
        return connectedWatchConnectionQuality == ConnectedWatchConnectionQuality.INSTABILITY_DETECTED_RECONNECTION_DELAYED
    }
}

enum class ConnectedWatchConnectionState(
    val value: Int
) {
    DISCONNECTED(0),
    CONNECTING(1),
    CONNECTED(2),
    RECONNECTING(3);

    companion object {
        fun from(findValue: Int): ConnectedWatchConnectionState = values().first { it.value == findValue }
    }
}

enum class SyncState(
    val value: Int
) {
    NOT_SYNCING(0),
    STARTING_SYNC(1),
    CHECKING_FOR_NEW_WORKOUTS(2),
    SYNCING_WORKOUTS(3),
    SYNCING_SETTINGS(4),
    SYNCING_ROUTES(5),
    SYNCING_POIS(6),
    CHECKING_FOR_NEW_GPS(7),
    SYNCING_GPS(8),
    SYNCING_TREND_DATA(9),
    SYNCING_SLEEP(10),
    SYNCING_SYSTEM_EVENTS_DATA(11),
    SYNCING_RECOVERY_DATA(12),
    SYNCING_SUUNTO_PLUS_GUIDES(13),
    SYNCING_WEATHER(14),
    SYNCING_ZONE_SENSE(15),
    SYNCING_BURIED_POINT(16),
    SYNCING_TRAINING_ZONE(99);

    companion object {
        fun from(findValue: Int): SyncState = values().first { it.value == findValue }
    }
}

enum class ConnectedWatchConnectionQuality(
    val value: Int
) {
    NORMAL(0),
    INSTABILITY_DETECTED_RECONNECTION_DELAYED(1);

    companion object {
        fun from(findValue: Int): ConnectedWatchConnectionQuality =
            values().first { it.value == findValue }
    }
}

data class ConnectedWatchSyncState(
    val state: SyncState,
    val step: Int,
    val stepCount: Int
)

data class DeviceInfo(
    val fwVersion: String,
    val model: String,
    val serial: String,
    val hwVersion: String,
    val manufacturer: String,
    val sku: String,
    val variantName: String,
    val capabilities: List<String>
)

data class AboutInfo(
    val serial: String,
    val version: String,
    val deviceInfoWear: DeviceInfoWear,
    val otaUpdateSupported: Boolean
)

data class UploadProgressState(
    val uploadInProgress: Boolean,
    val percentage: Int,
    val fileSizeInBytes: Long
) {
    val displayPercentage = "$percentage %"

    companion object {
        val EMPTY =
            UploadProgressState(
                uploadInProgress = false,
                percentage = 0,
                fileSizeInBytes = 0L
            )
    }
}
