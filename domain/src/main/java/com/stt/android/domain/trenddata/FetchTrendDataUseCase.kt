package com.stt.android.domain.trenddata

import com.stt.android.data.toEpochMilli
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

/**
 * Use case for fetching trend data
 */
class FetchTrendDataUseCase
@Inject constructor(
    private val trendDataRepository: TrendDataRepository,
) {
    /**
     * Get a [Flow] that emits list of [TrendData] entities between the specified timestamps.
     * New list is emitted whenever new entities are inserted to the data source.
     * @param fromTimestamp UTC timestamp in ms of the start of the day to query from
     * @param toTimestamp UTC timestamp in ms of the start of the day to query to
     * @return List of [TrendData] for the requested dates range if any exist
     */
    fun fetchTrendDataForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long,
        aggregated: Boolean
    ): Flow<List<TrendData>> = trendDataRepository.fetchTrendDataForDateRange(
        fromTimestamp = fromTimestamp,
        toTimestamp = toTimestamp,
        aggregated = aggregated,
    )

    fun fetchTrendData(
        from: LocalDate,
        to: LocalDate,
        aggregated: Boolean,
    ): Flow<List<TrendData>> {
        val zoneId = ZoneId.systemDefault()
        val fromTimestamp = from.atStartOfDay(zoneId).toEpochMilli()
        val toTimestamp = to.atStartOfDay(zoneId).toEpochMilli()
        return trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromTimestamp,
            toTimestamp = toTimestamp,
            aggregated = aggregated,
        )
    }

    fun fetchTrendDataExists(): Flow<Boolean> = trendDataRepository.fetchTrendDataExists()
}
