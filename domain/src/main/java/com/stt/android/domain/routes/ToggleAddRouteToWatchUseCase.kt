package com.stt.android.domain.routes

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Use case for toggling 'add to watch' switch for routes
 */
class ToggleAddRouteToWatchUseCase @Inject constructor(
    private val saveAndSyncRouteUseCase: SaveAndSyncRouteUseCase,
    private val getRouteUseCase: GetRouteUseCase,
) {
    suspend fun toggleAddToWatch(routeId: String, isAddToWatchSwitchedOn: Boolean) =
        withContext(Dispatchers.IO) {
            val route = getRouteUseCase.getRoute(routeId) ?: return@withContext
            if (route.watchEnabled == isAddToWatchSwitchedOn) {
                return@withContext
            }

            val syncState = if (!isAddToWatchSwitchedOn && route.watchRouteId == 0) {
                RouteWatchSyncState.IGNORED
            } else {
                RouteWatchSyncState.PENDING
            }

            saveAndSyncRouteUseCase.saveAndSyncRoute(
                route.copy(
                    watchSyncState = syncState,
                    watchEnabled = isAddToWatchSwitchedOn,
                    locallyChanged = true,
                )
            )
        }
}
