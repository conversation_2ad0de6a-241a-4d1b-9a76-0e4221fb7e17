package com.stt.android.domain.routes

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

class ExportKmlRouteUseCase @Inject constructor(
    private val repository: ExportRepository
) : CoroutineUseCase<String?, ExportKmlRouteUseCase.Params> {

    data class Params(val userName: String, val workoutKey: String)

    override suspend fun run(params: Params): String? {
        return repository.exportKmlRoute(params.userName, params.workoutKey)
    }
}
