package com.stt.android.domain.mapbox

import android.content.Context
import android.location.Address
import android.location.Geocoder
import android.os.Build
import androidx.annotation.RequiresApi
import com.stt.android.coroutines.runSuspendCatching
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.Locale
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class AndroidGeocodingAPI @Inject constructor(
    @ApplicationContext private val appContext: Context,
) : GeocodingAPI {
    private var geocoder = Geocoder(appContext, Locale.getDefault())

    override suspend fun fetchPlaceForLocation(
        latitude: Double,
        longitude: Double
    ): Place? = withContext(IO) {
        runSuspendCatching {
            fetchAddress(latitude, longitude)
                ?.takeIf { it.maxAddressLineIndex >= 0 }
                ?.let { address ->
                    Place(
                        fullAddress = address.getAddressLine(0),
                        city = address.locality,
                        province = address.adminArea,
                        country = address.countryName
                    )
                }
        }.getOrElse { e ->
            Timber.w(e, "Failed to get place for given location")
            null
        }
    }

    override fun initGeocoder(locale: Locale) {
        geocoder = Geocoder(appContext, locale)
    }

    private suspend fun fetchAddress(latitude: Double, longitude: Double): Address? =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            fetchPlaceT(latitude, longitude)?.lastOrNull()
        } else {
            fetchPlacePreT(latitude, longitude)?.lastOrNull()
        }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun fetchPlaceT(
        latitude: Double,
        longitude: Double,
    ): List<Address>? = suspendCancellableCoroutine { continuation ->
        val listener = WeakGeocodeListener(continuation)
        try {
            geocoder.getFromLocation(latitude, longitude, MAX_RESULTS, listener)
        } catch (e: Exception) {
            if (continuation.isActive) {
                continuation.resumeWithException(
                    RuntimeException("Geocoding failed unexpectedly: ${e.message}", e)
                )
            }
        }
        continuation.invokeOnCancellation {
            Timber.w("Geocoding coroutine was cancelled")
        }
    }

    @Suppress("DEPRECATION")
    private fun fetchPlacePreT(latitude: Double, longitude: Double): List<Address>? =
        geocoder.getFromLocation(latitude, longitude, MAX_RESULTS)

    companion object {
        // See https://stackoverflow.com/questions/52197005/geocoder-locale-language-always-return-english
        // We need to get 2 results and the last one is typically the correct one in the default locale
        private const val MAX_RESULTS = 2
    }
}

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
class WeakGeocodeListener(
    continuation: CancellableContinuation<List<Address>>
) : Geocoder.GeocodeListener {
    private val continuationRef = WeakReference(continuation)

    override fun onGeocode(addresses: List<Address>) {
        continuationRef.get()?.let { cont ->
            if (cont.isActive) {
                cont.resume(addresses)
            }
        }
    }

    override fun onError(errorMessage: String?) {
        continuationRef.get()?.let { cont ->
            if (cont.isActive) {
                cont.resumeWithException(
                    RuntimeException("Failed to fetch place: $errorMessage")
                )
            }
        }
    }
}
