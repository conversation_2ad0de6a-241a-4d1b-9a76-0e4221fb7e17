package com.stt.android.domain.routes

import com.stt.android.core.domain.workouts.CoreActivityType
import javax.inject.Inject

class DefaultActivitiesUseCase
@Inject constructor(
    private val routeRepository: RouteRepository,
) {

    // todo use suspend methods when this class is used by kotlin ViewModel

    fun getDefaultActivities(): List<CoreActivityType> =
        routeRepository.getDefaultActivities(
            listOf(CoreActivityType.CYCLING.id)
        ).mapNotNull { CoreActivityType.valueOf(it) }

    fun saveDefaultActivities(activities: List<CoreActivityType>) {
        routeRepository.saveDefaultActivities(activities.map { it.id })
    }
}
