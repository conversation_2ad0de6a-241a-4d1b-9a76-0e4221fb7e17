package com.stt.android.domain.activitydata.goals

import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class FetchBedtimeEndUseCaseTest {
    private lateinit var repository: ActivityDataGoalRepository
    private lateinit var useCase: FetchBedtimeEndUseCase

    @Before
    fun setUp() {
        repository = mock()
        useCase = FetchBedtimeEndUseCase(repository)
    }

    @Test
    fun `should access the repository when getting bedtime end`() = runTest {
        // prepare
        whenever(repository.fetchBedtimeEnd()) doReturn flowOf(10000)
        // verify
        useCase()
            .collect()
        verify(repository).fetchBedtimeEnd()
    }

    @Test(expected = Exception::class)
    fun `should fail if repository fails inside and return an error`() = runTest {
        // prepare
        whenever(repository.fetchBedtimeEnd()) doReturn flow { throw Exception() }
        // verify
        useCase()
            .collect()
        verify(repository).fetchBedtimeEnd()
    }
}
