package com.stt.android.remote.interceptors

import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Network interceptor which injects [appKey] into the query string
 * for authentication with MovesCount server
 */
open class McAppAuthInterceptor(private val appKey: String) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        return chain.proceed(
            chain.request()
                .newBuilder()
                .url(buildUrl(chain))
                .build()
        )
    }

    protected fun buildUrl(chain: Interceptor.Chain): HttpUrl {
        return chain.request()
            .url
            .newBuilder()
            .addQueryParameter("appkey", appKey)
            .build()
    }
}
