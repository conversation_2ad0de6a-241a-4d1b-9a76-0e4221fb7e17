package com.stt.android.remote.weather

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherResponse(
    @Json(name = "list") val list: List<OpenWeatherMapWeatherConditions>,
    @J<PERSON>(name = "city") val city: OpenWeatherCityInfo?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherConditions(
    @J<PERSON>(name = "weather") val weather: List<OpenWeatherMapWeatherConditionsWeather>?,
    @<PERSON>son(name = "main") val main: OpenWeatherMapWeatherConditionsMain?,
    @<PERSON>son(name = "wind") val wind: OpenWeatherMapWeatherConditionsWind?,
    @Json(name = "clouds") val clouds: OpenWeatherMapWeatherConditionsClouds?,
    @<PERSON><PERSON>(name = "rain") val rain: OpenWeatherMapWeatherPrecipitation?,
    @Json(name = "snow") val snow: OpenWeatherMapWeatherPrecipitation?,
    @Json(name = "dt") val unixTime: Long?,
    @<PERSON><PERSON>(name = "visibility") val visibility: Int?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherConditionsWeather(
    @Json(name = "icon") val iconId: String?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherConditionsMain(
    @Json(name = "temp") val temperature: Float?, // Kelvin
    @Json(name = "temp_min") val tempMin: Float?, // Kelvin
    @Json(name = "temp_max") val tempMax: Float?, // Kelvin
    @Json(name = "pressure") val pressure: Float?, // hPA
    @Json(name = "humidity") val humidity: Int?, // percentage
    @Json(name = "sea_level") val seaLevelPressure: Float?, // hPa
    @Json(name = "grnd_level") val groundLevelPressure: Float?, // hPa
    @Json(name = "feels_like") val feelsLike: Float? // Kelvin
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherConditionsWind(
    @Json(name = "speed") val speed: Float?, // m/s
    @Json(name = "deg") val direction: Float?, // Degrees
    @Json(name = "gust") val gust: Float? // m/s
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherConditionsClouds(
    @Json(name = "all") val cloudiness: Int?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherPrecipitation(
    @Json(name = "1h") val volume1h: Float?,
    @Json(name = "3h") val volume3h: Float?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapHistoricalWeatherResponse(
    @Json(name = "lat") val lat: Float?,
    @Json(name = "lon") val lon: Float?,
    @Json(name = "timezone") val timezone: String?,
    @Json(name = "timezone_offset") val timezoneOffset: Int?,
    @Json(name = "current") val current: OpenWeatherMapHistoricalWeatherInfo?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapHistoricalWeatherInfo(
    @Json(name = "dt") val timestamp: Long?,
    @Json(name = "sunrise") val sunrise: Long?,
    @Json(name = "sunset") val sunset: Long?,
    @Json(name = "temp") val temp: Float?, // Kelvin
    @Json(name = "feels_like") val feelsLike: Float?, // Kelvin
    @Json(name = "pressure") val pressure: Float?, // hPA
    @Json(name = "humidity") val humidity: Int?, // Percentage
    @Json(name = "dew_point") val dew_point: Float?, // Kelvin
    @Json(name = "clouds") val clouds: Int?, // Percentage
    @Json(name = "visibility") val visibility: Int?, // meters
    @Json(name = "wind_speed") val windSpeed: Float?, // m/s
    @Json(name = "wind_deg") val windDeg: Float?, // degrees
    @Json(name = "weather") val weatherDescription: List<OpenWeatherMapHistoricalWeatherDescription>,
    @Json(name = "rain") val rain: OpenWeatherMapWeatherPrecipitation?,
    @Json(name = "snow") val snow: OpenWeatherMapWeatherPrecipitation?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapHistoricalWeatherDescription(
    @Json(name = "id") val id: Int?, // https://openweathermap.org/weather-conditions#Weather-Condition-Codes-2
    @Json(name = "main") val main: String?,
    @Json(name = "description") val description: String?,
    @Json(name = "icon") val icon: String?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherCityInfo(
    @Json(name = "id") val id: String?,
    @Json(name = "name") val name: String?,
    @Json(name = "coord") val coord: OpenWeatherCoordinate?,
    @Json(name = "country") val country: String?,
    @Json(name = "sunrise") val sunrise: Int?,
    @Json(name = "sunset") val sunset: Int?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherCoordinate(
    @Json(name = "lat") val lat: Double?,
    @Json(name = "lon") val lon: Double?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapWeatherUvi(
    @Json(name = "value") val uvi: Float
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapAqiResponse(
    @Json(name = "list") val aqiList: List<OpenWeatherMapAqiConditions>?
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapAqiConditions(
    @Json(name = "main") val main: OpenWeatherMapAqiMain
)

@JsonClass(generateAdapter = true)
data class OpenWeatherMapAqiMain(
    @Json(name = "aqi") val aqi: Int
)
