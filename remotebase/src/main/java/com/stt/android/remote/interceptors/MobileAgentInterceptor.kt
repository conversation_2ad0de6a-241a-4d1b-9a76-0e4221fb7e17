package com.stt.android.remote.interceptors

import okhttp3.Interceptor
import okhttp3.Response
import java.util.Locale

/**
 * Adds User-Agent and Accept-Language headers
 */
class MobileAgentInterceptor(private val userAgent: String) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val language = with(Locale.getDefault()) {
            when {
                // Simplified Chinese
                language == "zh" && country == "CN" -> "zh-cn"
                // Traditional Chinese for other Chinese-speaking regions
                language == "zh" -> "zh-tw"
                else -> this.language
            }
        }
        val requestWithUserAgent = chain.request()
            .newBuilder()
            .header("User-Agent", userAgent)
            .header("Accept-Language", language)
            .build()
        return chain.proceed(requestWithUserAgent)
    }
}
